{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/badge/testing/badge-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ComponentHarness, HarnessPredicate} from '@angular/cdk/testing';\nimport {MatBadgePosition, MatBadgeSize} from '../badge';\nimport {BadgeHarnessFilters} from './badge-harness-filters';\n\n/** Harness for interacting with a standard Material badge in tests. */\nexport class MatBadgeHarness extends ComponentHarness {\n  static hostSelector = '.mat-badge';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a badge with specific attributes.\n   * @param options Options for narrowing the search:\n   *   - `text` finds a badge host with a particular text.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: BadgeHarnessFilters = {}): HarnessPredicate<MatBadgeHarness> {\n    return new HarnessPredicate(MatBadgeHarness, options).addOption(\n      'text',\n      options.text,\n      (harness, text) => HarnessPredicate.stringMatches(harness.getText(), text),\n    );\n  }\n\n  private _badgeElement = this.locatorFor('.mat-badge-content');\n\n  /** Gets a promise for the badge text. */\n  async getText(): Promise<string> {\n    return (await this._badgeElement()).text();\n  }\n\n  /** Gets whether the badge is overlapping the content. */\n  async isOverlapping(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-badge-overlap');\n  }\n\n  /** Gets the position of the badge. */\n  async getPosition(): Promise<MatBadgePosition> {\n    const host = await this.host();\n    let result = '';\n\n    if (await host.hasClass('mat-badge-above')) {\n      result += 'above';\n    } else if (await host.hasClass('mat-badge-below')) {\n      result += 'below';\n    }\n\n    if (await host.hasClass('mat-badge-before')) {\n      result += ' before';\n    } else if (await host.hasClass('mat-badge-after')) {\n      result += ' after';\n    }\n\n    return result.trim() as MatBadgePosition;\n  }\n\n  /** Gets the size of the badge. */\n  async getSize(): Promise<MatBadgeSize> {\n    const host = await this.host();\n\n    if (await host.hasClass('mat-badge-small')) {\n      return 'small';\n    } else if (await host.hasClass('mat-badge-large')) {\n      return 'large';\n    }\n\n    return 'medium';\n  }\n\n  /** Gets whether the badge is hidden. */\n  async isHidden(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-badge-hidden');\n  }\n\n  /** Gets whether the badge is disabled. */\n  async isDisabled(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-badge-disabled');\n  }\n}\n"], "names": [], "mappings": ";;AAYA;AACM,MAAO,eAAgB,SAAQ,gBAAgB,CAAA;AACnD,IAAA,OAAO,YAAY,GAAG,YAAY;AAElC;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAA+B,EAAE,EAAA;AAC3C,QAAA,OAAO,IAAI,gBAAgB,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC,SAAS,CAC7D,MAAM,EACN,OAAO,CAAC,IAAI,EACZ,CAAC,OAAO,EAAE,IAAI,KAAK,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,CAC3E;;AAGK,IAAA,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC;;AAG7D,IAAA,MAAM,OAAO,GAAA;QACX,OAAO,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,EAAE,IAAI,EAAE;;;AAI5C,IAAA,MAAM,aAAa,GAAA;AACjB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,mBAAmB,CAAC;;;AAI1D,IAAA,MAAM,WAAW,GAAA;AACf,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;QAC9B,IAAI,MAAM,GAAG,EAAE;QAEf,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;YAC1C,MAAM,IAAI,OAAO;;aACZ,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;YACjD,MAAM,IAAI,OAAO;;QAGnB,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;YAC3C,MAAM,IAAI,SAAS;;aACd,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;YACjD,MAAM,IAAI,QAAQ;;AAGpB,QAAA,OAAO,MAAM,CAAC,IAAI,EAAsB;;;AAI1C,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;QAE9B,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;AAC1C,YAAA,OAAO,OAAO;;aACT,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;AACjD,YAAA,OAAO,OAAO;;AAGhB,QAAA,OAAO,QAAQ;;;AAIjB,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,kBAAkB,CAAC;;;AAIzD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,oBAAoB,CAAC;;;;;;"}