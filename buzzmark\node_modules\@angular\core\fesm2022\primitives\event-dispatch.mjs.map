{"version": 3, "file": "event-dispatch.mjs", "sources": ["../../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/core/primitives/event-dispatch/src/attribute.ts", "../../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/core/primitives/event-dispatch/src/property.ts", "../../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/core/primitives/event-dispatch/src/cache.ts", "../../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/core/primitives/event-dispatch/src/event_type.ts", "../../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/core/primitives/event-dispatch/src/event.ts", "../../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/core/primitives/event-dispatch/src/event_contract_container.ts", "../../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/core/primitives/event-dispatch/src/char.ts", "../../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/core/primitives/event-dispatch/src/event_info.ts", "../../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/core/primitives/event-dispatch/src/action_resolver.ts", "../../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/core/primitives/event-dispatch/src/restriction.ts", "../../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/core/primitives/event-dispatch/src/dispatcher.ts", "../../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/core/primitives/event-dispatch/src/event_dispatcher.ts", "../../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/core/primitives/event-dispatch/src/earlyeventcontract.ts", "../../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/core/primitives/event-dispatch/src/event_contract_defines.ts", "../../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/core/primitives/event-dispatch/src/eventcontract.ts", "../../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/core/primitives/event-dispatch/src/bootstrap_app_scoped.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nexport const Attribute = {\n  /**\n   * The jsaction attribute defines a mapping of a DOM event to a\n   * generic event (aka jsaction), to which the actual event handlers\n   * that implement the behavior of the application are bound. The\n   * value is a semicolon separated list of colon separated pairs of\n   * an optional DOM event name and a jsaction name. If the optional\n   * DOM event name is omitted, 'click' is assumed. The jsaction names\n   * are dot separated pairs of a namespace and a simple jsaction\n   * name.\n   *\n   * See grammar in README.md for expected syntax in the attribute value.\n   */\n  JSACTION: 'jsaction' as const,\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/** All properties that are used by jsaction. */\nexport const Property = {\n  /**\n   * The parsed value of the jsaction attribute is stored in this\n   * property on the DOM node. The parsed value is an Object. The\n   * property names of the object are the events; the values are the\n   * names of the actions. This property is attached even on nodes\n   * that don't have a jsaction attribute as an optimization, because\n   * property lookup is faster than attribute access.\n   */\n  JSACTION: '__jsaction' as const,\n  /**\n   * The owner property references an a logical owner for a DOM node. JSAction\n   * will follow this reference instead of parentNode when traversing the DOM\n   * to find jsaction attributes. This allows overlaying a logical structure\n   * over a document where the DOM structure can't reflect that structure.\n   */\n  OWNER: '__owner' as const,\n};\n\ndeclare global {\n  interface Node {\n    [Property.JSACTION]?: {[key: string]: string | undefined};\n    [Property.OWNER]?: ParentNode;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Property} from './property';\n\n/**\n * Map from jsaction annotation to a parsed map from event name to action name.\n */\nconst parseCache: {[key: string]: {[key: string]: string | undefined}} = {};\n\n/**\n * Reads the jsaction parser cache from the given DOM Element.\n */\nexport function get(element: Element): {[key: string]: string | undefined} | undefined {\n  return element[Property.JSACTION];\n}\n\n/**\n * Reads the jsaction parser cache for the given DOM element. If no cache is yet present,\n * creates an empty one.\n */\nexport function getDefaulted(element: Element): {[key: string]: string | undefined} {\n  const cache = get(element) ?? {};\n  set(element, cache);\n  return cache;\n}\n\n/**\n * Writes the jsaction parser cache to the given DOM Element.\n */\nexport function set(element: Element, actionMap: {[key: string]: string | undefined}) {\n  element[Property.JSACTION] = actionMap;\n}\n\n/**\n * Looks up the parsed action map from the source jsaction attribute value.\n *\n * @param text Unparsed jsaction attribute value.\n * @return Parsed jsaction attribute value, if already present in the cache.\n */\nexport function getParsed(text: string): {[key: string]: string | undefined} | undefined {\n  return parseCache[text];\n}\n\n/**\n * Inserts the parse result for the given source jsaction value into the cache.\n *\n * @param text Unparsed jsaction attribute value.\n * @param parsed Attribute value parsed into the action map.\n */\nexport function setParsed(text: string, parsed: {[key: string]: string | undefined}) {\n  parseCache[text] = parsed;\n}\n\n/**\n * Clears the jsaction parser cache from the given DOM Element.\n *\n * @param element .\n */\nexport function clear(element: Element) {\n  if (Property.JSACTION in element) {\n    delete element[Property.JSACTION];\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/*\n * Names of events that are special to jsaction. These are not all\n * event types that are legal to use in either HTML or the addEvent()\n * API, but these are the ones that are treated specially. All other\n * DOM events can be used in either addEvent() or in the value of the\n * jsaction attribute. Beware of browser specific events or events\n * that don't bubble though: If they are not mentioned here, then\n * event contract doesn't work around their peculiarities.\n */\nexport const EventType = {\n  /**\n   * Mouse middle click, introduced in Chrome 55 and not yet supported on\n   * other browsers.\n   */\n  AUXCLICK: 'auxclick',\n\n  /**\n   * The change event fired by browsers when the `value` attribute of input,\n   * select, and textarea elements are changed.\n   */\n  CHANGE: 'change',\n\n  /**\n   * The click event. In addEvent() refers to all click events, in the\n   * jsaction attribute it refers to the unmodified click and Enter/Space\n   * keypress events.  In the latter case, a jsaction click will be triggered,\n   * for accessibility reasons.  See clickmod and clickonly, below.\n   */\n  CLICK: 'click',\n\n  /**\n   * Specifies the jsaction for a modified click event (i.e. a mouse\n   * click with the modifier key Cmd/Ctrl pressed). This event isn't\n   * separately enabled in addEvent(), because in the DOM, it's just a\n   * click event.\n   */\n  CLICKMOD: 'clickmod',\n\n  /**\n   * Specifies the jsaction for a click-only event.  Click-only doesn't take\n   * into account the case where an element with focus receives an Enter/Space\n   * keypress.  This event isn't separately enabled in addEvent().\n   */\n  CLICKONLY: 'clickonly',\n\n  /**\n   * The dblclick event.\n   */\n  DBLCLICK: 'dblclick',\n\n  /**\n   * Focus doesn't bubble, but you can use it in addEvent() and\n   * jsaction anyway. EventContract does the right thing under the\n   * hood.\n   */\n  FOCUS: 'focus',\n\n  /**\n   * This event only exists in IE. For addEvent() and jsaction, use\n   * focus instead; EventContract does the right thing even though\n   * focus doesn't bubble.\n   */\n  FOCUSIN: 'focusin',\n\n  /**\n   * Analog to focus.\n   */\n  BLUR: 'blur',\n\n  /**\n   * Analog to focusin.\n   */\n  FOCUSOUT: 'focusout',\n\n  /**\n   * Submit doesn't bubble, so it cannot be used with event\n   * contract. However, the browser helpfully fires a click event on\n   * the submit button of a form (even if the form is not submitted by\n   * a click on the submit button). So you should handle click on the\n   * submit button instead.\n   */\n  SUBMIT: 'submit',\n\n  /**\n   * The keydown event. In addEvent() and non-click jsaction it represents the\n   * regular DOM keydown event. It represents click actions in non-Gecko\n   * browsers.\n   */\n  KEYDOWN: 'keydown',\n\n  /**\n   * The keypress event. In addEvent() and non-click jsaction it represents the\n   * regular DOM keypress event. It represents click actions in Gecko browsers.\n   */\n  KEYPRESS: 'keypress',\n\n  /**\n   * The keyup event. In addEvent() and non-click jsaction it represents the\n   * regular DOM keyup event. It represents click actions in non-Gecko\n   * browsers.\n   */\n  KEYUP: 'keyup',\n\n  /**\n   * The mouseup event. Can either be used directly or used implicitly to\n   * capture mouseup events. In addEvent(), it represents a regular DOM\n   * mouseup event.\n   */\n  MOUSEUP: 'mouseup',\n\n  /**\n   * The mousedown event. Can either be used directly or used implicitly to\n   * capture mouseenter events. In addEvent(), it represents a regular DOM\n   * mouseover event.\n   */\n  MOUSEDOWN: 'mousedown',\n\n  /**\n   * The mouseover event. Can either be used directly or used implicitly to\n   * capture mouseenter events. In addEvent(), it represents a regular DOM\n   * mouseover event.\n   */\n  MOUSEOVER: 'mouseover',\n\n  /**\n   * The mouseout event. Can either be used directly or used implicitly to\n   * capture mouseover events. In addEvent(), it represents a regular DOM\n   * mouseout event.\n   */\n  MOUSEOUT: 'mouseout',\n\n  /**\n   * The mouseenter event. Does not bubble and fires individually on each\n   * element being entered within a DOM tree.\n   */\n  MOUSEENTER: 'mouseenter',\n\n  /**\n   * The mouseleave event. Does not bubble and fires individually on each\n   * element being entered within a DOM tree.\n   */\n  MOUSELEAVE: 'mouseleave',\n\n  /**\n   * The mousemove event.\n   */\n  MOUSEMOVE: 'mousemove',\n\n  /**\n   * The pointerup event. Can either be used directly or used implicitly to\n   * capture pointerup events. In addEvent(), it represents a regular DOM\n   * pointerup event.\n   */\n  POINTERUP: 'pointerup',\n\n  /**\n   * The pointerdown event. Can either be used directly or used implicitly to\n   * capture pointerenter events. In addEvent(), it represents a regular DOM\n   * mouseover event.\n   */\n  POINTERDOWN: 'pointerdown',\n\n  /**\n   * The pointerover event. Can either be used directly or used implicitly to\n   * capture pointerenter events. In addEvent(), it represents a regular DOM\n   * pointerover event.\n   */\n  POINTEROVER: 'pointerover',\n\n  /**\n   * The pointerout event. Can either be used directly or used implicitly to\n   * capture pointerover events. In addEvent(), it represents a regular DOM\n   * pointerout event.\n   */\n  POINTEROUT: 'pointerout',\n\n  /**\n   * The pointerenter event. Does not bubble and fires individually on each\n   * element being entered within a DOM tree.\n   */\n  POINTERENTER: 'pointerenter',\n\n  /**\n   * The pointerleave event. Does not bubble and fires individually on each\n   * element being entered within a DOM tree.\n   */\n  POINTERLEAVE: 'pointerleave',\n\n  /**\n   * The pointermove event.\n   */\n  POINTERMOVE: 'pointermove',\n\n  /**\n   * The pointercancel event.\n   */\n  POINTERCANCEL: 'pointercancel',\n\n  /**\n   * The gotpointercapture event is fired when\n   * Element.setPointerCapture(pointerId) is called on a mouse input, or\n   * implicitly when a touch input begins.\n   */\n  GOTPOINTERCAPTURE: 'gotpointercapture',\n\n  /**\n   * The lostpointercapture event is fired when\n   * Element.releasePointerCapture(pointerId) is called, or implicitly after a\n   * touch input ends.\n   */\n  LOSTPOINTERCAPTURE: 'lostpointercapture',\n\n  /**\n   * The error event. The error event doesn't bubble, but you can use it in\n   * addEvent() and jsaction anyway. EventContract does the right thing under\n   * the hood (except in IE8 which does not use error events).\n   */\n  ERROR: 'error',\n\n  /**\n   * The load event. The load event doesn't bubble, but you can use it in\n   * addEvent() and jsaction anyway. EventContract does the right thing\n   * under the hood.\n   */\n  LOAD: 'load',\n\n  /**\n   * The unload event.\n   */\n  UNLOAD: 'unload',\n\n  /**\n   * The touchstart event. Bubbles, will only ever fire in browsers with\n   * touch support.\n   */\n  TOUCHSTART: 'touchstart',\n\n  /**\n   * The touchend event. Bubbles, will only ever fire in browsers with\n   * touch support.\n   */\n  TOUCHEND: 'touchend',\n\n  /**\n   * The touchmove event. Bubbles, will only ever fire in browsers with\n   * touch support.\n   */\n  TOUCHMOVE: 'touchmove',\n\n  /**\n   * The input event.\n   */\n  INPUT: 'input',\n\n  /**\n   * The scroll event.\n   */\n  SCROLL: 'scroll',\n\n  /**\n   * The toggle event. The toggle event doesn't bubble, but you can use it in\n   * addEvent() and jsaction anyway. EventContract does the right thing\n   * under the hood.\n   */\n  TOGGLE: 'toggle',\n\n  /**\n   * A custom event. The actual custom event type is declared as the 'type'\n   * field in the event details. Supported in Firefox 6+, IE 9+, and all Chrome\n   * versions.\n   *\n   * This is an internal name. Users should use jsaction's fireCustomEvent to\n   * fire custom events instead of relying on this type to create them.\n   */\n  CUSTOM: '_custom',\n};\n\n/** All event types that do not bubble or capture and need a polyfill. */\nexport const MOUSE_SPECIAL_EVENT_TYPES = [\n  EventType.MOUSEENTER,\n  EventType.MOUSELEAVE,\n  'pointerenter',\n  'pointerleave',\n];\n\n/** All event types that are registered in the bubble phase. */\nexport const BUBBLE_EVENT_TYPES = [\n  EventType.CLICK,\n  EventType.DBLCLICK,\n  EventType.FOCUSIN,\n  EventType.FOCUSOUT,\n  EventType.KEYDOWN,\n  EventType.KEYUP,\n  EventType.KEYPRESS,\n  EventType.MOUSEOVER,\n  EventType.MOUSEOUT,\n  EventType.SUBMIT,\n  EventType.TOUCHSTART,\n  EventType.TOUCHEND,\n  EventType.TOUCHMOVE,\n  'touchcancel',\n\n  'auxclick',\n  'change',\n  'compositionstart',\n  'compositionupdate',\n  'compositionend',\n  'beforeinput',\n  'input',\n  'select',\n\n  'copy',\n  'cut',\n  'paste',\n  'mousedown',\n  'mouseup',\n  'wheel',\n  'contextmenu',\n\n  'dragover',\n  'dragenter',\n  'dragleave',\n  'drop',\n  'dragstart',\n  'dragend',\n\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointercancel',\n  'pointerover',\n  'pointerout',\n  'gotpointercapture',\n  'lostpointercapture',\n\n  // Video events.\n  'ended',\n  'loadedmetadata',\n\n  // Page visibility events.\n  'pagehide',\n  'pageshow',\n  'visibilitychange',\n\n  // Content visibility events.\n  'beforematch',\n];\n\n/** All event types that are registered in the capture phase. */\nexport const CAPTURE_EVENT_TYPES = [\n  EventType.FOCUS,\n  EventType.BLUR,\n  EventType.ERROR,\n  EventType.LOAD,\n  EventType.TOGGLE,\n];\n\n/**\n * Whether or not an event type should be registered in the capture phase.\n * @param eventType\n * @returns bool\n */\nexport const isCaptureEventType = (eventType: string) =>\n  CAPTURE_EVENT_TYPES.indexOf(eventType) >= 0;\n\n/** All event types that are registered early.  */\nconst EARLY_EVENT_TYPES = BUBBLE_EVENT_TYPES.concat(CAPTURE_EVENT_TYPES);\n\n/**\n * Whether or not an event type is registered in the early contract.\n */\nexport const isEarlyEventType = (eventType: string) => EARLY_EVENT_TYPES.indexOf(eventType) >= 0;\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {EventHandlerInfo} from './event_handler';\nimport {isCaptureEventType, EventType} from './event_type';\nimport {KeyCode} from './key_code';\n\n/**\n * Gets a browser event type, if it would differ from the JSAction event type.\n */\nexport function getBrowserEventType(eventType: string) {\n  // Mouseenter and mouseleave events are not handled directly because they\n  // are not available everywhere. In browsers where they are available, they\n  // don't bubble and aren't visible at the container boundary. Instead, we\n  // synthesize the mouseenter and mouseleave events from mouseover and\n  // mouseout events, respectively. Cf. eventcontract.js.\n  if (eventType === EventType.MOUSEENTER) {\n    return EventType.MOUSEOVER;\n  } else if (eventType === EventType.MOUSELEAVE) {\n    return EventType.MOUSEOUT;\n  } else if (eventType === EventType.POINTERENTER) {\n    return EventType.POINTEROVER;\n  } else if (eventType === EventType.POINTERLEAVE) {\n    return EventType.POINTEROUT;\n  }\n  return eventType;\n}\n\n/**\n * Registers the event handler function with the given DOM element for\n * the given event type.\n *\n * @param element The element.\n * @param eventType The event type.\n * @param handler The handler function to install.\n * @param passive A boolean value that, if `true`, indicates that the function\n *     specified by `handler` will never call `preventDefault()`.\n * @return Information needed to uninstall the event handler eventually.\n */\nexport function addEventListener(\n  element: Element,\n  eventType: string,\n  handler: (event: Event) => void,\n  passive?: boolean,\n): EventHandlerInfo {\n  // All event handlers are registered in the bubbling\n  // phase.\n  //\n  // All browsers support focus and blur, but these events only are propagated\n  // in the capture phase. Very legacy browsers do not support focusin or\n  // focusout.\n  //\n  // It would be a bad idea to register all event handlers in the\n  // capture phase because then regular onclick handlers would not be\n  // executed at all on events that trigger a jsaction. That's not\n  // entirely what we want, at least for now.\n  //\n  // Error and load events (i.e. on images) do not bubble so they are also\n  // handled in the capture phase.\n  let capture = false;\n\n  if (isCaptureEventType(eventType)) {\n    capture = true;\n  }\n\n  const options = typeof passive === 'boolean' ? {capture, passive} : capture;\n  element.addEventListener(eventType, handler, options);\n\n  return {eventType, handler, capture, passive};\n}\n\n/**\n * Removes the event handler for the given event from the element.\n * the given event type.\n *\n * @param element The element.\n * @param info The information needed to deregister the handler, as returned by\n *     addEventListener(), above.\n */\nexport function removeEventListener(element: Element, info: EventHandlerInfo) {\n  if (element.removeEventListener) {\n    // It's worth noting that some browser releases have been inconsistent on this, and unless\n    // you have specific reasons otherwise, it's probably wise to use the same values used for\n    // the call to addEventListener() when calling removeEventListener().\n    const options = typeof info.passive === 'boolean' ? {capture: info.capture} : info.capture;\n    element.removeEventListener(info.eventType, info.handler as EventListener, options);\n    // `detachEvent` is an old DOM API.\n  } else if ((element as any).detachEvent) {\n    // `detachEvent` is an old DOM API.\n    (element as any).detachEvent(`on${info.eventType}`, info.handler);\n  }\n}\n\n/**\n * Cancels propagation of an event.\n * @param e The event to cancel propagation for.\n */\nexport function stopPropagation(e: Event) {\n  e.stopPropagation ? e.stopPropagation() : (e.cancelBubble = true);\n}\n\n/**\n * Prevents the default action of an event.\n * @param e The event to prevent the default action for.\n */\nexport function preventDefault(e: Event) {\n  e.preventDefault ? e.preventDefault() : (e.returnValue = false);\n}\n\n/**\n * Gets the target Element of the event. In Firefox, a text node may appear as\n * the target of the event, in which case we return the parent element of the\n * text node.\n * @param e The event to get the target of.\n * @return The target element.\n */\nexport function getTarget(e: Event): Element {\n  let el = e.target as Element;\n\n  // In Firefox, the event may have a text node as its target. We always\n  // want the parent Element the text node belongs to, however.\n  if (!el.getAttribute && el.parentNode) {\n    el = el.parentNode as Element;\n  }\n\n  return el;\n}\n\n/**\n * Whether we are on a Mac. Not pulling in useragent just for this.\n */\nlet isMac: boolean = typeof navigator !== 'undefined' && /Macintosh/.test(navigator.userAgent);\n\n/**\n * Determines and returns whether the given event (which is assumed to be a\n * click event) is a middle click.\n * NOTE: There is not a consistent way to identify middle click\n * http://www.unixpapa.com/js/mouse.html\n */\nfunction isMiddleClick(e: Event): boolean {\n  return (\n    // `which` is an old DOM API.\n    (e as any).which === 2 ||\n    // `which` is an old DOM API.\n    ((e as any).which == null &&\n      // `button` is an old DOM API.\n      (e as any).button === 4) // middle click for IE\n  );\n}\n\n/**\n * Determines and returns whether the given event (which is assumed\n * to be a click event) is modified. A middle click is considered a modified\n * click to retain the default browser action, which opens a link in a new tab.\n * @param e The event.\n * @return Whether the given event is modified.\n */\nexport function isModifiedClickEvent(e: Event): boolean {\n  return (\n    // `metaKey` is an old DOM API.\n    (isMac && (e as any).metaKey) ||\n    // `ctrlKey` is an old DOM API.\n    (!isMac && (e as any).ctrlKey) ||\n    isMiddleClick(e) ||\n    // `shiftKey` is an old DOM API.\n    (e as any).shiftKey\n  );\n}\n\n/** Whether we are on WebKit (e.g., Chrome). */\nexport const isWebKit: boolean =\n  typeof navigator !== 'undefined' &&\n  !/Opera/.test(navigator.userAgent) &&\n  /WebKit/.test(navigator.userAgent);\n\n/** Whether we are on IE. */\nexport const isIe: boolean =\n  typeof navigator !== 'undefined' &&\n  (/MSIE/.test(navigator.userAgent) || /Trident/.test(navigator.userAgent));\n\n/** Whether we are on Gecko (e.g., Firefox). */\nexport const isGecko: boolean =\n  typeof navigator !== 'undefined' &&\n  !/Opera|WebKit/.test(navigator.userAgent) &&\n  /Gecko/.test(navigator.product);\n\n/**\n * Determines and returns whether the given element is a valid target for\n * keypress/keydown DOM events that act like regular DOM clicks.\n * @param el The element.\n * @return Whether the given element is a valid action key target.\n */\nexport function isValidActionKeyTarget(el: Element): boolean {\n  if (!('getAttribute' in el)) {\n    return false;\n  }\n  if (isTextControl(el)) {\n    return false;\n  }\n  if (isNativelyActivatable(el)) {\n    return false;\n  }\n  // `isContentEditable` is an old DOM API.\n  if ((el as any).isContentEditable) {\n    return false;\n  }\n\n  return true;\n}\n\n/**\n * Whether an event has a modifier key activated.\n * @param e The event.\n * @return True, if a modifier key is activated.\n */\nfunction hasModifierKey(e: Event): boolean {\n  return (\n    // `ctrlKey` is an old DOM API.\n    (e as any).ctrlKey ||\n    // `shiftKey` is an old DOM API.\n    (e as any).shiftKey ||\n    // `altKey` is an old DOM API.\n    (e as any).altKey ||\n    // `metaKey` is an old DOM API.\n    (e as any).metaKey\n  );\n}\n\n/**\n * Determines and returns whether the given event has a target that already\n * has event handlers attached because it is a native HTML control. Used to\n * determine if preventDefault should be called when isActionKeyEvent is true.\n * @param e The event.\n * @return If preventDefault should be called.\n */\nexport function shouldCallPreventDefaultOnNativeHtmlControl(e: Event): boolean {\n  const el = getTarget(e);\n  const tagName = el.tagName.toUpperCase();\n  const role = (el.getAttribute('role') || '').toUpperCase();\n\n  if (tagName === 'BUTTON' || role === 'BUTTON') {\n    return true;\n  }\n  if (!isNativeHTMLControl(el)) {\n    return false;\n  }\n  if (tagName === 'A') {\n    return false;\n  }\n  /**\n   * Fix for physical d-pads on feature phone platforms; the native event\n   * (ie. isTrusted: true) needs to fire to show the OPTION list. See\n   * b/135288469 for more info.\n   */\n  if (tagName === 'SELECT') {\n    return false;\n  }\n  if (processSpace(el)) {\n    return false;\n  }\n  if (isTextControl(el)) {\n    return false;\n  }\n  return true;\n}\n\n/**\n * Determines and returns whether the given event acts like a regular DOM click,\n * and should be handled instead of the click.  If this returns true, the caller\n * will call preventDefault() to prevent a possible duplicate event.\n * This is represented by a keypress (keydown on Gecko browsers) on Enter or\n * Space key.\n * @param e The event.\n * @return True, if the event emulates a DOM click.\n */\nexport function isActionKeyEvent(e: Event): boolean {\n  let key =\n    // `which` is an old DOM API.\n    (e as any).which ||\n    // `keyCode` is an old DOM API.\n    (e as any).keyCode;\n  if (!key && (e as KeyboardEvent).key) {\n    key = ACTION_KEY_TO_KEYCODE[(e as KeyboardEvent).key];\n  }\n  if (isWebKit && key === KeyCode.MAC_ENTER) {\n    key = KeyCode.ENTER;\n  }\n  if (key !== KeyCode.ENTER && key !== KeyCode.SPACE) {\n    return false;\n  }\n  const el = getTarget(e);\n  if (e.type !== EventType.KEYDOWN || !isValidActionKeyTarget(el) || hasModifierKey(e)) {\n    return false;\n  }\n\n  // For <input type=\"checkbox\">, we must only handle the browser's native click\n  // event, so that the browser can toggle the checkbox.\n  if (processSpace(el) && key === KeyCode.SPACE) {\n    return false;\n  }\n\n  // If this element is non-focusable, ignore stray keystrokes (b/18337209)\n  // Sscreen readers can move without tab focus, so any tabIndex is focusable.\n  // See B/21809604\n  if (!isFocusable(el)) {\n    return false;\n  }\n\n  const type = (\n    el.getAttribute('role') ||\n    (el as HTMLInputElement).type ||\n    el.tagName\n  ).toUpperCase();\n  const isSpecificTriggerKey = IDENTIFIER_TO_KEY_TRIGGER_MAPPING[type] % key === 0;\n  const isDefaultTriggerKey = !(type in IDENTIFIER_TO_KEY_TRIGGER_MAPPING) && key === KeyCode.ENTER;\n  const hasType = el.tagName.toUpperCase() !== 'INPUT' || !!(el as HTMLInputElement).type;\n  return (isSpecificTriggerKey || isDefaultTriggerKey) && hasType;\n}\n\n/**\n * Checks whether a DOM element can receive keyboard focus.\n * This code is based on goog.dom.isFocusable, but simplified since we shouldn't\n * care about visibility if we're already handling a keyboard event.\n */\nfunction isFocusable(el: Element): boolean {\n  return (\n    (el.tagName in NATIVELY_FOCUSABLE_ELEMENTS || hasSpecifiedTabIndex(el)) &&\n    !(el as HTMLInputElement).disabled\n  );\n}\n\n/**\n * @param element Element to check.\n * @return Whether the element has a specified tab index.\n */\nfunction hasSpecifiedTabIndex(element: Element): boolean {\n  // IE returns 0 for an unset tabIndex, so we must use getAttributeNode(),\n  // which returns an object with a 'specified' property if tabIndex is\n  // specified.  This works on other browsers, too.\n  const attrNode = element.getAttributeNode('tabindex'); // Must be lowercase!\n  return attrNode != null && attrNode.specified;\n}\n\n/** Element tagnames that are focusable by default. */\nconst NATIVELY_FOCUSABLE_ELEMENTS: {[key: string]: number} = {\n  'A': 1,\n  'INPUT': 1,\n  'TEXTAREA': 1,\n  'SELECT': 1,\n  'BUTTON': 1,\n};\n\n/** @return True, if the Space key was pressed. */\nexport function isSpaceKeyEvent(e: Event): boolean {\n  const key =\n    // `which` is an old DOM API.\n    (e as any).which ||\n    // `keyCode` is an old DOM API.\n    (e as any).keyCode;\n  const el = getTarget(e);\n  const elementName = ((el as HTMLInputElement).type || el.tagName).toUpperCase();\n  return key === KeyCode.SPACE && elementName !== 'CHECKBOX';\n}\n\n/**\n * Determines whether the event corresponds to a non-bubbling mouse\n * event type (mouseenter, mouseleave, pointerenter, and pointerleave).\n *\n * During mouseover (mouseenter) and pointerover (pointerenter), the\n * relatedTarget is the element being entered from. During mouseout (mouseleave)\n * and pointerout (pointerleave), the relatedTarget is the element being exited\n * to.\n *\n * In both cases, if relatedTarget is outside target, then the corresponding\n * special event has occurred, otherwise it hasn't.\n *\n * @param e The mouseover/mouseout event.\n * @param type The type of the mouse special event.\n * @param element The element on which the jsaction for the\n *     mouseenter/mouseleave event is defined.\n * @return True if the event is a mouseenter/mouseleave event.\n */\nexport function isMouseSpecialEvent(e: Event, type: string, element: Element): boolean {\n  // `relatedTarget` is an old DOM API.\n  const related = (e as any).relatedTarget as Node;\n\n  return (\n    ((e.type === EventType.MOUSEOVER && type === EventType.MOUSEENTER) ||\n      (e.type === EventType.MOUSEOUT && type === EventType.MOUSELEAVE) ||\n      (e.type === EventType.POINTEROVER && type === EventType.POINTERENTER) ||\n      (e.type === EventType.POINTEROUT && type === EventType.POINTERLEAVE)) &&\n    (!related || (related !== element && !element.contains(related)))\n  );\n}\n\n/**\n * Creates a new EventLike object for a mouseenter/mouseleave event that's\n * derived from the original corresponding mouseover/mouseout event.\n * @param e The event.\n * @param target The element on which the jsaction for the mouseenter/mouseleave\n *     event is defined.\n * @return A modified event-like object copied from the event object passed into\n *     this function.\n */\nexport function createMouseSpecialEvent(e: Event, target: Element): Event {\n  // We have to create a copy of the event object because we need to mutate\n  // its fields. We do this for the special mouse events because the event\n  // target needs to be retargeted to the action element rather than the real\n  // element (since we are simulating the special mouse events with mouseover/\n  // mouseout).\n  //\n  // Since we're making a copy anyways, we might as well attempt to convert\n  // this event into a pseudo-real mouseenter/mouseleave event by adjusting\n  // its type.\n  //\n  const copy: {-readonly [P in keyof Event]?: Event[P]} & {'_originalEvent'?: Event} = {};\n  for (const property in e) {\n    if (property === 'srcElement' || property === 'target') {\n      continue;\n    }\n    const key = property as keyof Event;\n    // Making a copy requires iterating through all properties of `Event`.\n    const value = e[key];\n    if (typeof value === 'function') {\n      continue;\n    }\n    // Value should be the expected type, but the value of `key` is not known\n    // statically.\n    copy[key] = value as any;\n  }\n  if (e.type === EventType.MOUSEOVER) {\n    copy['type'] = EventType.MOUSEENTER;\n  } else if (e.type === EventType.MOUSEOUT) {\n    copy['type'] = EventType.MOUSELEAVE;\n  } else if (e.type === EventType.POINTEROVER) {\n    copy['type'] = EventType.POINTERENTER;\n  } else {\n    copy['type'] = EventType.POINTERLEAVE;\n  }\n  copy['target'] = copy['srcElement'] = target;\n  copy['bubbles'] = false;\n  copy['_originalEvent'] = e;\n  return copy as Event;\n}\n\n/**\n * Returns touch data extracted from the touch event: clientX, clientY, screenX\n * and screenY. If the event has no touch information at all, the returned\n * value is null.\n *\n * The fields of this Object are unquoted.\n *\n * @param event A touch event.\n */\nexport function getTouchData(\n  event: TouchEvent,\n): {clientX: number; clientY: number; screenX: number; screenY: number} | null {\n  const touch =\n    (event.changedTouches && event.changedTouches[0]) || (event.touches && event.touches[0]);\n  if (!touch) {\n    return null;\n  }\n  return {\n    clientX: touch.clientX,\n    clientY: touch.clientY,\n    screenX: touch.screenX,\n    screenY: touch.screenY,\n  };\n}\n\ndeclare interface SyntheticMouseEvent extends Event {\n  // Redeclared from Event to indicate that it is not readonly.\n  defaultPrevented: boolean;\n  originalEventType: string;\n  _propagationStopped?: boolean;\n}\n\n/**\n * Creates a new EventLike object for a \"click\" event that's derived from the\n * original corresponding \"touchend\" event for a fast-click implementation.\n *\n * It takes a touch event, adds common fields found in a click event and\n * changes the type to 'click', so that the resulting event looks more like\n * a real click event.\n *\n * @param event A touch event.\n * @return A modified event-like object copied from the event object passed into\n *     this function.\n */\nexport function recreateTouchEventAsClick(event: TouchEvent): MouseEvent {\n  const click: {-readonly [P in keyof MouseEvent]?: MouseEvent[P]} & Partial<SyntheticMouseEvent> =\n    {};\n  click['originalEventType'] = event.type;\n  click['type'] = EventType.CLICK;\n  for (const property in event) {\n    if (property === 'type' || property === 'srcElement') {\n      continue;\n    }\n    const key = property as keyof TouchEvent;\n    // Making a copy requires iterating through all properties of `TouchEvent`.\n    const value = event[key];\n    if (typeof value === 'function') {\n      continue;\n    }\n    // Value should be the expected type, but the value of `key` is not known\n    // statically.\n    click[key as keyof MouseEvent] = value as any;\n  }\n\n  // Ensure that the event has the most recent timestamp. This timestamp\n  // may be used in the future to validate or cancel subsequent click events.\n  click['timeStamp'] = Date.now();\n\n  // Emulate preventDefault and stopPropagation behavior\n  click['defaultPrevented'] = false;\n  click['preventDefault'] = syntheticPreventDefault;\n  click['_propagationStopped'] = false;\n  click['stopPropagation'] = syntheticStopPropagation;\n\n  // Emulate click coordinates using touch info\n  const touch = getTouchData(event);\n  if (touch) {\n    click['clientX'] = touch.clientX;\n    click['clientY'] = touch.clientY;\n    click['screenX'] = touch.screenX;\n    click['screenY'] = touch.screenY;\n  }\n  return click as MouseEvent;\n}\n\n/**\n * An implementation of \"preventDefault\" for a synthesized event. Simply\n * sets \"defaultPrevented\" property to true.\n */\nfunction syntheticPreventDefault(this: Event) {\n  (this as SyntheticMouseEvent).defaultPrevented = true;\n}\n\n/**\n * An implementation of \"stopPropagation\" for a synthesized event. It simply\n * sets a synthetic non-standard \"_propagationStopped\" property to true.\n */\nfunction syntheticStopPropagation(this: Event) {\n  (this as SyntheticMouseEvent)._propagationStopped = true;\n}\n\n/**\n * Mapping of KeyboardEvent.key values to\n * KeyCode values.\n */\nconst ACTION_KEY_TO_KEYCODE: {[key: string]: number} = {\n  'Enter': KeyCode.ENTER,\n  ' ': KeyCode.SPACE,\n};\n\n/**\n * Mapping of HTML element identifiers (ARIA role, type, or tagName) to the\n * keys (enter and/or space) that should activate them. A value of zero means\n * that both should activate them.\n */\nexport const IDENTIFIER_TO_KEY_TRIGGER_MAPPING: {[key: string]: number} = {\n  'A': KeyCode.ENTER,\n  'BUTTON': 0,\n  'CHECKBOX': KeyCode.SPACE,\n  'COMBOBOX': KeyCode.ENTER,\n  'FILE': 0,\n  'GRIDCELL': KeyCode.ENTER,\n  'LINK': KeyCode.ENTER,\n  'LISTBOX': KeyCode.ENTER,\n  'MENU': 0,\n  'MENUBAR': 0,\n  'MENUITEM': 0,\n  'MENUITEMCHECKBOX': 0,\n  'MENUITEMRADIO': 0,\n  'OPTION': 0,\n  'RADIO': KeyCode.SPACE,\n  'RADIOGROUP': KeyCode.SPACE,\n  'RESET': 0,\n  'SUBMIT': 0,\n  'SWITCH': KeyCode.SPACE,\n  'TAB': 0,\n  'TREE': KeyCode.ENTER,\n  'TREEITEM': KeyCode.ENTER,\n};\n\n/**\n * Returns whether or not to process space based on the type of the element;\n * checks to make sure that type is not null.\n * @param element The element.\n * @return Whether or not to process space based on type.\n */\nfunction processSpace(element: Element): boolean {\n  const type = (element.getAttribute('type') || element.tagName).toUpperCase();\n  return type in PROCESS_SPACE;\n}\n\n/**\n * Returns whether or not the given element is a text control.\n * @param el The element.\n * @return Whether or not the given element is a text control.\n */\nfunction isTextControl(el: Element): boolean {\n  const type = (el.getAttribute('type') || el.tagName).toUpperCase();\n  return type in TEXT_CONTROLS;\n}\n\n/**\n * Returns if the given element is a native HTML control.\n * @param el The element.\n * @return If the given element is a native HTML control.\n */\nexport function isNativeHTMLControl(el: Element): boolean {\n  return el.tagName.toUpperCase() in NATIVE_HTML_CONTROLS;\n}\n\n/**\n * Returns if the given element is natively activatable. Browsers emit click\n * events for natively activatable elements, even when activated via keyboard.\n * For these elements, we don't need to raise a11y click events.\n * @param el The element.\n * @return If the given element is a native HTML control.\n */\nfunction isNativelyActivatable(el: Element): boolean {\n  return (\n    el.tagName.toUpperCase() === 'BUTTON' ||\n    (!!(el as HTMLInputElement).type && (el as HTMLInputElement).type.toUpperCase() === 'FILE')\n  );\n}\n\n/**\n * HTML <input> types (not ARIA roles) which will auto-trigger a click event for\n * the Space key, with side-effects. We will not call preventDefault if space is\n * pressed, nor will we raise a11y click events.  For all other elements, we can\n * suppress the default event (which has no desired side-effects) and handle the\n * keydown ourselves.\n */\nconst PROCESS_SPACE: {[key: string]: boolean} = {\n  'CHECKBOX': true,\n  'FILE': true,\n  'OPTION': true,\n  'RADIO': true,\n};\n\n/** TagNames and Input types for which to not process enter/space as click. */\nconst TEXT_CONTROLS: {[key: string]: boolean} = {\n  'COLOR': true,\n  'DATE': true,\n  'DATETIME': true,\n  'DATETIME-LOCAL': true,\n  'EMAIL': true,\n  'MONTH': true,\n  'NUMBER': true,\n  'PASSWORD': true,\n  'RANGE': true,\n  'SEARCH': true,\n  'TEL': true,\n  'TEXT': true,\n  'TEXTAREA': true,\n  'TIME': true,\n  'URL': true,\n  'WEEK': true,\n};\n\n/** TagNames that are native HTML controls. */\nconst NATIVE_HTML_CONTROLS: {[key: string]: boolean} = {\n  'A': true,\n  'AREA': true,\n  'BUTTON': true,\n  'DIALOG': true,\n  'IMG': true,\n  'INPUT': true,\n  'LINK': true,\n  'MENU': true,\n  'OPTGROUP': true,\n  'OPTION': true,\n  'PROGRESS': true,\n  'SELECT': true,\n  'TEXTAREA': true,\n};\n\n/** Exported for testing. */\nexport const testing = {\n  setIsMac(value: boolean) {\n    isMac = value;\n  },\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport * as eventLib from './event';\nimport {EventHandlerInfo} from './event_handler';\n\n/**\n * An `EventContractContainerManager` provides the common interface for managing\n * containers.\n */\nexport interface EventContractContainerManager {\n  addEventListener(\n    eventType: string,\n    getHandler: (element: Element) => (event: Event) => void,\n    passive?: boolean,\n  ): void;\n\n  cleanUp(): void;\n}\n\n/**\n * Whether the user agent is running on iOS.\n */\nconst isIos = typeof navigator !== 'undefined' && /iPhone|iPad|iPod/.test(navigator.userAgent);\n\n/**\n * A class representing a container node and all the event handlers\n * installed on it. Used so that handlers can be cleaned up if the\n * container is removed from the contract.\n */\nexport class EventContractContainer implements EventContractContainerManager {\n  /**\n   * Array of event handlers and their corresponding event types that are\n   * installed on this container.\n   *\n   */\n  private handlerInfos: EventHandlerInfo[] = [];\n\n  /**\n   * @param element The container Element.\n   */\n  constructor(readonly element: Element) {}\n\n  /**\n   * Installs the provided installer on the element owned by this container,\n   * and maintains a reference to resulting handler in order to remove it\n   * later if desired.\n   */\n  addEventListener(\n    eventType: string,\n    getHandler: (element: Element) => (event: Event) => void,\n    passive?: boolean,\n  ) {\n    // In iOS, event bubbling doesn't happen automatically in any DOM element,\n    // unless it has an onclick attribute or DOM event handler attached to it.\n    // This breaks JsAction in some cases. See \"Making Elements Clickable\"\n    // section at http://goo.gl/2VoGnB.\n    //\n    // A workaround for this issue is to change the CSS cursor style to 'pointer'\n    // for the container element, which magically turns on event bubbling. This\n    // solution is described in the comments section at http://goo.gl/6pEO1z.\n    //\n    // We use a navigator.userAgent check here as this problem is present both\n    // on Mobile Safari and thin WebKit wrappers, such as Chrome for iOS.\n    if (isIos) {\n      (this.element as HTMLElement).style.cursor = 'pointer';\n    }\n    this.handlerInfos.push(\n      eventLib.addEventListener(this.element, eventType, getHandler(this.element), passive),\n    );\n  }\n\n  /**\n   * Removes all the handlers installed on this container.\n   */\n  cleanUp() {\n    for (let i = 0; i < this.handlerInfos.length; i++) {\n      eventLib.removeEventListener(this.element, this.handlerInfos[i]);\n    }\n\n    this.handlerInfos = [];\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nexport const Char = {\n  /**\n   * The separator between the namespace and the action name in the\n   * jsaction attribute value.\n   */\n  NAMESPACE_ACTION_SEPARATOR: '.' as const,\n\n  /**\n   * The separator between the event name and action in the jsaction\n   * attribute value.\n   */\n  EVENT_ACTION_SEPARATOR: ':' as const,\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Records information about the action that should handle a given `Event`.\n */\nexport interface ActionInfo {\n  name: string;\n  element: Element;\n}\n\ntype ActionInfoInternal = [name: string, element: Element];\n\n/**\n * Records information for later handling of events. This type is\n * shared, and instances of it are passed, between the eventcontract\n * and the dispatcher jsbinary. Therefore, the fields of this type are\n * referenced by string literals rather than property literals\n * throughout the code.\n *\n * 'targetElement' is the element the action occurred on, 'actionElement'\n * is the element that has the jsaction handler.\n *\n * A null 'actionElement' identifies an EventInfo instance that didn't match a\n * jsaction attribute.  This allows us to execute global event handlers with the\n * appropriate event type (including a11y clicks and custom events).\n * The declare portion of this interface creates a set of externs that make sure\n * renaming doesn't happen for EventInfo. This is important since EventInfo\n * is shared across multiple binaries.\n */\nexport declare interface EventInfo {\n  eventType: string;\n  event: Event;\n  targetElement: Element;\n  /** The element that is the container for this Event. */\n  eic: Element;\n  timeStamp: number;\n  /**\n   * The action parsed from the JSAction element.\n   */\n  eia?: ActionInfoInternal;\n  /**\n   * Whether this `Event` is a replay event, meaning no dispatcher was\n   * installed when this `Event` was originally dispatched.\n   */\n  eirp?: boolean;\n  /**\n   * Whether this `Event` represents a `keydown` event that should be processed\n   * as a `click`. Only used when a11y click events is on.\n   */\n  eiack?: boolean;\n  /** Whether action resolution has already run on this `EventInfo`. */\n  eir?: boolean;\n}\n\n/** Added for readability when accessing stable property names. */\nexport function getEventType(eventInfo: EventInfo) {\n  return eventInfo.eventType;\n}\n\n/** Added for readability when accessing stable property names. */\nexport function setEventType(eventInfo: EventInfo, eventType: string) {\n  eventInfo.eventType = eventType;\n}\n\n/** Added for readability when accessing stable property names. */\nexport function getEvent(eventInfo: EventInfo) {\n  return eventInfo.event;\n}\n\n/** Added for readability when accessing stable property names. */\nexport function setEvent(eventInfo: EventInfo, event: Event) {\n  eventInfo.event = event;\n}\n\n/** Added for readability when accessing stable property names. */\nexport function getTargetElement(eventInfo: EventInfo) {\n  return eventInfo.targetElement;\n}\n\n/** Added for readability when accessing stable property names. */\nexport function setTargetElement(eventInfo: EventInfo, targetElement: Element) {\n  eventInfo.targetElement = targetElement;\n}\n\n/** Added for readability when accessing stable property names. */\nexport function getContainer(eventInfo: EventInfo) {\n  return eventInfo.eic;\n}\n\n/** Added for readability when accessing stable property names. */\nexport function setContainer(eventInfo: EventInfo, container: Element) {\n  eventInfo.eic = container;\n}\n\n/** Added for readability when accessing stable property names. */\nexport function getTimestamp(eventInfo: EventInfo) {\n  return eventInfo.timeStamp;\n}\n\n/** Added for readability when accessing stable property names. */\nexport function setTimestamp(eventInfo: EventInfo, timestamp: number) {\n  eventInfo.timeStamp = timestamp;\n}\n\n/** Added for readability when accessing stable property names. */\nexport function getAction(eventInfo: EventInfo) {\n  return eventInfo.eia;\n}\n\n/** Added for readability when accessing stable property names. */\nexport function setAction(eventInfo: EventInfo, actionName: string, actionElement: Element) {\n  eventInfo.eia = [actionName, actionElement];\n}\n\n/** Added for readability when accessing stable property names. */\nexport function unsetAction(eventInfo: EventInfo) {\n  eventInfo.eia = undefined;\n}\n\n/** Added for readability when accessing stable property names. */\nexport function getActionName(actionInfo: ActionInfoInternal) {\n  return actionInfo[0];\n}\n\n/** Added for readability when accessing stable property names. */\nexport function getActionElement(actionInfo: ActionInfoInternal) {\n  return actionInfo[1];\n}\n\n/** Added for readability when accessing stable property names. */\nexport function getIsReplay(eventInfo: EventInfo) {\n  return eventInfo.eirp;\n}\n\n/** Added for readability when accessing stable property names. */\nexport function setIsReplay(eventInfo: EventInfo, replay: boolean) {\n  eventInfo.eirp = replay;\n}\n\n/** Added for readability when accessing stable property names. */\nexport function getA11yClickKey(eventInfo: EventInfo) {\n  return eventInfo.eiack;\n}\n\n/** Added for readability when accessing stable property names. */\nexport function setA11yClickKey(eventInfo: EventInfo, a11yClickKey: boolean) {\n  eventInfo.eiack = a11yClickKey;\n}\n\n/** Added for readability when accessing stable property names. */\nexport function getResolved(eventInfo: EventInfo) {\n  return eventInfo.eir;\n}\n\n/** Added for readability when accessing stable property names. */\nexport function setResolved(eventInfo: EventInfo, resolved: boolean) {\n  eventInfo.eir = resolved;\n}\n\n/** Clones an `EventInfo` */\nexport function cloneEventInfo(eventInfo: EventInfo): EventInfo {\n  return {\n    eventType: eventInfo.eventType,\n    event: eventInfo.event,\n    targetElement: eventInfo.targetElement,\n    eic: eventInfo.eic,\n    eia: eventInfo.eia,\n    timeStamp: eventInfo.timeStamp,\n    eirp: eventInfo.eirp,\n    eiack: eventInfo.eiack,\n    eir: eventInfo.eir,\n  };\n}\n\n/**\n * Utility function for creating an `EventInfo`.\n *\n * This can be used from code-size sensitive compilation units, as taking\n * parameters vs. an `Object` literal reduces code size.\n */\nexport function createEventInfoFromParameters(\n  eventType: string,\n  event: Event,\n  targetElement: Element,\n  container: Element,\n  timestamp: number,\n  action?: ActionInfoInternal,\n  isReplay?: boolean,\n  a11yClickKey?: boolean,\n): EventInfo {\n  return {\n    eventType,\n    event,\n    targetElement,\n    eic: container,\n    timeStamp: timestamp,\n    eia: action,\n    eirp: isReplay,\n    eiack: a11yClickKey,\n  };\n}\n\n/**\n * Utility function for creating an `EventInfo`.\n *\n * This should be used in compilation units that are less sensitive to code\n * size.\n */\nexport function createEventInfo({\n  eventType,\n  event,\n  targetElement,\n  container,\n  timestamp,\n  action,\n  isReplay,\n  a11yClickKey,\n}: {\n  eventType: string;\n  event: Event;\n  targetElement: Element;\n  container: Element;\n  timestamp: number;\n  action?: ActionInfo;\n  isReplay?: boolean;\n  a11yClickKey?: boolean;\n}): EventInfo {\n  return {\n    eventType,\n    event,\n    targetElement,\n    eic: container,\n    timeStamp: timestamp,\n    eia: action ? [action.name, action.element] : undefined,\n    eirp: isReplay,\n    eiack: a11yClickKey,\n  };\n}\n\n/**\n * Utility class around an `EventInfo`.\n *\n * This should be used in compilation units that are less sensitive to code\n * size.\n */\nexport class EventInfoWrapper {\n  constructor(readonly eventInfo: EventInfo) {}\n\n  getEventType() {\n    return getEventType(this.eventInfo);\n  }\n\n  setEventType(eventType: string) {\n    setEventType(this.eventInfo, eventType);\n  }\n\n  getEvent() {\n    return getEvent(this.eventInfo);\n  }\n\n  setEvent(event: Event) {\n    setEvent(this.eventInfo, event);\n  }\n\n  getTargetElement() {\n    return getTargetElement(this.eventInfo);\n  }\n\n  setTargetElement(targetElement: Element) {\n    setTargetElement(this.eventInfo, targetElement);\n  }\n\n  getContainer() {\n    return getContainer(this.eventInfo);\n  }\n\n  setContainer(container: Element) {\n    setContainer(this.eventInfo, container);\n  }\n  getTimestamp() {\n    return getTimestamp(this.eventInfo);\n  }\n\n  setTimestamp(timestamp: number) {\n    setTimestamp(this.eventInfo, timestamp);\n  }\n\n  getAction() {\n    const action = getAction(this.eventInfo);\n    if (!action) return undefined;\n    return {\n      name: action[0],\n      element: action[1],\n    };\n  }\n\n  setAction(action: ActionInfo | undefined) {\n    if (!action) {\n      unsetAction(this.eventInfo);\n      return;\n    }\n    setAction(this.eventInfo, action.name, action.element);\n  }\n\n  getIsReplay() {\n    return getIsReplay(this.eventInfo);\n  }\n\n  setIsReplay(replay: boolean) {\n    setIsReplay(this.eventInfo, replay);\n  }\n\n  getResolved() {\n    return getResolved(this.eventInfo);\n  }\n\n  setResolved(resolved: boolean) {\n    setResolved(this.eventInfo, resolved);\n  }\n\n  clone() {\n    return new EventInfoWrapper(cloneEventInfo(this.eventInfo));\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Attribute} from './attribute';\nimport {Char} from './char';\nimport {EventType} from './event_type';\nimport {Property} from './property';\nimport * as a11yClick from './a11y_click';\nimport * as cache from './cache';\nimport * as eventInfoLib from './event_info';\nimport * as eventLib from './event';\n\n/**\n * Since maps from event to action are immutable we can use a single map\n * to represent the empty map.\n */\nconst EMPTY_ACTION_MAP: {[key: string]: string} = {};\n\n/**\n * This regular expression matches a semicolon.\n */\nconst REGEXP_SEMICOLON = /\\s*;\\s*/;\n\n/** If no event type is defined, defaults to `click`. */\nconst DEFAULT_EVENT_TYPE: string = EventType.CLICK;\n\n/** Resolves actions for Events. */\nexport class ActionResolver {\n  private a11yClickSupport: boolean = false;\n  private clickModSupport: boolean = true;\n  private readonly syntheticMouseEventSupport: boolean;\n\n  private updateEventInfoForA11yClick?: (eventInfo: eventInfoLib.EventInfo) => void = undefined;\n\n  private preventDefaultForA11yClick?: (eventInfo: eventInfoLib.EventInfo) => void = undefined;\n\n  private populateClickOnlyAction?: (\n    actionElement: Element,\n    eventInfo: eventInfoLib.EventInfo,\n    actionMap: {[key: string]: string | undefined},\n  ) => void = undefined;\n\n  constructor({\n    syntheticMouseEventSupport = false,\n    clickModSupport = true,\n  }: {\n    syntheticMouseEventSupport?: boolean;\n    clickModSupport?: boolean;\n  } = {}) {\n    this.syntheticMouseEventSupport = syntheticMouseEventSupport;\n    this.clickModSupport = clickModSupport;\n  }\n\n  resolveEventType(eventInfo: eventInfoLib.EventInfo) {\n    // We distinguish modified and plain clicks in order to support the\n    // default browser behavior of modified clicks on links; usually to\n    // open the URL of the link in new tab or new window on ctrl/cmd\n    // click. A DOM 'click' event is mapped to the jsaction 'click'\n    // event iff there is no modifier present on the event. If there is\n    // a modifier, it's mapped to 'clickmod' instead.\n    //\n    // It's allowed to omit the event in the jsaction attribute. In that\n    // case, 'click' is assumed. Thus the following two are equivalent:\n    //\n    //   <a href=\"someurl\" jsaction=\"gna.fu\">\n    //   <a href=\"someurl\" jsaction=\"click:gna.fu\">\n    //\n    // For unmodified clicks, EventContract invokes the jsaction\n    // 'gna.fu'. For modified clicks, EventContract won't find a\n    // suitable action and leave the event to be handled by the\n    // browser.\n    //\n    // In order to also invoke a jsaction handler for a modifier click,\n    // 'clickmod' needs to be used:\n    //\n    //   <a href=\"someurl\" jsaction=\"clickmod:gna.fu\">\n    //\n    // EventContract invokes the jsaction 'gna.fu' for modified\n    // clicks. Unmodified clicks are left to the browser.\n    //\n    // In order to set up the event contract to handle both clickonly and\n    // clickmod, only addEvent(EventType.CLICK) is necessary.\n    //\n    // In order to set up the event contract to handle click,\n    // addEvent() is necessary for CLICK, KEYDOWN, and KEYPRESS event types.  If\n    // a11y click support is enabled, addEvent() will set up the appropriate key\n    // event handler automatically.\n    if (\n      this.clickModSupport &&\n      eventInfoLib.getEventType(eventInfo) === EventType.CLICK &&\n      eventLib.isModifiedClickEvent(eventInfoLib.getEvent(eventInfo))\n    ) {\n      eventInfoLib.setEventType(eventInfo, EventType.CLICKMOD);\n    } else if (this.a11yClickSupport) {\n      this.updateEventInfoForA11yClick!(eventInfo);\n    }\n  }\n\n  resolveAction(eventInfo: eventInfoLib.EventInfo) {\n    if (eventInfoLib.getResolved(eventInfo)) {\n      return;\n    }\n    this.populateAction(eventInfo, eventInfoLib.getTargetElement(eventInfo));\n    eventInfoLib.setResolved(eventInfo, true);\n  }\n\n  resolveParentAction(eventInfo: eventInfoLib.EventInfo) {\n    const action = eventInfoLib.getAction(eventInfo);\n    const actionElement = action && eventInfoLib.getActionElement(action);\n    eventInfoLib.unsetAction(eventInfo);\n    const parentNode = actionElement && this.getParentNode(actionElement);\n    if (!parentNode) {\n      return;\n    }\n    this.populateAction(eventInfo, parentNode);\n  }\n\n  /**\n   * Searches for a jsaction that the DOM event maps to and creates an\n   * object containing event information used for dispatching by\n   * jsaction.Dispatcher. This method populates the `action` and `actionElement`\n   * fields of the EventInfo object passed in by finding the first\n   * jsaction attribute above the target Node of the event, and below\n   * the container Node, that specifies a jsaction for the event\n   * type. If no such jsaction is found, then action is undefined.\n   *\n   * @param eventInfo `EventInfo` to set `action` and `actionElement` if an\n   *    action is found on any `Element` in the path of the `Event`.\n   */\n  private populateAction(eventInfo: eventInfoLib.EventInfo, currentTarget: Element) {\n    let actionElement: Element | null = currentTarget;\n    while (actionElement && actionElement !== eventInfoLib.getContainer(eventInfo)) {\n      if (actionElement.nodeType === Node.ELEMENT_NODE) {\n        this.populateActionOnElement(actionElement, eventInfo);\n      }\n\n      if (eventInfoLib.getAction(eventInfo)) {\n        // An event is handled by at most one jsaction. Thus we stop at the\n        // first matching jsaction specified in a jsaction attribute up the\n        // ancestor chain of the event target node.\n        break;\n      }\n      actionElement = this.getParentNode(actionElement);\n    }\n\n    const action = eventInfoLib.getAction(eventInfo);\n    if (!action) {\n      // No action found.\n      return;\n    }\n\n    if (this.a11yClickSupport) {\n      this.preventDefaultForA11yClick!(eventInfo);\n    }\n\n    // We attempt to handle the mouseenter/mouseleave events here by\n    // detecting whether the mouseover/mouseout events correspond to\n    // entering/leaving an element.\n    if (this.syntheticMouseEventSupport) {\n      if (\n        eventInfoLib.getEventType(eventInfo) === EventType.MOUSEENTER ||\n        eventInfoLib.getEventType(eventInfo) === EventType.MOUSELEAVE ||\n        eventInfoLib.getEventType(eventInfo) === EventType.POINTERENTER ||\n        eventInfoLib.getEventType(eventInfo) === EventType.POINTERLEAVE\n      ) {\n        // We attempt to handle the mouseenter/mouseleave events here by\n        // detecting whether the mouseover/mouseout events correspond to\n        // entering/leaving an element.\n        if (\n          eventLib.isMouseSpecialEvent(\n            eventInfoLib.getEvent(eventInfo),\n            eventInfoLib.getEventType(eventInfo),\n            eventInfoLib.getActionElement(action),\n          )\n        ) {\n          // If both mouseover/mouseout and mouseenter/mouseleave events are\n          // enabled, two separate handlers for mouseover/mouseout are\n          // registered. Both handlers will see the same event instance\n          // so we create a copy to avoid interfering with the dispatching of\n          // the mouseover/mouseout event.\n          const copiedEvent = eventLib.createMouseSpecialEvent(\n            eventInfoLib.getEvent(eventInfo),\n            eventInfoLib.getActionElement(action),\n          );\n          eventInfoLib.setEvent(eventInfo, copiedEvent);\n          // Since the mouseenter/mouseleave events do not bubble, the target\n          // of the event is technically the `actionElement` (the node with the\n          // `jsaction` attribute)\n          eventInfoLib.setTargetElement(eventInfo, eventInfoLib.getActionElement(action));\n        } else {\n          eventInfoLib.unsetAction(eventInfo);\n        }\n      }\n    }\n  }\n\n  /**\n   * Walk to the parent node, unless the node has a different owner in\n   * which case we walk to the owner. Attempt to walk to host of a\n   * shadow root if needed.\n   */\n  private getParentNode(element: Element): Element | null {\n    const owner = element[Property.OWNER];\n    if (owner) {\n      return owner as Element;\n    }\n    const parentNode = element.parentNode;\n    if (parentNode?.nodeName === '#document-fragment') {\n      return (parentNode as ShadowRoot | null)?.host ?? null;\n    }\n    return parentNode as Element | null;\n  }\n\n  /**\n   * Accesses the jsaction map on a node and retrieves the name of the\n   * action the given event is mapped to, if any. It parses the\n   * attribute value and stores it in a property on the node for\n   * subsequent retrieval without re-parsing and re-accessing the\n   * attribute.\n   *\n   * @param actionElement The DOM node to retrieve the jsaction map from.\n   * @param eventInfo `EventInfo` to set `action` and `actionElement` if an\n   *    action is found on the `actionElement`.\n   */\n  private populateActionOnElement(actionElement: Element, eventInfo: eventInfoLib.EventInfo) {\n    const actionMap = this.parseActions(actionElement);\n\n    const actionName = actionMap[eventInfoLib.getEventType(eventInfo)];\n    if (actionName !== undefined) {\n      eventInfoLib.setAction(eventInfo, actionName, actionElement);\n    }\n\n    if (this.a11yClickSupport) {\n      this.populateClickOnlyAction!(actionElement, eventInfo, actionMap);\n    }\n  }\n\n  /**\n   * Parses and caches an element's jsaction element into a map.\n   *\n   * This is primarily for internal use.\n   *\n   * @param actionElement The DOM node to retrieve the jsaction map from.\n   * @return Map from event to qualified name of the jsaction bound to it.\n   */\n  private parseActions(actionElement: Element): {[key: string]: string | undefined} {\n    let actionMap: {[key: string]: string | undefined} | undefined = cache.get(actionElement);\n    if (!actionMap) {\n      const jsactionAttribute = actionElement.getAttribute(Attribute.JSACTION);\n      if (!jsactionAttribute) {\n        actionMap = EMPTY_ACTION_MAP;\n        cache.set(actionElement, actionMap);\n      } else {\n        actionMap = cache.getParsed(jsactionAttribute);\n        if (!actionMap) {\n          actionMap = {};\n          const values = jsactionAttribute.split(REGEXP_SEMICOLON);\n          for (let idx = 0; idx < values.length; idx++) {\n            const value = values[idx];\n            if (!value) {\n              continue;\n            }\n            const colon = value.indexOf(Char.EVENT_ACTION_SEPARATOR);\n            const hasColon = colon !== -1;\n            const type = hasColon ? value.substr(0, colon).trim() : DEFAULT_EVENT_TYPE;\n            const action = hasColon ? value.substr(colon + 1).trim() : value;\n            actionMap[type] = action;\n          }\n          cache.setParsed(jsactionAttribute, actionMap);\n        }\n        cache.set(actionElement, actionMap);\n      }\n    }\n    return actionMap;\n  }\n\n  addA11yClickSupport(\n    updateEventInfoForA11yClick: typeof a11yClick.updateEventInfoForA11yClick,\n    preventDefaultForA11yClick: typeof a11yClick.preventDefaultForA11yClick,\n    populateClickOnlyAction: typeof a11yClick.populateClickOnlyAction,\n  ) {\n    this.a11yClickSupport = true;\n    this.updateEventInfoForA11yClick = updateEventInfoForA11yClick;\n    this.preventDefaultForA11yClick = preventDefaultForA11yClick;\n    this.populateClickOnlyAction = populateClickOnlyAction;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * @fileoverview An enum to control who can call certain jsaction APIs.\n */\n\nexport enum Restriction {\n  I_AM_THE_JSACTION_FRAMEWORK,\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {EventInfo, EventInfoWrapper} from './event_info';\nimport {EventType} from './event_type';\nimport {Restriction} from './restriction';\nimport {UnrenamedEventContract} from './eventcontract';\nimport * as eventLib from './event';\nimport {ActionResolver} from './action_resolver';\n\n/**\n * A replayer is a function that is called when there are queued events,\n * either from the `EventContract` or when there are no detected handlers.\n */\nexport type Replayer = (eventInfoWrappers: EventInfoWrapper[]) => void;\n\n/**\n * Receives a DOM event, determines the jsaction associated with the source\n * element of the DOM event, and invokes the handler associated with the\n * jsaction.\n */\nexport class Dispatcher {\n  // The ActionResolver to use to resolve actions.\n  private actionResolver?: ActionResolver;\n\n  /** The replayer function to be called when there are queued events. */\n  private eventReplayer?: Replayer;\n\n  /** Whether the event replay is scheduled. */\n  private eventReplayScheduled = false;\n\n  /** The queue of events. */\n  private readonly replayEventInfoWrappers: EventInfoWrapper[] = [];\n\n  /**\n   * Options are:\n   *   - `eventReplayer`: When the event contract dispatches replay events\n   *      to the Dispatcher, the Dispatcher collects them and in the next tick\n   *      dispatches them to the `eventReplayer`. Defaults to dispatching to `dispatchDelegate`.\n   * @param dispatchDelegate A function that should handle dispatching an `EventInfoWrapper` to handlers.\n   */\n  constructor(\n    private readonly dispatchDelegate: (eventInfoWrapper: EventInfoWrapper) => void,\n    {\n      actionResolver,\n      eventReplayer,\n    }: {actionResolver?: ActionResolver; eventReplayer?: Replayer} = {},\n  ) {\n    this.actionResolver = actionResolver;\n    this.eventReplayer = eventReplayer;\n  }\n\n  /**\n   * Receives an event or the event queue from the EventContract. The event\n   * queue is copied and it attempts to replay.\n   * If event info is passed in it looks for an action handler that can handle\n   * the given event.  If there is no handler registered queues the event and\n   * checks if a loader is registered for the given namespace. If so, calls it.\n   *\n   * Alternatively, if in global dispatch mode, calls all registered global\n   * handlers for the appropriate event type.\n   *\n   * The three functionalities of this call are deliberately not split into\n   * three methods (and then declared as an abstract interface), because the\n   * interface is used by EventContract, which lives in a different jsbinary.\n   * Therefore the interface between the three is defined entirely in terms that\n   * are invariant under jscompiler processing (Function and Array, as opposed\n   * to a custom type with method names).\n   *\n   * @param eventInfo The info for the event that triggered this call or the\n   *     queue of events from EventContract.\n   */\n  dispatch(eventInfo: EventInfo): void {\n    const eventInfoWrapper = new EventInfoWrapper(eventInfo);\n    this.actionResolver?.resolveEventType(eventInfo);\n    this.actionResolver?.resolveAction(eventInfo);\n    const action = eventInfoWrapper.getAction();\n    if (action && shouldPreventDefaultBeforeDispatching(action.element, eventInfoWrapper)) {\n      eventLib.preventDefault(eventInfoWrapper.getEvent());\n    }\n    if (this.eventReplayer && eventInfoWrapper.getIsReplay()) {\n      this.scheduleEventInfoWrapperReplay(eventInfoWrapper);\n      return;\n    }\n    this.dispatchDelegate(eventInfoWrapper);\n  }\n\n  /**\n   * Schedules an `EventInfoWrapper` for replay. The replaying will happen in its own\n   * stack once the current flow cedes control. This is done to mimic\n   * browser event handling.\n   */\n  private scheduleEventInfoWrapperReplay(eventInfoWrapper: EventInfoWrapper) {\n    this.replayEventInfoWrappers.push(eventInfoWrapper);\n    if (this.eventReplayScheduled) {\n      return;\n    }\n    this.eventReplayScheduled = true;\n    Promise.resolve().then(() => {\n      this.eventReplayScheduled = false;\n      this.eventReplayer!(this.replayEventInfoWrappers);\n    });\n  }\n}\n\n/**\n * Creates an `EventReplayer` that calls the `replay` function for every `eventInfoWrapper` in\n * the queue.\n */\nexport function createEventReplayer(replay: (eventInfoWrapper: EventInfoWrapper) => void) {\n  return (eventInfoWrappers: EventInfoWrapper[]) => {\n    for (const eventInfoWrapper of eventInfoWrappers) {\n      replay(eventInfoWrapper);\n    }\n  };\n}\n\n/**\n * Returns true if the default action of this event should be prevented before\n * this event is dispatched.\n */\nfunction shouldPreventDefaultBeforeDispatching(\n  actionElement: Element,\n  eventInfoWrapper: EventInfoWrapper,\n): boolean {\n  // Prevent browser from following <a> node links if a jsaction is present\n  // and we are dispatching the action now. Note that the targetElement may be\n  // a child of an anchor that has a jsaction attached. For that reason, we\n  // need to check the actionElement rather than the targetElement.\n  return (\n    actionElement.tagName === 'A' &&\n    (eventInfoWrapper.getEventType() === EventType.CLICK ||\n      eventInfoWrapper.getEventType() === EventType.CLICKMOD)\n  );\n}\n\n/**\n * Registers deferred functionality for an EventContract and a Jsaction\n * Dispatcher.\n */\nexport function registerDispatcher(eventContract: UnrenamedEventContract, dispatcher: Dispatcher) {\n  eventContract.ecrd((eventInfo: EventInfo) => {\n    dispatcher.dispatch(eventInfo);\n  }, Restriction.I_AM_THE_JSACTION_FRAMEWORK);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ActionResolver} from './action_resolver';\nimport {Dispatcher} from './dispatcher';\nimport {EventInfo, EventInfoWrapper} from './event_info';\nimport {isCaptureEventType} from './event_type';\nimport {UnrenamedEventContract} from './eventcontract';\nimport {Restriction} from './restriction';\n\n// Necessary to make the `ngDevMode` global types available.\nimport '../../../src/util/ng_dev_mode';\n\n/**\n * A replayer is a function that is called when there are queued events, from the `EventContract`.\n */\nexport type Replayer = (eventInfoWrappers: Event[]) => void;\n\n/** An internal symbol used to indicate whether propagation should be stopped or not. */\nexport const PROPAGATION_STOPPED_SYMBOL: unique symbol =\n  /* @__PURE__ */ Symbol.for('propagationStopped');\n\n/** Extra event phases beyond what the browser provides. */\nexport const EventPhase = {\n  REPLAY: 101,\n};\n\nconst PREVENT_DEFAULT_ERROR_MESSAGE_DETAILS =\n  ' Because event replay occurs after browser dispatch, `preventDefault` would have no ' +\n  'effect. You can check whether an event is being replayed by accessing the event phase: ' +\n  '`event.eventPhase === EventPhase.REPLAY`.';\nconst PREVENT_DEFAULT_ERROR_MESSAGE = `\\`preventDefault\\` called during event replay.`;\nconst COMPOSED_PATH_ERROR_MESSAGE_DETAILS =\n  ' Because event replay occurs after browser ' +\n  'dispatch, `composedPath()` will be empty. Iterate parent nodes from `event.target` or ' +\n  '`event.currentTarget` if you need to check elements in the event path.';\nconst COMPOSED_PATH_ERROR_MESSAGE = `\\`composedPath\\` called during event replay.`;\n\ndeclare global {\n  interface Event {\n    [PROPAGATION_STOPPED_SYMBOL]?: boolean;\n  }\n}\n\n/**\n * A dispatcher that uses browser-based `Event` semantics, for example bubbling, `stopPropagation`,\n * `currentTarget`, etc.\n */\nexport class EventDispatcher {\n  private readonly actionResolver: ActionResolver;\n\n  private readonly dispatcher: Dispatcher;\n\n  constructor(\n    private readonly dispatchDelegate: (event: Event, actionName: string) => void,\n    private readonly clickModSupport = true,\n  ) {\n    this.actionResolver = new ActionResolver({clickModSupport});\n    this.dispatcher = new Dispatcher(\n      (eventInfoWrapper: EventInfoWrapper) => {\n        this.dispatchToDelegate(eventInfoWrapper);\n      },\n      {\n        actionResolver: this.actionResolver,\n      },\n    );\n  }\n\n  /**\n   * The entrypoint for the `EventContract` dispatch.\n   */\n  dispatch(eventInfo: EventInfo): void {\n    this.dispatcher.dispatch(eventInfo);\n  }\n\n  /** Internal method that does basic disaptching. */\n  private dispatchToDelegate(eventInfoWrapper: EventInfoWrapper) {\n    if (eventInfoWrapper.getIsReplay()) {\n      prepareEventForReplay(eventInfoWrapper);\n    }\n    prepareEventForBubbling(eventInfoWrapper);\n    while (eventInfoWrapper.getAction()) {\n      prepareEventForDispatch(eventInfoWrapper);\n      // If this is a capture event, ONLY dispatch if the action element is the target.\n      if (\n        isCaptureEventType(eventInfoWrapper.getEventType()) &&\n        eventInfoWrapper.getAction()!.element !== eventInfoWrapper.getTargetElement()\n      ) {\n        return;\n      }\n      this.dispatchDelegate(eventInfoWrapper.getEvent(), eventInfoWrapper.getAction()!.name);\n      if (propagationStopped(eventInfoWrapper)) {\n        return;\n      }\n      this.actionResolver.resolveParentAction(eventInfoWrapper.eventInfo);\n    }\n  }\n}\n\nfunction prepareEventForBubbling(eventInfoWrapper: EventInfoWrapper) {\n  const event = eventInfoWrapper.getEvent();\n  const originalStopPropagation = eventInfoWrapper.getEvent().stopPropagation.bind(event);\n  const stopPropagation = () => {\n    event[PROPAGATION_STOPPED_SYMBOL] = true;\n    originalStopPropagation();\n  };\n  patchEventInstance(event, 'stopPropagation', stopPropagation);\n  patchEventInstance(event, 'stopImmediatePropagation', stopPropagation);\n}\n\nfunction propagationStopped(eventInfoWrapper: EventInfoWrapper) {\n  const event = eventInfoWrapper.getEvent();\n  return !!event[PROPAGATION_STOPPED_SYMBOL];\n}\n\nfunction prepareEventForReplay(eventInfoWrapper: EventInfoWrapper) {\n  const event = eventInfoWrapper.getEvent();\n  const target = eventInfoWrapper.getTargetElement();\n  const originalPreventDefault = event.preventDefault.bind(event);\n  patchEventInstance(event, 'target', target);\n  patchEventInstance(event, 'eventPhase', EventPhase.REPLAY);\n  patchEventInstance(event, 'preventDefault', () => {\n    originalPreventDefault();\n    throw new Error(\n      PREVENT_DEFAULT_ERROR_MESSAGE + (ngDevMode ? PREVENT_DEFAULT_ERROR_MESSAGE_DETAILS : ''),\n    );\n  });\n  patchEventInstance(event, 'composedPath', () => {\n    throw new Error(\n      COMPOSED_PATH_ERROR_MESSAGE + (ngDevMode ? COMPOSED_PATH_ERROR_MESSAGE_DETAILS : ''),\n    );\n  });\n}\n\nfunction prepareEventForDispatch(eventInfoWrapper: EventInfoWrapper) {\n  const event = eventInfoWrapper.getEvent();\n  const currentTarget = eventInfoWrapper.getAction()?.element;\n  if (currentTarget) {\n    patchEventInstance(event, 'currentTarget', currentTarget, {\n      // `currentTarget` is going to get reassigned every dispatch.\n      configurable: true,\n    });\n  }\n}\n\n/**\n * Patch `Event` instance during non-standard `Event` dispatch. This patches just the `Event`\n * instance that the browser created, it does not patch global properties or methods.\n *\n * This is necessary because dispatching an `Event` outside of browser dispatch results in\n * incorrect properties and methods that need to be polyfilled or do not work.\n *\n * JSAction dispatch adds two extra \"phases\" to event dispatch:\n * 1. Event delegation - the event is being dispatched by a delegating event handler on a container\n *    (typically `window.document.documentElement`), to a delegated event handler on some child\n *    element. Certain `Event` properties will be unintuitive, such as `currentTarget`, which would\n *    be the container rather than the child element. Bubbling would also not work. In order to\n *    emulate the browser, these properties and methods on the `Event` are patched.\n * 2. Event replay - the event is being dispatched by the framework once the handlers have been\n *    loaded (during hydration, or late-loaded). Certain `Event` properties can be unset by the\n *    browser because the `Event` is no longer actively being dispatched, such as `target`. Other\n *    methods have no effect because the `Event` has already been dispatched, such as\n *    `preventDefault`. Bubbling would also not work. These properties and methods are patched,\n *    either to fill in information that the browser may have removed, or to throw errors in methods\n *    that no longer behave as expected.\n */\nfunction patchEventInstance<T>(\n  event: Event,\n  property: string,\n  value: T,\n  {configurable = false}: {configurable?: boolean} = {},\n) {\n  Object.defineProperty(event, property, {value, configurable});\n}\n\n/**\n * Registers deferred functionality for an EventContract and a Jsaction\n * Dispatcher.\n */\nexport function registerDispatcher(\n  eventContract: UnrenamedEventContract,\n  dispatcher: EventDispatcher,\n) {\n  eventContract.ecrd((eventInfo: EventInfo) => {\n    dispatcher.dispatch(eventInfo);\n  }, Restriction.I_AM_THE_JSACTION_FRAMEWORK);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {createEventInfoFromParameters, EventInfo} from './event_info';\n\nexport declare interface EarlyJsactionDataContainer {\n  _ejsa?: EarlyJsactionData;\n  _ejsas?: {[appId: string]: EarlyJsactionData | undefined};\n}\n\ndeclare global {\n  interface Window {\n    _ejsa?: EarlyJsactionData;\n    _ejsas?: {[appId: string]: EarlyJsactionData | undefined};\n  }\n}\n\n/**\n * Defines the early jsaction data types.\n */\nexport declare interface EarlyJsactionData {\n  /** List used to keep track of the early JSAction event types. */\n  et: string[];\n\n  /** List used to keep track of the early JSAction capture event types. */\n  etc: string[];\n\n  /** Early JSAction handler for all events. */\n  h: (event: Event) => void;\n\n  /** Dispatcher handler. Initializes to populating `q`. */\n  d: (eventInfo: EventInfo) => void;\n\n  /** List used to push `EventInfo` objects if the dispatcher is not registered. */\n  q: EventInfo[];\n\n  /** Container for listening to events. */\n  c: HTMLElement;\n}\n\n/**\n * EarlyEventContract intercepts events in the bubbling phase at the\n * boundary of the document body. This mapping will be passed to the\n * late-loaded EventContract.\n */\nexport class EarlyEventContract {\n  constructor(\n    private readonly dataContainer: EarlyJsactionDataContainer = window,\n    container = window.document.documentElement,\n  ) {\n    dataContainer._ejsa = createEarlyJsactionData(container);\n  }\n\n  /**\n   * Installs a list of event types for container .\n   */\n  addEvents(types: string[], capture?: boolean) {\n    addEvents(this.dataContainer._ejsa!, types, capture);\n  }\n}\n\n/** Creates an `EarlyJsactionData` object. */\nexport function createEarlyJsactionData(container: HTMLElement) {\n  const q: EventInfo[] = [];\n  const d = (eventInfo: EventInfo) => {\n    q.push(eventInfo);\n  };\n  const h = (event: Event) => {\n    d(\n      createEventInfoFromParameters(\n        event.type,\n        event,\n        event.target as Element,\n        container,\n        Date.now(),\n      ),\n    );\n  };\n  return {\n    c: container,\n    q,\n    et: [],\n    etc: [],\n    d,\n    h,\n  };\n}\n\n/** Add all the events to the container stored in the `EarlyJsactionData`. */\nexport function addEvents(\n  earlyJsactionData: EarlyJsactionData,\n  types: string[],\n  capture?: boolean,\n) {\n  for (let i = 0; i < types.length; i++) {\n    const eventType = types[i];\n    const eventTypes = capture ? earlyJsactionData.etc : earlyJsactionData.et;\n    eventTypes.push(eventType);\n    earlyJsactionData.c.addEventListener(eventType, earlyJsactionData.h, capture);\n  }\n}\n\n/** Get the queued `EventInfo` objects that were dispatched before a dispatcher was registered. */\nexport function getQueuedEventInfos(earlyJsactionData: EarlyJsactionData | undefined) {\n  return earlyJsactionData?.q ?? [];\n}\n\n/** Register a different dispatcher function on the `EarlyJsactionData`. */\nexport function registerDispatcher(\n  earlyJsactionData: EarlyJsactionData | undefined,\n  dispatcher: (eventInfo: EventInfo) => void,\n) {\n  if (!earlyJsactionData) {\n    return;\n  }\n  earlyJsactionData.d = dispatcher;\n}\n\n/** Removes all event listener handlers. */\nexport function removeAllEventListeners(earlyJsactionData: EarlyJsactionData | undefined) {\n  if (!earlyJsactionData) {\n    return;\n  }\n  removeEventListeners(earlyJsactionData.c, earlyJsactionData.et, earlyJsactionData.h);\n  removeEventListeners(earlyJsactionData.c, earlyJsactionData.etc, earlyJsactionData.h, true);\n}\n\nfunction removeEventListeners(\n  container: HTMLElement,\n  eventTypes: string[],\n  earlyEventHandler: (e: Event) => void,\n  capture?: boolean,\n) {\n  for (let i = 0; i < eventTypes.length; i++) {\n    container.removeEventListener(eventTypes[i], earlyEventHandler, /* useCapture */ capture);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * @define Support for the non-bubbling mouseenter and mouseleave events.  This\n * flag can be overridden in a build rule.\n */\nexport const MOUSE_SPECIAL_SUPPORT = false;\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * @fileoverview Implements the local event handling contract. This\n * allows DOM objects in a container that enters into this contract to\n * define event handlers which are executed in a local context.\n *\n * One EventContract instance can manage the contract for multiple\n * containers, which are added using the addContainer() method.\n *\n * Events can be registered using the addEvent() method.\n *\n * A Dispatcher is added using the registerDispatcher() method. Until there is\n * a dispatcher, events are queued. The idea is that the EventContract\n * class is inlined in the HTML of the top level page and instantiated\n * right after the start of <body>. The Dispatcher class is contained\n * in the external deferred js, and instantiated and registered with\n * EventContract when the external javascript in the page loads. The\n * external javascript will also register the jsaction handlers, which\n * then pick up the queued events at the time of registration.\n *\n * Since this class is meant to be inlined in the main page HTML, the\n * size of the binary compiled from this file MUST be kept as small as\n * possible and thus its dependencies to a minimum.\n */\n\nimport {\n  EarlyJsactionData,\n  EarlyJsactionDataContainer,\n  removeAllEventListeners,\n} from './earlyeventcontract';\nimport * as eventLib from './event';\nimport {EventContractContainerManager} from './event_contract_container';\nimport {MOUSE_SPECIAL_SUPPORT} from './event_contract_defines';\nimport * as eventInfoLib from './event_info';\nimport {MOUSE_SPECIAL_EVENT_TYPES} from './event_type';\nimport {Restriction} from './restriction';\n\n/**\n * The API of an EventContract that is safe to call from any compilation unit.\n */\nexport declare interface UnrenamedEventContract {\n  // Alias for Jsction EventContract registerDispatcher.\n  ecrd(dispatcher: Dispatcher, restriction: Restriction): void;\n}\n\n/** A function that is called to handle events captured by the EventContract. */\nexport type Dispatcher = (eventInfo: eventInfoLib.EventInfo, globalDispatch?: boolean) => void;\n\n/**\n * A function that handles an event dispatched from the browser.\n *\n * eventType: May differ from `event.type` if JSAction uses a\n * short-hand name or is patching over an non-bubbling event with a bubbling\n * variant.\n * event: The native browser event.\n * container: The container for this dispatch.\n */\ntype EventHandler = (eventType: string, event: Event, container: Element) => void;\n\n/**\n * EventContract intercepts events in the bubbling phase at the\n * boundary of a container element, and maps them to generic actions\n * which are specified using the custom jsaction attribute in\n * HTML. Behavior of the application is then specified in terms of\n * handler for such actions, cf. jsaction.Dispatcher in dispatcher.js.\n *\n * This has several benefits: (1) No DOM event handlers need to be\n * registered on the specific elements in the UI. (2) The set of\n * events that the application has to handle can be specified in terms\n * of the semantics of the application, rather than in terms of DOM\n * events. (3) Invocation of handlers can be delayed and handlers can\n * be delay loaded in a generic way.\n */\nexport class EventContract implements UnrenamedEventContract {\n  static MOUSE_SPECIAL_SUPPORT = MOUSE_SPECIAL_SUPPORT;\n\n  private containerManager: EventContractContainerManager | null;\n\n  /**\n   * The DOM events which this contract covers. Used to prevent double\n   * registration of event types. The value of the map is the\n   * internally created DOM event handler function that handles the\n   * DOM events. See addEvent().\n   *\n   */\n  private eventHandlers: {[key: string]: EventHandler} = {};\n\n  private browserEventTypeToExtraEventTypes: {[key: string]: string[]} = {};\n\n  /**\n   * The dispatcher function. Events are passed to this function for\n   * handling once it was set using the registerDispatcher() method. This is\n   * done because the function is passed from another jsbinary, so passing the\n   * instance and invoking the method here would require to leave the method\n   * unobfuscated.\n   */\n  private dispatcher: Dispatcher | null = null;\n\n  /**\n   * The list of suspended `EventInfo` that will be dispatched\n   * as soon as the `Dispatcher` is registered.\n   */\n  private queuedEventInfos: eventInfoLib.EventInfo[] | null = [];\n\n  constructor(containerManager: EventContractContainerManager) {\n    this.containerManager = containerManager;\n  }\n\n  private handleEvent(eventType: string, event: Event, container: Element) {\n    const eventInfo = eventInfoLib.createEventInfoFromParameters(\n      /* eventType= */ eventType,\n      /* event= */ event,\n      /* targetElement= */ event.target as Element,\n      /* container= */ container,\n      /* timestamp= */ Date.now(),\n    );\n    this.handleEventInfo(eventInfo);\n  }\n\n  /**\n   * Handle an `EventInfo`.\n   */\n  private handleEventInfo(eventInfo: eventInfoLib.EventInfo) {\n    if (!this.dispatcher) {\n      // All events are queued when the dispatcher isn't yet loaded.\n      eventInfoLib.setIsReplay(eventInfo, true);\n      this.queuedEventInfos?.push(eventInfo);\n      return;\n    }\n    this.dispatcher(eventInfo);\n  }\n\n  /**\n   * Enables jsaction handlers to be called for the event type given by\n   * name.\n   *\n   * If the event is already registered, this does nothing.\n   *\n   * @param prefixedEventType If supplied, this event is used in\n   *     the actual browser event registration instead of the name that is\n   *     exposed to jsaction. Use this if you e.g. want users to be able\n   *     to subscribe to jsaction=\"transitionEnd:foo\" while the underlying\n   *     event is webkitTransitionEnd in one browser and mozTransitionEnd\n   *     in another.\n   *\n   * @param passive A boolean value that, if `true`, indicates that the event\n   *     handler will never call `preventDefault()`.\n   */\n  addEvent(eventType: string, prefixedEventType?: string, passive?: boolean) {\n    if (eventType in this.eventHandlers || !this.containerManager) {\n      return;\n    }\n\n    if (!EventContract.MOUSE_SPECIAL_SUPPORT && MOUSE_SPECIAL_EVENT_TYPES.indexOf(eventType) >= 0) {\n      return;\n    }\n\n    const eventHandler = (eventType: string, event: Event, container: Element) => {\n      this.handleEvent(eventType, event, container);\n    };\n\n    // Store the callback to allow us to replay events.\n    this.eventHandlers[eventType] = eventHandler;\n\n    const browserEventType = eventLib.getBrowserEventType(prefixedEventType || eventType);\n\n    if (browserEventType !== eventType) {\n      const eventTypes = this.browserEventTypeToExtraEventTypes[browserEventType] || [];\n      eventTypes.push(eventType);\n      this.browserEventTypeToExtraEventTypes[browserEventType] = eventTypes;\n    }\n\n    this.containerManager.addEventListener(\n      browserEventType,\n      (element: Element) => {\n        return (event: Event) => {\n          eventHandler(eventType, event, element);\n        };\n      },\n      passive,\n    );\n  }\n\n  /**\n   * Gets the queued early events and replay them using the appropriate handler\n   * in the provided event contract. Once all the events are replayed, it cleans\n   * up the early contract.\n   */\n  replayEarlyEvents(earlyJsactionData: EarlyJsactionData | undefined = window._ejsa) {\n    // Check if the early contract is present and prevent calling this function\n    // more than once.\n    if (!earlyJsactionData) {\n      return;\n    }\n\n    // Replay the early contract events.\n    this.replayEarlyEventInfos(earlyJsactionData.q);\n\n    // Clean up the early contract.\n    removeAllEventListeners(earlyJsactionData);\n    delete window._ejsa;\n  }\n\n  /**\n   * Replays all the early `EventInfo` objects, dispatching them through the normal\n   * `EventContract` flow.\n   */\n  replayEarlyEventInfos(earlyEventInfos: eventInfoLib.EventInfo[]) {\n    for (let i = 0; i < earlyEventInfos.length; i++) {\n      const earlyEventInfo: eventInfoLib.EventInfo = earlyEventInfos[i];\n      const eventTypes = this.getEventTypesForBrowserEventType(earlyEventInfo.eventType);\n      for (let j = 0; j < eventTypes.length; j++) {\n        const eventInfo = eventInfoLib.cloneEventInfo(earlyEventInfo);\n        // EventInfo eventType maps to JSAction's internal event type,\n        // rather than the browser event type.\n        eventInfoLib.setEventType(eventInfo, eventTypes[j]);\n        this.handleEventInfo(eventInfo);\n      }\n    }\n  }\n\n  /**\n   * Returns all JSAction event types that have been registered for a given\n   * browser event type.\n   */\n  private getEventTypesForBrowserEventType(browserEventType: string) {\n    const eventTypes = [];\n    if (this.eventHandlers[browserEventType]) {\n      eventTypes.push(browserEventType);\n    }\n    if (this.browserEventTypeToExtraEventTypes[browserEventType]) {\n      eventTypes.push(...this.browserEventTypeToExtraEventTypes[browserEventType]);\n    }\n    return eventTypes;\n  }\n\n  /**\n   * Returns the event handler function for a given event type.\n   */\n  handler(eventType: string): EventHandler | undefined {\n    return this.eventHandlers[eventType];\n  }\n\n  /**\n   * Cleans up the event contract. This resets all of the `EventContract`'s\n   * internal state. Users are responsible for not using this `EventContract`\n   * after it has been cleaned up.\n   */\n  cleanUp() {\n    this.containerManager?.cleanUp();\n    this.containerManager = null;\n    this.eventHandlers = {};\n    this.browserEventTypeToExtraEventTypes = {};\n    this.dispatcher = null;\n    this.queuedEventInfos = [];\n  }\n\n  /**\n   * Register a dispatcher function. Event info of each event mapped to\n   * a jsaction is passed for handling to this callback. The queued\n   * events are passed as well to the dispatcher for later replaying\n   * once the dispatcher is registered. Clears the event queue to null.\n   *\n   * @param dispatcher The dispatcher function.\n   * @param restriction\n   */\n  registerDispatcher(dispatcher: Dispatcher, restriction: Restriction) {\n    this.ecrd(dispatcher, restriction);\n  }\n\n  /**\n   * Unrenamed alias for registerDispatcher. Necessary for any codebases that\n   * split the `EventContract` and `Dispatcher` code into different compilation\n   * units.\n   */\n  ecrd(dispatcher: Dispatcher, restriction: Restriction) {\n    this.dispatcher = dispatcher;\n\n    if (this.queuedEventInfos?.length) {\n      for (let i = 0; i < this.queuedEventInfos.length; i++) {\n        this.handleEventInfo(this.queuedEventInfos[i]);\n      }\n      this.queuedEventInfos = null;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Restriction} from './restriction';\nimport {\n  EarlyJsactionDataContainer,\n  addEvents,\n  createEarlyJsactionData,\n  getQueuedEventInfos,\n  registerDispatcher,\n  removeAllEventListeners,\n} from './earlyeventcontract';\nimport {EventInfo} from './event_info';\n\n/**\n * Creates an `EarlyJsactionData`, adds events to it, and populates it on a nested object on\n * the window.\n */\nexport function bootstrapAppScopedEarlyEventContract(\n  container: HTMLElement,\n  appId: string,\n  bubbleEventTypes: string[],\n  captureEventTypes: string[],\n  dataContainer: EarlyJsactionDataContainer = window,\n) {\n  const earlyJsactionData = createEarlyJsactionData(container);\n  if (!dataContainer._ejsas) {\n    dataContainer._ejsas = {};\n  }\n  dataContainer._ejsas[appId] = earlyJsactionData;\n  addEvents(earlyJsactionData, bubbleEventTypes);\n  addEvents(earlyJsactionData, captureEventTypes, /* capture= */ true);\n}\n\n/** Get the queued `EventInfo` objects that were dispatched before a dispatcher was registered. */\nexport function getAppScopedQueuedEventInfos(\n  appId: string,\n  dataContainer: EarlyJsactionDataContainer = window,\n) {\n  return getQueuedEventInfos(dataContainer._ejsas?.[appId]);\n}\n\n/**\n * Registers a dispatcher function on the `EarlyJsactionData` present on the nested object on the\n * window.\n */\nexport function registerAppScopedDispatcher(\n  restriction: Restriction,\n  appId: string,\n  dispatcher: (eventInfo: EventInfo) => void,\n  dataContainer: EarlyJsactionDataContainer = window,\n) {\n  registerDispatcher(dataContainer._ejsas?.[appId], dispatcher);\n}\n\n/** Removes all event listener handlers. */\nexport function removeAllAppScopedEventListeners(\n  appId: string,\n  dataContainer: EarlyJsactionDataContainer = window,\n) {\n  removeAllEventListeners(dataContainer._ejsas?.[appId]);\n}\n\n/** Clear the early event contract. */\nexport function clearAppScopedEarlyEventContract(\n  appId: string,\n  dataContainer: EarlyJsactionDataContainer = window,\n) {\n  if (!dataContainer._ejsas) {\n    return;\n  }\n  dataContainer._ejsas[appId] = undefined;\n}\n"], "names": ["eventLib.addEventListener", "eventLib.removeEventListener", "eventInfoLib.getEventType", "eventLib.isModifiedClickEvent", "eventInfoLib.getEvent", "eventInfoLib.setEventType", "eventInfoLib.getResolved", "eventInfoLib.getTargetElement", "eventInfoLib.setResolved", "eventInfoLib.getAction", "eventInfoLib.getActionElement", "eventInfoLib.unsetAction", "eventInfoLib.getContainer", "eventLib.isMouseSpecialEvent", "eventLib.createMouseSpecialEvent", "eventInfoLib.setEvent", "eventInfoLib.setTargetElement", "eventInfoLib.setAction", "cache.get", "cache.set", "cache.getParsed", "cache.setParsed", "eventLib.preventDefault", "registerDispatcher", "eventInfoLib.createEventInfoFromParameters", "eventInfoLib.setIsReplay", "eventLib.getBrowserEventType", "eventInfoLib.cloneEventInfo"], "mappings": ";;;;;;AAQa,MAAA,SAAS,GAAG;AACvB;;;;;;;;;;;AAWG;AACH,IAAA,QAAQ,EAAE,UAAmB;;;ACb/B;AACO,MAAM,QAAQ,GAAG;AACtB;;;;;;;AAOG;AACH,IAAA,QAAQ,EAAE,YAAqB;AAC/B;;;;;AAKG;AACH,IAAA,KAAK,EAAE,SAAkB;CAC1B;;AChBD;;AAEG;AACH,MAAM,UAAU,GAAyD,EAAE;AAE3E;;AAEG;AACG,SAAU,GAAG,CAAC,OAAgB,EAAA;AAClC,IAAA,OAAO,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC;AACnC;AAEA;;;AAGG;AACG,SAAU,YAAY,CAAC,OAAgB,EAAA;IAC3C,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;AAChC,IAAA,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC;AACnB,IAAA,OAAO,KAAK;AACd;AAEA;;AAEG;AACa,SAAA,GAAG,CAAC,OAAgB,EAAE,SAA8C,EAAA;AAClF,IAAA,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,SAAS;AACxC;AAEA;;;;;AAKG;AACG,SAAU,SAAS,CAAC,IAAY,EAAA;AACpC,IAAA,OAAO,UAAU,CAAC,IAAI,CAAC;AACzB;AAEA;;;;;AAKG;AACa,SAAA,SAAS,CAAC,IAAY,EAAE,MAA2C,EAAA;AACjF,IAAA,UAAU,CAAC,IAAI,CAAC,GAAG,MAAM;AAC3B;;ACjDA;;;;;;;;AAQG;AACI,MAAM,SAAS,GAAG;AACvB,IAYA;;;;;AAKG;AACH,IAAA,KAAK,EAAE,OAAO;AAEd;;;;;AAKG;AACH,IAAA,QAAQ,EAAE,UAAU;AAEpB,IAOA;;AAEG;AACH,IAAA,QAAQ,EAAE,UAAU;AAEpB;;;;AAIG;AACH,IAAA,KAAK,EAAE,OAAO;AAEd;;;;AAIG;AACH,IAAA,OAAO,EAAE,SAAS;AAElB;;AAEG;AACH,IAAA,IAAI,EAAE,MAAM;AAEZ;;AAEG;AACH,IAAA,QAAQ,EAAE,UAAU;AAEpB;;;;;;AAMG;AACH,IAAA,MAAM,EAAE,QAAQ;AAEhB;;;;AAIG;AACH,IAAA,OAAO,EAAE,SAAS;AAElB;;;AAGG;AACH,IAAA,QAAQ,EAAE,UAAU;AAEpB;;;;AAIG;AACH,IAAA,KAAK,EAAE,OAAO;AAEd,IAcA;;;;AAIG;AACH,IAAA,SAAS,EAAE,WAAW;AAEtB;;;;AAIG;AACH,IAAA,QAAQ,EAAE,UAAU;AAEpB;;;AAGG;AACH,IAAA,UAAU,EAAE,YAAY;AAExB;;;AAGG;AACH,IAAA,UAAU,EAAE,YAAY;AAExB,IAmBA;;;;AAIG;AACH,IAAA,WAAW,EAAE,aAAa;AAE1B;;;;AAIG;AACH,IAAA,UAAU,EAAE,YAAY;AAExB;;;AAGG;AACH,IAAA,YAAY,EAAE,cAAc;AAE5B;;;AAGG;AACH,IAAA,YAAY,EAAE,cAAc;AAE5B,IAwBA;;;;AAIG;AACH,IAAA,KAAK,EAAE,OAAO;AAEd;;;;AAIG;AACH,IAAA,IAAI,EAAE,MAAM;AAEZ,IAKA;;;AAGG;AACH,IAAA,UAAU,EAAE,YAAY;AAExB;;;AAGG;AACH,IAAA,QAAQ,EAAE,UAAU;AAEpB;;;AAGG;AACH,IAAA,SAAS,EAAE,WAAW;AAEtB,IAUA;;;;AAIG;AACH,IAAA,MAAM,EAAE,SAWT;AAED;AACO,MAAM,yBAAyB,GAAG;AACvC,IAAA,SAAS,CAAC,UAAU;AACpB,IAAA,SAAS,CAAC,UAAU;IACpB,cAAc;IACd,cAAc;CACf;AAED;AACO,MAAM,kBAAkB,GAAG;AAChC,IAAA,SAAS,CAAC,KAAK;AACf,IAAA,SAAS,CAAC,QAAQ;AAClB,IAAA,SAAS,CAAC,OAAO;AACjB,IAAA,SAAS,CAAC,QAAQ;AAClB,IAAA,SAAS,CAAC,OAAO;AACjB,IAAA,SAAS,CAAC,KAAK;AACf,IAAA,SAAS,CAAC,QAAQ;AAClB,IAAA,SAAS,CAAC,SAAS;AACnB,IAAA,SAAS,CAAC,QAAQ;AAClB,IAAA,SAAS,CAAC,MAAM;AAChB,IAAA,SAAS,CAAC,UAAU;AACpB,IAAA,SAAS,CAAC,QAAQ;AAClB,IAAA,SAAS,CAAC,SAAS;IACnB,aAAa;IAEb,UAAU;IACV,QAAQ;IACR,kBAAkB;IAClB,mBAAmB;IACnB,gBAAgB;IAChB,aAAa;IACb,OAAO;IACP,QAAQ;IAER,MAAM;IACN,KAAK;IACL,OAAO;IACP,WAAW;IACX,SAAS;IACT,OAAO;IACP,aAAa;IAEb,UAAU;IACV,WAAW;IACX,WAAW;IACX,MAAM;IACN,WAAW;IACX,SAAS;IAET,aAAa;IACb,aAAa;IACb,WAAW;IACX,eAAe;IACf,aAAa;IACb,YAAY;IACZ,mBAAmB;IACnB,oBAAoB;;IAGpB,OAAO;IACP,gBAAgB;;IAGhB,UAAU;IACV,UAAU;IACV,kBAAkB;;IAGlB,aAAa;CACd;AAED;AACO,MAAM,mBAAmB,GAAG;AACjC,IAAA,SAAS,CAAC,KAAK;AACf,IAAA,SAAS,CAAC,IAAI;AACd,IAAA,SAAS,CAAC,KAAK;AACf,IAAA,SAAS,CAAC,IAAI;AACd,IAAA,SAAS,CAAC,MAAM;CACjB;AAED;;;;AAIG;AACU,MAAA,kBAAkB,GAAG,CAAC,SAAiB,KAClD,mBAAmB,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI;AAE5C;AACA,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,MAAM,CAAC,mBAAmB,CAAC;AAExE;;AAEG;AACU,MAAA,gBAAgB,GAAG,CAAC,SAAiB,KAAK,iBAAiB,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI;;AC/W/F;;AAEG;AACG,SAAU,mBAAmB,CAAC,SAAiB,EAAA;;;;;;AAMnD,IAAA,IAAI,SAAS,KAAK,SAAS,CAAC,UAAU,EAAE;QACtC,OAAO,SAAS,CAAC,SAAS;;AACrB,SAAA,IAAI,SAAS,KAAK,SAAS,CAAC,UAAU,EAAE;QAC7C,OAAO,SAAS,CAAC,QAAQ;;AACpB,SAAA,IAAI,SAAS,KAAK,SAAS,CAAC,YAAY,EAAE;QAC/C,OAAO,SAAS,CAAC,WAAW;;AACvB,SAAA,IAAI,SAAS,KAAK,SAAS,CAAC,YAAY,EAAE;QAC/C,OAAO,SAAS,CAAC,UAAU;;AAE7B,IAAA,OAAO,SAAS;AAClB;AAEA;;;;;;;;;;AAUG;AACG,SAAU,gBAAgB,CAC9B,OAAgB,EAChB,SAAiB,EACjB,OAA+B,EAC/B,OAAiB,EAAA;;;;;;;;;;;;;;;IAgBjB,IAAI,OAAO,GAAG,KAAK;AAEnB,IAAA,IAAI,kBAAkB,CAAC,SAAS,CAAC,EAAE;QACjC,OAAO,GAAG,IAAI;;AAGhB,IAAA,MAAM,OAAO,GAAG,OAAO,OAAO,KAAK,SAAS,GAAG,EAAC,OAAO,EAAE,OAAO,EAAC,GAAG,OAAO;IAC3E,OAAO,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC;IAErD,OAAO,EAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAC;AAC/C;AAEA;;;;;;;AAOG;AACa,SAAA,mBAAmB,CAAC,OAAgB,EAAE,IAAsB,EAAA;AAC1E,IAAA,IAAI,OAAO,CAAC,mBAAmB,EAAE;;;;QAI/B,MAAM,OAAO,GAAG,OAAO,IAAI,CAAC,OAAO,KAAK,SAAS,GAAG,EAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAC,GAAG,IAAI,CAAC,OAAO;AAC1F,QAAA,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAwB,EAAE,OAAO,CAAC;;;AAE9E,SAAA,IAAK,OAAe,CAAC,WAAW,EAAE;;AAEtC,QAAA,OAAe,CAAC,WAAW,CAAC,CAAA,EAAA,EAAK,IAAI,CAAC,SAAS,CAAA,CAAE,EAAE,IAAI,CAAC,OAAO,CAAC;;AAErE;AAUA;;;AAGG;AACG,SAAU,cAAc,CAAC,CAAQ,EAAA;AACrC,IAAA,CAAC,CAAC,cAAc,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC;AACjE;AAqBA;;AAEG;AACH,IAAI,KAAK,GAAY,OAAO,SAAS,KAAK,WAAW,IAAI,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;AAE9F;;;;;AAKG;AACH,SAAS,aAAa,CAAC,CAAQ,EAAA;IAC7B;;IAEG,CAAS,CAAC,KAAK,KAAK,CAAC;;AAEtB,SAAE,CAAS,CAAC,KAAK,IAAI,IAAI;;AAEtB,YAAA,CAAS,CAAC,MAAM,KAAK,CAAC,CAAC;;AAE9B;AAEA;;;;;;AAMG;AACG,SAAU,oBAAoB,CAAC,CAAQ,EAAA;IAC3C;;AAEE,IAAA,CAAC,KAAK,IAAK,CAAS,CAAC,OAAO;;AAE5B,SAAC,CAAC,KAAK,IAAK,CAAS,CAAC,OAAO,CAAC;QAC9B,aAAa,CAAC,CAAC,CAAC;;QAEf,CAAS,CAAC,QAAQ;AAEvB;AAqMA;;;;;;;;;;;;;;;;;AAiBG;SACa,mBAAmB,CAAC,CAAQ,EAAE,IAAY,EAAE,OAAgB,EAAA;;AAE1E,IAAA,MAAM,OAAO,GAAI,CAAS,CAAC,aAAqB;AAEhD,IAAA,QACE,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,SAAS,IAAI,IAAI,KAAK,SAAS,CAAC,UAAU;AAC/D,SAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,QAAQ,IAAI,IAAI,KAAK,SAAS,CAAC,UAAU,CAAC;AAChE,SAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,WAAW,IAAI,IAAI,KAAK,SAAS,CAAC,YAAY,CAAC;AACrE,SAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,UAAU,IAAI,IAAI,KAAK,SAAS,CAAC,YAAY,CAAC;AACtE,SAAC,CAAC,OAAO,KAAK,OAAO,KAAK,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;AAErE;AAEA;;;;;;;;AAQG;AACa,SAAA,uBAAuB,CAAC,CAAQ,EAAE,MAAe,EAAA;;;;;;;;;;;IAW/D,MAAM,IAAI,GAA2E,EAAE;AACvF,IAAA,KAAK,MAAM,QAAQ,IAAI,CAAC,EAAE;QACxB,IAAI,QAAQ,KAAK,YAAY,IAAI,QAAQ,KAAK,QAAQ,EAAE;YACtD;;QAEF,MAAM,GAAG,GAAG,QAAuB;;AAEnC,QAAA,MAAM,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;AACpB,QAAA,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;YAC/B;;;;AAIF,QAAA,IAAI,CAAC,GAAG,CAAC,GAAG,KAAY;;IAE1B,IAAI,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,SAAS,EAAE;AAClC,QAAA,IAAI,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,UAAU;;SAC9B,IAAI,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,QAAQ,EAAE;AACxC,QAAA,IAAI,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,UAAU;;SAC9B,IAAI,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,WAAW,EAAE;AAC3C,QAAA,IAAI,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,YAAY;;SAChC;AACL,QAAA,IAAI,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,YAAY;;IAEvC,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,MAAM;AAC5C,IAAA,IAAI,CAAC,SAAS,CAAC,GAAG,KAAK;AACvB,IAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC;AAC1B,IAAA,OAAO,IAAa;AACtB;;ACvaA;;AAEG;AACH,MAAM,KAAK,GAAG,OAAO,SAAS,KAAK,WAAW,IAAI,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;AAE9F;;;;AAIG;MACU,sBAAsB,CAAA;AAWZ,IAAA,OAAA;AAVrB;;;;AAIG;IACK,YAAY,GAAuB,EAAE;AAE7C;;AAEG;AACH,IAAA,WAAA,CAAqB,OAAgB,EAAA;QAAhB,IAAO,CAAA,OAAA,GAAP,OAAO;;AAE5B;;;;AAIG;AACH,IAAA,gBAAgB,CACd,SAAiB,EACjB,UAAwD,EACxD,OAAiB,EAAA;;;;;;;;;;;;QAajB,IAAI,KAAK,EAAE;YACR,IAAI,CAAC,OAAuB,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS;;QAExD,IAAI,CAAC,YAAY,CAAC,IAAI,CACpBA,gBAAyB,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,CACtF;;AAGH;;AAEG;IACH,OAAO,GAAA;AACL,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjD,YAAAC,mBAA4B,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;;AAGlE,QAAA,IAAI,CAAC,YAAY,GAAG,EAAE;;AAEzB;;AC/EM,MAAM,IAAI,GAAG;AAClB,IAMA;;;AAGG;AACH,IAAA,sBAAsB,EAAE,GAAY;CACrC;;ACwCD;AACM,SAAU,YAAY,CAAC,SAAoB,EAAA;IAC/C,OAAO,SAAS,CAAC,SAAS;AAC5B;AAEA;AACgB,SAAA,YAAY,CAAC,SAAoB,EAAE,SAAiB,EAAA;AAClE,IAAA,SAAS,CAAC,SAAS,GAAG,SAAS;AACjC;AAEA;AACM,SAAU,QAAQ,CAAC,SAAoB,EAAA;IAC3C,OAAO,SAAS,CAAC,KAAK;AACxB;AAEA;AACgB,SAAA,QAAQ,CAAC,SAAoB,EAAE,KAAY,EAAA;AACzD,IAAA,SAAS,CAAC,KAAK,GAAG,KAAK;AACzB;AAEA;AACM,SAAU,gBAAgB,CAAC,SAAoB,EAAA;IACnD,OAAO,SAAS,CAAC,aAAa;AAChC;AAEA;AACgB,SAAA,gBAAgB,CAAC,SAAoB,EAAE,aAAsB,EAAA;AAC3E,IAAA,SAAS,CAAC,aAAa,GAAG,aAAa;AACzC;AAEA;AACM,SAAU,YAAY,CAAC,SAAoB,EAAA;IAC/C,OAAO,SAAS,CAAC,GAAG;AACtB;AAEA;AACgB,SAAA,YAAY,CAAC,SAAoB,EAAE,SAAkB,EAAA;AACnE,IAAA,SAAS,CAAC,GAAG,GAAG,SAAS;AAC3B;AAEA;AACM,SAAU,YAAY,CAAC,SAAoB,EAAA;IAC/C,OAAO,SAAS,CAAC,SAAS;AAC5B;AAEA;AACgB,SAAA,YAAY,CAAC,SAAoB,EAAE,SAAiB,EAAA;AAClE,IAAA,SAAS,CAAC,SAAS,GAAG,SAAS;AACjC;AAEA;AACM,SAAU,SAAS,CAAC,SAAoB,EAAA;IAC5C,OAAO,SAAS,CAAC,GAAG;AACtB;AAEA;SACgB,SAAS,CAAC,SAAoB,EAAE,UAAkB,EAAE,aAAsB,EAAA;IACxF,SAAS,CAAC,GAAG,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC;AAC7C;AAEA;AACM,SAAU,WAAW,CAAC,SAAoB,EAAA;AAC9C,IAAA,SAAS,CAAC,GAAG,GAAG,SAAS;AAC3B;AAOA;AACM,SAAU,gBAAgB,CAAC,UAA8B,EAAA;AAC7D,IAAA,OAAO,UAAU,CAAC,CAAC,CAAC;AACtB;AAEA;AACM,SAAU,WAAW,CAAC,SAAoB,EAAA;IAC9C,OAAO,SAAS,CAAC,IAAI;AACvB;AAEA;AACgB,SAAA,WAAW,CAAC,SAAoB,EAAE,MAAe,EAAA;AAC/D,IAAA,SAAS,CAAC,IAAI,GAAG,MAAM;AACzB;AAYA;AACM,SAAU,WAAW,CAAC,SAAoB,EAAA;IAC9C,OAAO,SAAS,CAAC,GAAG;AACtB;AAEA;AACgB,SAAA,WAAW,CAAC,SAAoB,EAAE,QAAiB,EAAA;AACjE,IAAA,SAAS,CAAC,GAAG,GAAG,QAAQ;AAC1B;AAEA;AACM,SAAU,cAAc,CAAC,SAAoB,EAAA;IACjD,OAAO;QACL,SAAS,EAAE,SAAS,CAAC,SAAS;QAC9B,KAAK,EAAE,SAAS,CAAC,KAAK;QACtB,aAAa,EAAE,SAAS,CAAC,aAAa;QACtC,GAAG,EAAE,SAAS,CAAC,GAAG;QAClB,GAAG,EAAE,SAAS,CAAC,GAAG;QAClB,SAAS,EAAE,SAAS,CAAC,SAAS;QAC9B,IAAI,EAAE,SAAS,CAAC,IAAI;QACpB,KAAK,EAAE,SAAS,CAAC,KAAK;QACtB,GAAG,EAAE,SAAS,CAAC,GAAG;KACnB;AACH;AAEA;;;;;AAKG;SACa,6BAA6B,CAC3C,SAAiB,EACjB,KAAY,EACZ,aAAsB,EACtB,SAAkB,EAClB,SAAiB,EACjB,MAA2B,EAC3B,QAAkB,EAClB,YAAsB,EAAA;IAEtB,OAAO;QACL,SAAS;QACT,KAAK;QACL,aAAa;AACb,QAAA,GAAG,EAAE,SAAS;AACd,QAAA,SAAS,EAAE,SAAS;AACpB,QAAA,GAAG,EAAE,MAAM;AACX,QAAA,IAAI,EAAE,QAAQ;AACd,QAAA,KAAK,EAAE,YAAY;KACpB;AACH;AAuCA;;;;;AAKG;MACU,gBAAgB,CAAA;AACN,IAAA,SAAA;AAArB,IAAA,WAAA,CAAqB,SAAoB,EAAA;QAApB,IAAS,CAAA,SAAA,GAAT,SAAS;;IAE9B,YAAY,GAAA;AACV,QAAA,OAAO,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC;;AAGrC,IAAA,YAAY,CAAC,SAAiB,EAAA;AAC5B,QAAA,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC;;IAGzC,QAAQ,GAAA;AACN,QAAA,OAAO,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC;;AAGjC,IAAA,QAAQ,CAAC,KAAY,EAAA;AACnB,QAAA,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC;;IAGjC,gBAAgB,GAAA;AACd,QAAA,OAAO,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC;;AAGzC,IAAA,gBAAgB,CAAC,aAAsB,EAAA;AACrC,QAAA,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,CAAC;;IAGjD,YAAY,GAAA;AACV,QAAA,OAAO,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC;;AAGrC,IAAA,YAAY,CAAC,SAAkB,EAAA;AAC7B,QAAA,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC;;IAEzC,YAAY,GAAA;AACV,QAAA,OAAO,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC;;AAGrC,IAAA,YAAY,CAAC,SAAiB,EAAA;AAC5B,QAAA,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC;;IAGzC,SAAS,GAAA;QACP,MAAM,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC;AACxC,QAAA,IAAI,CAAC,MAAM;AAAE,YAAA,OAAO,SAAS;QAC7B,OAAO;AACL,YAAA,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;AACf,YAAA,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;SACnB;;AAGH,IAAA,SAAS,CAAC,MAA8B,EAAA;QACtC,IAAI,CAAC,MAAM,EAAE;AACX,YAAA,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC;YAC3B;;AAEF,QAAA,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC;;IAGxD,WAAW,GAAA;AACT,QAAA,OAAO,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC;;AAGpC,IAAA,WAAW,CAAC,MAAe,EAAA;AACzB,QAAA,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC;;IAGrC,WAAW,GAAA;AACT,QAAA,OAAO,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC;;AAGpC,IAAA,WAAW,CAAC,QAAiB,EAAA;AAC3B,QAAA,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC;;IAGvC,KAAK,GAAA;QACH,OAAO,IAAI,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;;AAE9D;;ACxTD;;;AAGG;AACH,MAAM,gBAAgB,GAA4B,EAAE;AAEpD;;AAEG;AACH,MAAM,gBAAgB,GAAG,SAAS;AAElC;AACA,MAAM,kBAAkB,GAAW,SAAS,CAAC,KAAK;AAElD;MACa,cAAc,CAAA;IACjB,gBAAgB,GAAY,KAAK;IACjC,eAAe,GAAY,IAAI;AACtB,IAAA,0BAA0B;IAEnC,2BAA2B,GAAiD,SAAS;IAErF,0BAA0B,GAAiD,SAAS;IAEpF,uBAAuB,GAInB,SAAS;IAErB,WAAY,CAAA,EACV,0BAA0B,GAAG,KAAK,EAClC,eAAe,GAAG,IAAI,GAAA,GAIpB,EAAE,EAAA;AACJ,QAAA,IAAI,CAAC,0BAA0B,GAAG,0BAA0B;AAC5D,QAAA,IAAI,CAAC,eAAe,GAAG,eAAe;;AAGxC,IAAA,gBAAgB,CAAC,SAAiC,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAkChD,IACE,IAAI,CAAC,eAAe;YACpBC,YAAyB,CAAC,SAAS,CAAC,KAAK,SAAS,CAAC,KAAK;YACxDC,oBAA6B,CAACC,QAAqB,CAAC,SAAS,CAAC,CAAC,EAC/D;YACAC,YAAyB,CAAC,SAAS,EAAE,SAAS,CAAC,QAAQ,CAAC;;AACnD,aAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AAChC,YAAA,IAAI,CAAC,2BAA4B,CAAC,SAAS,CAAC;;;AAIhD,IAAA,aAAa,CAAC,SAAiC,EAAA;AAC7C,QAAA,IAAIC,WAAwB,CAAC,SAAS,CAAC,EAAE;YACvC;;AAEF,QAAA,IAAI,CAAC,cAAc,CAAC,SAAS,EAAEC,gBAA6B,CAAC,SAAS,CAAC,CAAC;AACxE,QAAAC,WAAwB,CAAC,SAAS,EAAE,IAAI,CAAC;;AAG3C,IAAA,mBAAmB,CAAC,SAAiC,EAAA;QACnD,MAAM,MAAM,GAAGC,SAAsB,CAAC,SAAS,CAAC;QAChD,MAAM,aAAa,GAAG,MAAM,IAAIC,gBAA6B,CAAC,MAAM,CAAC;AACrE,QAAAC,WAAwB,CAAC,SAAS,CAAC;QACnC,MAAM,UAAU,GAAG,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;QACrE,IAAI,CAAC,UAAU,EAAE;YACf;;AAEF,QAAA,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,UAAU,CAAC;;AAG5C;;;;;;;;;;;AAWG;IACK,cAAc,CAAC,SAAiC,EAAE,aAAsB,EAAA;QAC9E,IAAI,aAAa,GAAmB,aAAa;QACjD,OAAO,aAAa,IAAI,aAAa,KAAKC,YAAyB,CAAC,SAAS,CAAC,EAAE;YAC9E,IAAI,aAAa,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY,EAAE;AAChD,gBAAA,IAAI,CAAC,uBAAuB,CAAC,aAAa,EAAE,SAAS,CAAC;;AAGxD,YAAA,IAAIH,SAAsB,CAAC,SAAS,CAAC,EAAE;;;;gBAIrC;;AAEF,YAAA,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;;QAGnD,MAAM,MAAM,GAAGA,SAAsB,CAAC,SAAS,CAAC;QAChD,IAAI,CAAC,MAAM,EAAE;;YAEX;;AAGF,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,YAAA,IAAI,CAAC,0BAA2B,CAAC,SAAS,CAAC;;;;;AAM7C,QAAA,IAAI,IAAI,CAAC,0BAA0B,EAAE;YACnC,IACEP,YAAyB,CAAC,SAAS,CAAC,KAAK,SAAS,CAAC,UAAU;gBAC7DA,YAAyB,CAAC,SAAS,CAAC,KAAK,SAAS,CAAC,UAAU;gBAC7DA,YAAyB,CAAC,SAAS,CAAC,KAAK,SAAS,CAAC,YAAY;gBAC/DA,YAAyB,CAAC,SAAS,CAAC,KAAK,SAAS,CAAC,YAAY,EAC/D;;;;gBAIA,IACEW,mBAA4B,CAC1BT,QAAqB,CAAC,SAAS,CAAC,EAChCF,YAAyB,CAAC,SAAS,CAAC,EACpCQ,gBAA6B,CAAC,MAAM,CAAC,CACtC,EACD;;;;;;oBAMA,MAAM,WAAW,GAAGI,uBAAgC,CAClDV,QAAqB,CAAC,SAAS,CAAC,EAChCM,gBAA6B,CAAC,MAAM,CAAC,CACtC;AACD,oBAAAK,QAAqB,CAAC,SAAS,EAAE,WAAW,CAAC;;;;AAI7C,oBAAAC,gBAA6B,CAAC,SAAS,EAAEN,gBAA6B,CAAC,MAAM,CAAC,CAAC;;qBAC1E;AACL,oBAAAC,WAAwB,CAAC,SAAS,CAAC;;;;;AAM3C;;;;AAIG;AACK,IAAA,aAAa,CAAC,OAAgB,EAAA;QACpC,MAAM,KAAK,GAAG,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC;QACrC,IAAI,KAAK,EAAE;AACT,YAAA,OAAO,KAAgB;;AAEzB,QAAA,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU;AACrC,QAAA,IAAI,UAAU,EAAE,QAAQ,KAAK,oBAAoB,EAAE;AACjD,YAAA,OAAQ,UAAgC,EAAE,IAAI,IAAI,IAAI;;AAExD,QAAA,OAAO,UAA4B;;AAGrC;;;;;;;;;;AAUG;IACK,uBAAuB,CAAC,aAAsB,EAAE,SAAiC,EAAA;QACvF,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;QAElD,MAAM,UAAU,GAAG,SAAS,CAACT,YAAyB,CAAC,SAAS,CAAC,CAAC;AAClE,QAAA,IAAI,UAAU,KAAK,SAAS,EAAE;YAC5Be,SAAsB,CAAC,SAAS,EAAE,UAAU,EAAE,aAAa,CAAC;;AAG9D,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,uBAAwB,CAAC,aAAa,EAAE,SAAS,EAAE,SAAS,CAAC;;;AAItE;;;;;;;AAOG;AACK,IAAA,YAAY,CAAC,aAAsB,EAAA;QACzC,IAAI,SAAS,GAAoDC,GAAS,CAAC,aAAa,CAAC;QACzF,IAAI,CAAC,SAAS,EAAE;YACd,MAAM,iBAAiB,GAAG,aAAa,CAAC,YAAY,CAAC,SAAS,CAAC,QAAQ,CAAC;YACxE,IAAI,CAAC,iBAAiB,EAAE;gBACtB,SAAS,GAAG,gBAAgB;AAC5B,gBAAAC,GAAS,CAAC,aAAa,EAAE,SAAS,CAAC;;iBAC9B;AACL,gBAAA,SAAS,GAAGC,SAAe,CAAC,iBAAiB,CAAC;gBAC9C,IAAI,CAAC,SAAS,EAAE;oBACd,SAAS,GAAG,EAAE;oBACd,MAAM,MAAM,GAAG,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC;AACxD,oBAAA,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;AAC5C,wBAAA,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC;wBACzB,IAAI,CAAC,KAAK,EAAE;4BACV;;wBAEF,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC;AACxD,wBAAA,MAAM,QAAQ,GAAG,KAAK,KAAK,EAAE;wBAC7B,MAAM,IAAI,GAAG,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,IAAI,EAAE,GAAG,kBAAkB;wBAC1E,MAAM,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,KAAK;AAChE,wBAAA,SAAS,CAAC,IAAI,CAAC,GAAG,MAAM;;AAE1B,oBAAAC,SAAe,CAAC,iBAAiB,EAAE,SAAS,CAAC;;AAE/C,gBAAAF,GAAS,CAAC,aAAa,EAAE,SAAS,CAAC;;;AAGvC,QAAA,OAAO,SAAS;;AAGlB,IAAA,mBAAmB,CACjB,2BAAyE,EACzE,0BAAuE,EACvE,uBAAiE,EAAA;AAEjE,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;AAC5B,QAAA,IAAI,CAAC,2BAA2B,GAAG,2BAA2B;AAC9D,QAAA,IAAI,CAAC,0BAA0B,GAAG,0BAA0B;AAC5D,QAAA,IAAI,CAAC,uBAAuB,GAAG,uBAAuB;;AAEzD;;AC3RD;;AAEG;AAEH,IAAY,WAEX;AAFD,CAAA,UAAY,WAAW,EAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,6BAAA,CAAA,GAAA,CAAA,CAAA,GAAA,6BAA2B;AAC7B,CAAC,EAFW,WAAW,KAAX,WAAW,GAEtB,EAAA,CAAA,CAAA;;ACOD;;;;AAIG;MACU,UAAU,CAAA;AAqBF,IAAA,gBAAA;;AAnBX,IAAA,cAAc;;AAGd,IAAA,aAAa;;IAGb,oBAAoB,GAAG,KAAK;;IAGnB,uBAAuB,GAAuB,EAAE;AAEjE;;;;;;AAMG;AACH,IAAA,WAAA,CACmB,gBAA8D,EAC/E,EACE,cAAc,EACd,aAAa,MACkD,EAAE,EAAA;QAJlD,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB;AAMjC,QAAA,IAAI,CAAC,cAAc,GAAG,cAAc;AACpC,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa;;AAGpC;;;;;;;;;;;;;;;;;;;AAmBG;AACH,IAAA,QAAQ,CAAC,SAAoB,EAAA;AAC3B,QAAA,MAAM,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,SAAS,CAAC;AACxD,QAAA,IAAI,CAAC,cAAc,EAAE,gBAAgB,CAAC,SAAS,CAAC;AAChD,QAAA,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,SAAS,CAAC;AAC7C,QAAA,MAAM,MAAM,GAAG,gBAAgB,CAAC,SAAS,EAAE;QAC3C,IAAI,MAAM,IAAI,qCAAqC,CAAC,MAAM,CAAC,OAAO,EAAE,gBAAgB,CAAC,EAAE;YACrFG,cAAuB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;;QAEtD,IAAI,IAAI,CAAC,aAAa,IAAI,gBAAgB,CAAC,WAAW,EAAE,EAAE;AACxD,YAAA,IAAI,CAAC,8BAA8B,CAAC,gBAAgB,CAAC;YACrD;;AAEF,QAAA,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC;;AAGzC;;;;AAIG;AACK,IAAA,8BAA8B,CAAC,gBAAkC,EAAA;AACvE,QAAA,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,gBAAgB,CAAC;AACnD,QAAA,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B;;AAEF,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI;AAChC,QAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;AAC1B,YAAA,IAAI,CAAC,oBAAoB,GAAG,KAAK;AACjC,YAAA,IAAI,CAAC,aAAc,CAAC,IAAI,CAAC,uBAAuB,CAAC;AACnD,SAAC,CAAC;;AAEL;AAcD;;;AAGG;AACH,SAAS,qCAAqC,CAC5C,aAAsB,EACtB,gBAAkC,EAAA;;;;;AAMlC,IAAA,QACE,aAAa,CAAC,OAAO,KAAK,GAAG;AAC7B,SAAC,gBAAgB,CAAC,YAAY,EAAE,KAAK,SAAS,CAAC,KAAK;YAClD,gBAAgB,CAAC,YAAY,EAAE,KAAK,SAAS,CAAC,QAAQ,CAAC;AAE7D;;ACpHA;AACO,MAAM,0BAA0B;AACrC,gBAAgB,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC;AAElD;AACa,MAAA,UAAU,GAAG;AACxB,IAAA,MAAM,EAAE,GAAG;;AAGb,MAAM,qCAAqC,GACzC,sFAAsF;IACtF,yFAAyF;AACzF,IAAA,2CAA2C;AAC7C,MAAM,6BAA6B,GAAG,CAAA,8CAAA,CAAgD;AACtF,MAAM,mCAAmC,GACvC,6CAA6C;IAC7C,wFAAwF;AACxF,IAAA,wEAAwE;AAC1E,MAAM,2BAA2B,GAAG,CAAA,4CAAA,CAA8C;AAQlF;;;AAGG;MACU,eAAe,CAAA;AAMP,IAAA,gBAAA;AACA,IAAA,eAAA;AANF,IAAA,cAAc;AAEd,IAAA,UAAU;IAE3B,WACmB,CAAA,gBAA4D,EAC5D,eAAA,GAAkB,IAAI,EAAA;QADtB,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB;QAChB,IAAe,CAAA,eAAA,GAAf,eAAe;QAEhC,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,CAAC,EAAC,eAAe,EAAC,CAAC;QAC3D,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU,CAC9B,CAAC,gBAAkC,KAAI;AACrC,YAAA,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC;AAC3C,SAAC,EACD;YACE,cAAc,EAAE,IAAI,CAAC,cAAc;AACpC,SAAA,CACF;;AAGH;;AAEG;AACH,IAAA,QAAQ,CAAC,SAAoB,EAAA;AAC3B,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC;;;AAI7B,IAAA,kBAAkB,CAAC,gBAAkC,EAAA;AAC3D,QAAA,IAAI,gBAAgB,CAAC,WAAW,EAAE,EAAE;YAClC,qBAAqB,CAAC,gBAAgB,CAAC;;QAEzC,uBAAuB,CAAC,gBAAgB,CAAC;AACzC,QAAA,OAAO,gBAAgB,CAAC,SAAS,EAAE,EAAE;YACnC,uBAAuB,CAAC,gBAAgB,CAAC;;AAEzC,YAAA,IACE,kBAAkB,CAAC,gBAAgB,CAAC,YAAY,EAAE,CAAC;gBACnD,gBAAgB,CAAC,SAAS,EAAG,CAAC,OAAO,KAAK,gBAAgB,CAAC,gBAAgB,EAAE,EAC7E;gBACA;;AAEF,YAAA,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,EAAE,gBAAgB,CAAC,SAAS,EAAG,CAAC,IAAI,CAAC;AACtF,YAAA,IAAI,kBAAkB,CAAC,gBAAgB,CAAC,EAAE;gBACxC;;YAEF,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,SAAS,CAAC;;;AAGxE;AAED,SAAS,uBAAuB,CAAC,gBAAkC,EAAA;AACjE,IAAA,MAAM,KAAK,GAAG,gBAAgB,CAAC,QAAQ,EAAE;AACzC,IAAA,MAAM,uBAAuB,GAAG,gBAAgB,CAAC,QAAQ,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC;IACvF,MAAM,eAAe,GAAG,MAAK;AAC3B,QAAA,KAAK,CAAC,0BAA0B,CAAC,GAAG,IAAI;AACxC,QAAA,uBAAuB,EAAE;AAC3B,KAAC;AACD,IAAA,kBAAkB,CAAC,KAAK,EAAE,iBAAiB,EAAE,eAAe,CAAC;AAC7D,IAAA,kBAAkB,CAAC,KAAK,EAAE,0BAA0B,EAAE,eAAe,CAAC;AACxE;AAEA,SAAS,kBAAkB,CAAC,gBAAkC,EAAA;AAC5D,IAAA,MAAM,KAAK,GAAG,gBAAgB,CAAC,QAAQ,EAAE;AACzC,IAAA,OAAO,CAAC,CAAC,KAAK,CAAC,0BAA0B,CAAC;AAC5C;AAEA,SAAS,qBAAqB,CAAC,gBAAkC,EAAA;AAC/D,IAAA,MAAM,KAAK,GAAG,gBAAgB,CAAC,QAAQ,EAAE;AACzC,IAAA,MAAM,MAAM,GAAG,gBAAgB,CAAC,gBAAgB,EAAE;IAClD,MAAM,sBAAsB,GAAG,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;AAC/D,IAAA,kBAAkB,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC;IAC3C,kBAAkB,CAAC,KAAK,EAAE,YAAY,EAAE,UAAU,CAAC,MAAM,CAAC;AAC1D,IAAA,kBAAkB,CAAC,KAAK,EAAE,gBAAgB,EAAE,MAAK;AAC/C,QAAA,sBAAsB,EAAE;AACxB,QAAA,MAAM,IAAI,KAAK,CACb,6BAA6B,IAAI,SAAS,GAAG,qCAAqC,GAAG,EAAE,CAAC,CACzF;AACH,KAAC,CAAC;AACF,IAAA,kBAAkB,CAAC,KAAK,EAAE,cAAc,EAAE,MAAK;AAC7C,QAAA,MAAM,IAAI,KAAK,CACb,2BAA2B,IAAI,SAAS,GAAG,mCAAmC,GAAG,EAAE,CAAC,CACrF;AACH,KAAC,CAAC;AACJ;AAEA,SAAS,uBAAuB,CAAC,gBAAkC,EAAA;AACjE,IAAA,MAAM,KAAK,GAAG,gBAAgB,CAAC,QAAQ,EAAE;IACzC,MAAM,aAAa,GAAG,gBAAgB,CAAC,SAAS,EAAE,EAAE,OAAO;IAC3D,IAAI,aAAa,EAAE;AACjB,QAAA,kBAAkB,CAAC,KAAK,EAAE,eAAe,EAAE,aAAa,EAAE;;AAExD,YAAA,YAAY,EAAE,IAAI;AACnB,SAAA,CAAC;;AAEN;AAEA;;;;;;;;;;;;;;;;;;;;AAoBG;AACH,SAAS,kBAAkB,CACzB,KAAY,EACZ,QAAgB,EAChB,KAAQ,EACR,EAAC,YAAY,GAAG,KAAK,KAA8B,EAAE,EAAA;AAErD,IAAA,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAC,KAAK,EAAE,YAAY,EAAC,CAAC;AAC/D;AAEA;;;AAGG;AACa,SAAAC,oBAAkB,CAChC,aAAqC,EACrC,UAA2B,EAAA;AAE3B,IAAA,aAAa,CAAC,IAAI,CAAC,CAAC,SAAoB,KAAI;AAC1C,QAAA,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC;AAChC,KAAC,EAAE,WAAW,CAAC,2BAA2B,CAAC;AAC7C;;AC7HA;AACM,SAAU,uBAAuB,CAAC,SAAsB,EAAA;IAC5D,MAAM,CAAC,GAAgB,EAAE;AACzB,IAAA,MAAM,CAAC,GAAG,CAAC,SAAoB,KAAI;AACjC,QAAA,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;AACnB,KAAC;AACD,IAAA,MAAM,CAAC,GAAG,CAAC,KAAY,KAAI;QACzB,CAAC,CACC,6BAA6B,CAC3B,KAAK,CAAC,IAAI,EACV,KAAK,EACL,KAAK,CAAC,MAAiB,EACvB,SAAS,EACT,IAAI,CAAC,GAAG,EAAE,CACX,CACF;AACH,KAAC;IACD,OAAO;AACL,QAAA,CAAC,EAAE,SAAS;QACZ,CAAC;AACD,QAAA,EAAE,EAAE,EAAE;AACN,QAAA,GAAG,EAAE,EAAE;QACP,CAAC;QACD,CAAC;KACF;AACH;AAEA;SACgB,SAAS,CACvB,iBAAoC,EACpC,KAAe,EACf,OAAiB,EAAA;AAEjB,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACrC,QAAA,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC;AAC1B,QAAA,MAAM,UAAU,GAAG,OAAO,GAAG,iBAAiB,CAAC,GAAG,GAAG,iBAAiB,CAAC,EAAE;AACzE,QAAA,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC;AAC1B,QAAA,iBAAiB,CAAC,CAAC,CAAC,gBAAgB,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC,EAAE,OAAO,CAAC;;AAEjF;AAEA;AACM,SAAU,mBAAmB,CAAC,iBAAgD,EAAA;AAClF,IAAA,OAAO,iBAAiB,EAAE,CAAC,IAAI,EAAE;AACnC;AAEA;AACgB,SAAA,kBAAkB,CAChC,iBAAgD,EAChD,UAA0C,EAAA;IAE1C,IAAI,CAAC,iBAAiB,EAAE;QACtB;;AAEF,IAAA,iBAAiB,CAAC,CAAC,GAAG,UAAU;AAClC;AAEA;AACM,SAAU,uBAAuB,CAAC,iBAAgD,EAAA;IACtF,IAAI,CAAC,iBAAiB,EAAE;QACtB;;AAEF,IAAA,oBAAoB,CAAC,iBAAiB,CAAC,CAAC,EAAE,iBAAiB,CAAC,EAAE,EAAE,iBAAiB,CAAC,CAAC,CAAC;AACpF,IAAA,oBAAoB,CAAC,iBAAiB,CAAC,CAAC,EAAE,iBAAiB,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC;AAC7F;AAEA,SAAS,oBAAoB,CAC3B,SAAsB,EACtB,UAAoB,EACpB,iBAAqC,EACrC,OAAiB,EAAA;AAEjB,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,QAAA,SAAS,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,iBAAiB,mBAAmB,OAAO,CAAC;;AAE7F;;ACrIA;;;AAGG;AACI,MAAM,qBAAqB,GAAG,KAAK;;ACJ1C;;;;;;;;;;;;;;;;;;;;;;AAsBG;AAoCH;;;;;;;;;;;;;AAaG;MACU,aAAa,CAAA;AACxB,IAAA,OAAO,qBAAqB,GAAG,qBAAqB;AAE5C,IAAA,gBAAgB;AAExB;;;;;;AAMG;IACK,aAAa,GAAkC,EAAE;IAEjD,iCAAiC,GAA8B,EAAE;AAEzE;;;;;;AAMG;IACK,UAAU,GAAsB,IAAI;AAE5C;;;AAGG;IACK,gBAAgB,GAAoC,EAAE;AAE9D,IAAA,WAAA,CAAY,gBAA+C,EAAA;AACzD,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB;;AAGlC,IAAA,WAAW,CAAC,SAAiB,EAAE,KAAY,EAAE,SAAkB,EAAA;AACrE,QAAA,MAAM,SAAS,GAAGC,6BAA0C;AAC1D,yBAAiB,SAAS;AAC1B,qBAAa,KAAK;6BACG,KAAK,CAAC,MAAiB;AAC5C,yBAAiB,SAAS;AAC1B,yBAAiB,IAAI,CAAC,GAAG,EAAE,CAC5B;AACD,QAAA,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC;;AAGjC;;AAEG;AACK,IAAA,eAAe,CAAC,SAAiC,EAAA;AACvD,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;;AAEpB,YAAAC,WAAwB,CAAC,SAAS,EAAE,IAAI,CAAC;AACzC,YAAA,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC;YACtC;;AAEF,QAAA,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC;;AAG5B;;;;;;;;;;;;;;;AAeG;AACH,IAAA,QAAQ,CAAC,SAAiB,EAAE,iBAA0B,EAAE,OAAiB,EAAA;QACvE,IAAI,SAAS,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC7D;;AAGF,QAAA,IAAI,CAAC,aAAa,CAAC,qBAAqB,IAAI,yBAAyB,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;YAC7F;;QAGF,MAAM,YAAY,GAAG,CAAC,SAAiB,EAAE,KAAY,EAAE,SAAkB,KAAI;YAC3E,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,KAAK,EAAE,SAAS,CAAC;AAC/C,SAAC;;AAGD,QAAA,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,YAAY;QAE5C,MAAM,gBAAgB,GAAGC,mBAA4B,CAAC,iBAAiB,IAAI,SAAS,CAAC;AAErF,QAAA,IAAI,gBAAgB,KAAK,SAAS,EAAE;YAClC,MAAM,UAAU,GAAG,IAAI,CAAC,iCAAiC,CAAC,gBAAgB,CAAC,IAAI,EAAE;AACjF,YAAA,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC;AAC1B,YAAA,IAAI,CAAC,iCAAiC,CAAC,gBAAgB,CAAC,GAAG,UAAU;;QAGvE,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CACpC,gBAAgB,EAChB,CAAC,OAAgB,KAAI;YACnB,OAAO,CAAC,KAAY,KAAI;AACtB,gBAAA,YAAY,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC;AACzC,aAAC;SACF,EACD,OAAO,CACR;;AAGH;;;;AAIG;AACH,IAAA,iBAAiB,CAAC,iBAAA,GAAmD,MAAM,CAAC,KAAK,EAAA;;;QAG/E,IAAI,CAAC,iBAAiB,EAAE;YACtB;;;AAIF,QAAA,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,CAAC,CAAC;;QAG/C,uBAAuB,CAAC,iBAAiB,CAAC;QAC1C,OAAO,MAAM,CAAC,KAAK;;AAGrB;;;AAGG;AACH,IAAA,qBAAqB,CAAC,eAAyC,EAAA;AAC7D,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC/C,YAAA,MAAM,cAAc,GAA2B,eAAe,CAAC,CAAC,CAAC;YACjE,MAAM,UAAU,GAAG,IAAI,CAAC,gCAAgC,CAAC,cAAc,CAAC,SAAS,CAAC;AAClF,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC1C,MAAM,SAAS,GAAGC,cAA2B,CAAC,cAAc,CAAC;;;gBAG7DtB,YAAyB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;AACnD,gBAAA,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC;;;;AAKrC;;;AAGG;AACK,IAAA,gCAAgC,CAAC,gBAAwB,EAAA;QAC/D,MAAM,UAAU,GAAG,EAAE;AACrB,QAAA,IAAI,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,EAAE;AACxC,YAAA,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC;;AAEnC,QAAA,IAAI,IAAI,CAAC,iCAAiC,CAAC,gBAAgB,CAAC,EAAE;YAC5D,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,iCAAiC,CAAC,gBAAgB,CAAC,CAAC;;AAE9E,QAAA,OAAO,UAAU;;AAGnB;;AAEG;AACH,IAAA,OAAO,CAAC,SAAiB,EAAA;AACvB,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;;AAGtC;;;;AAIG;IACH,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,gBAAgB,EAAE,OAAO,EAAE;AAChC,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;AAC5B,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE;AACvB,QAAA,IAAI,CAAC,iCAAiC,GAAG,EAAE;AAC3C,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI;AACtB,QAAA,IAAI,CAAC,gBAAgB,GAAG,EAAE;;AAG5B;;;;;;;;AAQG;IACH,kBAAkB,CAAC,UAAsB,EAAE,WAAwB,EAAA;AACjE,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,CAAC;;AAGpC;;;;AAIG;IACH,IAAI,CAAC,UAAsB,EAAE,WAAwB,EAAA;AACnD,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU;AAE5B,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE,MAAM,EAAE;AACjC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACrD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;;AAEhD,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;;;;;AC9QlC;;;AAGG;AACa,SAAA,oCAAoC,CAClD,SAAsB,EACtB,KAAa,EACb,gBAA0B,EAC1B,iBAA2B,EAC3B,aAAA,GAA4C,MAAM,EAAA;AAElD,IAAA,MAAM,iBAAiB,GAAG,uBAAuB,CAAC,SAAS,CAAC;AAC5D,IAAA,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;AACzB,QAAA,aAAa,CAAC,MAAM,GAAG,EAAE;;AAE3B,IAAA,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,iBAAiB;AAC/C,IAAA,SAAS,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;IAC9C,SAAS,CAAC,iBAAiB,EAAE,iBAAiB,iBAAiB,IAAI,CAAC;AACtE;AAEA;SACgB,4BAA4B,CAC1C,KAAa,EACb,gBAA4C,MAAM,EAAA;IAElD,OAAO,mBAAmB,CAAC,aAAa,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC;AAC3D;AAEA;;;AAGG;AACG,SAAU,2BAA2B,CACzC,WAAwB,EACxB,KAAa,EACb,UAA0C,EAC1C,aAAA,GAA4C,MAAM,EAAA;IAElD,kBAAkB,CAAC,aAAa,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,UAAU,CAAC;AAC/D;AAEA;SACgB,gCAAgC,CAC9C,KAAa,EACb,gBAA4C,MAAM,EAAA;IAElD,uBAAuB,CAAC,aAAa,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC;AACxD;AAEA;SACgB,gCAAgC,CAC9C,KAAa,EACb,gBAA4C,MAAM,EAAA;AAElD,IAAA,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;QACzB;;AAEF,IAAA,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS;AACzC;;;;"}