{"version": 3, "file": "table.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/table/table.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/table/cell.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/table/row.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/table/text-column.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/table/module.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/table/table-data-source.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ChangeDetectionStrategy, Component, Directive, ViewEncapsulation} from '@angular/core';\nimport {\n  CdkTable,\n  _CoalescedStyleScheduler,\n  _COALESCED_STYLE_SCHEDULER,\n  CDK_TABLE,\n  STICKY_POSITIONING_LISTENER,\n  HeaderRowOutlet,\n  DataRowOutlet,\n  NoDataRowOutlet,\n  FooterRowOutlet,\n} from '@angular/cdk/table';\nimport {\n  _DisposeViewRepeaterStrategy,\n  _RecycleViewRepeaterStrategy,\n  _VIEW_REPEATER_STRATEGY,\n} from '@angular/cdk/collections';\n\n/**\n * Enables the recycle view repeater strategy, which reduces rendering latency. Not compatible with\n * tables that animate rows.\n */\n@Directive({\n  selector: 'mat-table[recycleRows], table[mat-table][recycleRows]',\n  providers: [{provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy}],\n})\nexport class MatRecycleRows {}\n\n@Component({\n  selector: 'mat-table, table[mat-table]',\n  exportAs: 'matTable',\n  // Note that according to MDN, the `caption` element has to be projected as the **first**\n  // element in the table. See https://developer.mozilla.org/en-US/docs/Web/HTML/Element/caption\n  // We can't reuse `CDK_TABLE_TEMPLATE` because it's incompatible with local compilation mode.\n  template: `\n    <ng-content select=\"caption\"/>\n    <ng-content select=\"colgroup, col\"/>\n\n    <!--\n      Unprojected content throws a hydration error so we need this to capture it.\n      It gets removed on the client so it doesn't affect the layout.\n    -->\n    @if (_isServer) {\n      <ng-content/>\n    }\n\n    @if (_isNativeHtmlTable) {\n      <thead role=\"rowgroup\">\n        <ng-container headerRowOutlet/>\n      </thead>\n      <tbody class=\"mdc-data-table__content\" role=\"rowgroup\">\n        <ng-container rowOutlet/>\n        <ng-container noDataRowOutlet/>\n      </tbody>\n      <tfoot role=\"rowgroup\">\n        <ng-container footerRowOutlet/>\n      </tfoot>\n    } @else {\n      <ng-container headerRowOutlet/>\n      <ng-container rowOutlet/>\n      <ng-container noDataRowOutlet/>\n      <ng-container footerRowOutlet/>\n    }\n  `,\n  styleUrl: 'table.css',\n  host: {\n    'class': 'mat-mdc-table mdc-data-table__table',\n    '[class.mdc-table-fixed-layout]': 'fixedLayout',\n  },\n  providers: [\n    {provide: CdkTable, useExisting: MatTable},\n    {provide: CDK_TABLE, useExisting: MatTable},\n    {provide: _COALESCED_STYLE_SCHEDULER, useClass: _CoalescedStyleScheduler},\n    // TODO(michaeljamesparsons) Abstract the view repeater strategy to a directive API so this code\n    //  is only included in the build if used.\n    {provide: _VIEW_REPEATER_STRATEGY, useClass: _DisposeViewRepeaterStrategy},\n    // Prevent nested tables from seeing this table's StickyPositioningListener.\n    {provide: STICKY_POSITIONING_LISTENER, useValue: null},\n  ],\n  encapsulation: ViewEncapsulation.None,\n  // See note on CdkTable for explanation on why this uses the default change detection strategy.\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  imports: [HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet],\n})\nexport class MatTable<T> extends CdkTable<T> {\n  /** Overrides the sticky CSS class set by the `CdkTable`. */\n  protected override stickyCssClass = 'mat-mdc-table-sticky';\n\n  /** Overrides the need to add position: sticky on every sticky cell element in `CdkTable`. */\n  protected override needsPositionStickyOnElement = false;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, Input} from '@angular/core';\nimport {\n  CdkCell,\n  CdkCellDef,\n  CdkColumnDef,\n  CdkFooterCell,\n  CdkFooterCellDef,\n  CdkHeaderCell,\n  CdkHeaderCellDef,\n} from '@angular/cdk/table';\n\n/**\n * Cell definition for the mat-table.\n * Captures the template of a column's data row cell as well as cell-specific properties.\n */\n@Directive({\n  selector: '[matCellDef]',\n  providers: [{provide: CdkCellDef, useExisting: MatCellDef}],\n})\nexport class MatCellDef extends CdkCellDef {}\n\n/**\n * Header cell definition for the mat-table.\n * Captures the template of a column's header cell and as well as cell-specific properties.\n */\n@Directive({\n  selector: '[matHeaderCellDef]',\n  providers: [{provide: CdkHeaderCellDef, useExisting: MatHeaderCellDef}],\n})\nexport class MatHeader<PERSON>ellDef extends CdkHeaderCellDef {}\n\n/**\n * Footer cell definition for the mat-table.\n * Captures the template of a column's footer cell and as well as cell-specific properties.\n */\n@Directive({\n  selector: '[matFooterCellDef]',\n  providers: [{provide: CdkFooterCellDef, useExisting: MatFooterCellDef}],\n})\nexport class MatFooterCellDef extends CdkFooterCellDef {}\n\n/**\n * Column definition for the mat-table.\n * Defines a set of cells available for a table column.\n */\n@Directive({\n  selector: '[matColumnDef]',\n  providers: [\n    {provide: CdkColumnDef, useExisting: MatColumnDef},\n    {provide: 'MAT_SORT_HEADER_COLUMN_DEF', useExisting: MatColumnDef},\n  ],\n})\nexport class MatColumnDef extends CdkColumnDef {\n  /** Unique name for this column. */\n  @Input('matColumnDef')\n  override get name(): string {\n    return this._name;\n  }\n  override set name(name: string) {\n    this._setNameInput(name);\n  }\n\n  /**\n   * Add \"mat-column-\" prefix in addition to \"cdk-column-\" prefix.\n   * In the future, this will only add \"mat-column-\" and columnCssClassName\n   * will change from type string[] to string.\n   * @docs-private\n   */\n  protected override _updateColumnCssClassName() {\n    super._updateColumnCssClassName();\n    this._columnCssClassName!.push(`mat-column-${this.cssClassFriendlyName}`);\n  }\n}\n\n/** Header cell template container that adds the right classes and role. */\n@Directive({\n  selector: 'mat-header-cell, th[mat-header-cell]',\n  host: {\n    'class': 'mat-mdc-header-cell mdc-data-table__header-cell',\n    'role': 'columnheader',\n  },\n})\nexport class MatHeaderCell extends CdkHeaderCell {}\n\n/** Footer cell template container that adds the right classes and role. */\n@Directive({\n  selector: 'mat-footer-cell, td[mat-footer-cell]',\n  host: {\n    'class': 'mat-mdc-footer-cell mdc-data-table__cell',\n  },\n})\nexport class MatFooterCell extends CdkFooterCell {}\n\n/** Cell template container that adds the right classes and role. */\n@Directive({\n  selector: 'mat-cell, td[mat-cell]',\n  host: {\n    'class': 'mat-mdc-cell mdc-data-table__cell',\n  },\n})\nexport class MatCell extends CdkCell {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  CdkFooterRow,\n  CdkFooterRowDef,\n  CdkHeaderRow,\n  CdkHeaderRowDef,\n  CdkRow,\n  CdkRowDef,\n  CdkNoDataRow,\n  CdkCellOutlet,\n} from '@angular/cdk/table';\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  Directive,\n  ViewEncapsulation,\n  booleanAttribute,\n} from '@angular/core';\n\n// We can't reuse `CDK_ROW_TEMPLATE` because it's incompatible with local compilation mode.\nconst ROW_TEMPLATE = `<ng-container cdkCellOutlet></ng-container>`;\n\n/**\n * Header row definition for the mat-table.\n * Captures the header row's template and other header properties such as the columns to display.\n */\n@Directive({\n  selector: '[matHeaderRowDef]',\n  providers: [{provide: CdkHeaderRowDef, useExisting: MatHeaderRowDef}],\n  inputs: [\n    {name: 'columns', alias: 'matHeaderRowDef'},\n    {name: 'sticky', alias: 'matHeaderRowDefSticky', transform: booleanAttribute},\n  ],\n})\nexport class MatHeaderRowDef extends CdkHeaderRowDef {}\n\n/**\n * Footer row definition for the mat-table.\n * Captures the footer row's template and other footer properties such as the columns to display.\n */\n@Directive({\n  selector: '[matFooterRowDef]',\n  providers: [{provide: CdkFooterRowDef, useExisting: MatFooterRowDef}],\n  inputs: [\n    {name: 'columns', alias: 'matFooterRowDef'},\n    {name: 'sticky', alias: 'matFooterRowDefSticky', transform: booleanAttribute},\n  ],\n})\nexport class MatFooterRowDef extends CdkFooterRowDef {}\n\n/**\n * Data row definition for the mat-table.\n * Captures the data row's template and other properties such as the columns to display and\n * a when predicate that describes when this row should be used.\n */\n@Directive({\n  selector: '[matRowDef]',\n  providers: [{provide: CdkRowDef, useExisting: MatRowDef}],\n  inputs: [\n    {name: 'columns', alias: 'matRowDefColumns'},\n    {name: 'when', alias: 'matRowDefWhen'},\n  ],\n})\nexport class MatRowDef<T> extends CdkRowDef<T> {}\n\n/** Header template container that contains the cell outlet. Adds the right class and role. */\n@Component({\n  selector: 'mat-header-row, tr[mat-header-row]',\n  template: ROW_TEMPLATE,\n  host: {\n    'class': 'mat-mdc-header-row mdc-data-table__header-row',\n    'role': 'row',\n  },\n  // See note on CdkTable for explanation on why this uses the default change detection strategy.\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  encapsulation: ViewEncapsulation.None,\n  exportAs: 'matHeaderRow',\n  providers: [{provide: CdkHeaderRow, useExisting: MatHeaderRow}],\n  imports: [CdkCellOutlet],\n})\nexport class MatHeaderRow extends CdkHeaderRow {}\n\n/** Footer template container that contains the cell outlet. Adds the right class and role. */\n@Component({\n  selector: 'mat-footer-row, tr[mat-footer-row]',\n  template: ROW_TEMPLATE,\n  host: {\n    'class': 'mat-mdc-footer-row mdc-data-table__row',\n    'role': 'row',\n  },\n  // See note on CdkTable for explanation on why this uses the default change detection strategy.\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  encapsulation: ViewEncapsulation.None,\n  exportAs: 'matFooterRow',\n  providers: [{provide: CdkFooterRow, useExisting: MatFooterRow}],\n  imports: [CdkCellOutlet],\n})\nexport class MatFooterRow extends CdkFooterRow {}\n\n/** Data row template container that contains the cell outlet. Adds the right class and role. */\n@Component({\n  selector: 'mat-row, tr[mat-row]',\n  template: ROW_TEMPLATE,\n  host: {\n    'class': 'mat-mdc-row mdc-data-table__row',\n    'role': 'row',\n  },\n  // See note on CdkTable for explanation on why this uses the default change detection strategy.\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  encapsulation: ViewEncapsulation.None,\n  exportAs: 'matRow',\n  providers: [{provide: CdkRow, useExisting: MatRow}],\n  imports: [CdkCellOutlet],\n})\nexport class MatRow extends CdkRow {}\n\n/** Row that can be used to display a message when no data is shown in the table. */\n@Directive({\n  selector: 'ng-template[matNoDataRow]',\n  providers: [{provide: CdkNoDataRow, useExisting: MatNoDataRow}],\n})\nexport class MatNoDataRow extends CdkNoDataRow {\n  override _contentClassName = 'mat-mdc-no-data-row';\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {CdkTextColumn} from '@angular/cdk/table';\nimport {ChangeDetectionStrategy, Component, ViewEncapsulation} from '@angular/core';\nimport {MatColumnDef, MatHeaderCellDef, MatHeaderCell, MatCellDef, MatCell} from './cell';\n\n/**\n * Column that simply shows text content for the header and row cells. Assumes that the table\n * is using the native table implementation (`<table>`).\n *\n * By default, the name of this column will be the header text and data property accessor.\n * The header text can be overridden with the `headerText` input. Cell values can be overridden with\n * the `dataAccessor` input. Change the text justification to the start or end using the `justify`\n * input.\n */\n@Component({\n  selector: 'mat-text-column',\n  template: `\n    <ng-container matColumnDef>\n      <th mat-header-cell *matHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td mat-cell *matCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `,\n  encapsulation: ViewEncapsulation.None,\n  // Change detection is intentionally not set to OnPush. This component's template will be provided\n  // to the table to be inserted into its view. This is problematic when change detection runs since\n  // the bindings in this template will be evaluated _after_ the table's view is evaluated, which\n  // mean's the template in the table's view will not have the updated value (and in fact will cause\n  // an ExpressionChangedAfterItHasBeenCheckedError).\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  imports: [MatColumnDef, MatHeaderCellDef, MatHeaderCell, MatCellDef, MatCell],\n})\nexport class MatTextColumn<T> extends CdkTextColumn<T> {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '../core';\nimport {MatRecycleRows, MatTable} from './table';\nimport {CdkTableModule} from '@angular/cdk/table';\nimport {\n  MatCell,\n  MatCellDef,\n  MatColumnDef,\n  MatFooterCell,\n  MatFooterCellDef,\n  MatHeaderCell,\n  MatHeaderCellDef,\n} from './cell';\nimport {\n  MatFooterRow,\n  MatFooterRowDef,\n  MatHeaderRow,\n  MatHeaderRowDef,\n  MatRow,\n  MatRowDef,\n  MatNoDataRow,\n} from './row';\nimport {MatTextColumn} from './text-column';\n\nconst EXPORTED_DECLARATIONS = [\n  // Table\n  MatTable,\n  MatRecycleRows,\n\n  // Template defs\n  MatHeaderCellDef,\n  <PERSON><PERSON><PERSON>er<PERSON>owDef,\n  <PERSON><PERSON>olumnDef,\n  Mat<PERSON>ellDef,\n  <PERSON><PERSON>owDef,\n  <PERSON><PERSON><PERSON>er<PERSON>ellDef,\n  MatFooterRowDef,\n\n  // Cell directives\n  MatHeader<PERSON>ell,\n  Mat<PERSON>ell,\n  MatFooterCell,\n\n  // Row directives\n  MatHeaderRow,\n  MatRow,\n  MatFooterRow,\n  MatNoDataRow,\n\n  MatTextColumn,\n];\n\n@NgModule({\n  imports: [MatCommonModule, CdkTableModule, ...EXPORTED_DECLARATIONS],\n  exports: [MatCommonModule, EXPORTED_DECLARATIONS],\n})\nexport class MatTableModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {MatPaginator, PageEvent} from '../paginator';\nimport {\n  BehaviorSubject,\n  combineLatest,\n  merge,\n  Observable,\n  of as observableOf,\n  Subject,\n  Subscription,\n} from 'rxjs';\nimport {DataSource} from '@angular/cdk/collections';\nimport {MatSort, Sort} from '../sort';\nimport {_isNumberValue} from '@angular/cdk/coercion';\nimport {map} from 'rxjs/operators';\n\n/**\n * Corresponds to `Number.MAX_SAFE_INTEGER`. Moved out into a variable here due to\n * flaky browser support and the value not being defined in Closure's typings.\n */\nconst MAX_SAFE_INTEGER = 9007199254740991;\n\n/**\n * Data source that accepts a client-side data array and includes native support of filtering,\n * sorting (using MatSort), and pagination (using MatPaginator).\n *\n * Allows for sort customization by overriding sortingDataAccessor, which defines how data\n * properties are accessed. Also allows for filter customization by overriding filterPredicate,\n * which defines how row data is converted to a string for filter matching.\n *\n * **Note:** This class is meant to be a simple data source to help you get started. As such\n * it isn't equipped to handle some more advanced cases like robust i18n support or server-side\n * interactions. If your app needs to support more advanced use cases, consider implementing your\n * own `DataSource`.\n */\nexport class MatTableDataSource<T, P extends MatPaginator = MatPaginator> extends DataSource<T> {\n  /** Stream that emits when a new data array is set on the data source. */\n  private readonly _data: BehaviorSubject<T[]>;\n\n  /** Stream emitting render data to the table (depends on ordered data changes). */\n  private readonly _renderData = new BehaviorSubject<T[]>([]);\n\n  /** Stream that emits when a new filter string is set on the data source. */\n  private readonly _filter = new BehaviorSubject<string>('');\n\n  /** Used to react to internal changes of the paginator that are made by the data source itself. */\n  private readonly _internalPageChanges = new Subject<void>();\n\n  /**\n   * Subscription to the changes that should trigger an update to the table's rendered rows, such\n   * as filtering, sorting, pagination, or base data changes.\n   */\n  _renderChangesSubscription: Subscription | null = null;\n\n  /**\n   * The filtered set of data that has been matched by the filter string, or all the data if there\n   * is no filter. Useful for knowing the set of data the table represents.\n   * For example, a 'selectAll()' function would likely want to select the set of filtered data\n   * shown to the user rather than all the data.\n   */\n  filteredData: T[];\n\n  /** Array of data that should be rendered by the table, where each object represents one row. */\n  get data() {\n    return this._data.value;\n  }\n\n  set data(data: T[]) {\n    data = Array.isArray(data) ? data : [];\n    this._data.next(data);\n    // Normally the `filteredData` is updated by the re-render\n    // subscription, but that won't happen if it's inactive.\n    if (!this._renderChangesSubscription) {\n      this._filterData(data);\n    }\n  }\n\n  /**\n   * Filter term that should be used to filter out objects from the data array. To override how\n   * data objects match to this filter string, provide a custom function for filterPredicate.\n   */\n  get filter(): string {\n    return this._filter.value;\n  }\n\n  set filter(filter: string) {\n    this._filter.next(filter);\n    // Normally the `filteredData` is updated by the re-render\n    // subscription, but that won't happen if it's inactive.\n    if (!this._renderChangesSubscription) {\n      this._filterData(this.data);\n    }\n  }\n\n  /**\n   * Instance of the MatSort directive used by the table to control its sorting. Sort changes\n   * emitted by the MatSort will trigger an update to the table's rendered data.\n   */\n  get sort(): MatSort | null {\n    return this._sort;\n  }\n\n  set sort(sort: MatSort | null) {\n    this._sort = sort;\n    this._updateChangeSubscription();\n  }\n\n  private _sort: MatSort | null;\n\n  /**\n   * Instance of the paginator component used by the table to control what page of the data is\n   * displayed. Page changes emitted by the paginator will trigger an update to the\n   * table's rendered data.\n   *\n   * Note that the data source uses the paginator's properties to calculate which page of data\n   * should be displayed. If the paginator receives its properties as template inputs,\n   * e.g. `[pageLength]=100` or `[pageIndex]=1`, then be sure that the paginator's view has been\n   * initialized before assigning it to this data source.\n   */\n  get paginator(): P | null {\n    return this._paginator;\n  }\n\n  set paginator(paginator: P | null) {\n    this._paginator = paginator;\n    this._updateChangeSubscription();\n  }\n\n  private _paginator: P | null;\n\n  /**\n   * Data accessor function that is used for accessing data properties for sorting through\n   * the default sortData function.\n   * This default function assumes that the sort header IDs (which defaults to the column name)\n   * matches the data's properties (e.g. column Xyz represents data['Xyz']).\n   * May be set to a custom function for different behavior.\n   * @param data Data object that is being accessed.\n   * @param sortHeaderId The name of the column that represents the data.\n   */\n  sortingDataAccessor: (data: T, sortHeaderId: string) => string | number = (\n    data: T,\n    sortHeaderId: string,\n  ): string | number => {\n    const value = (data as unknown as Record<string, any>)[sortHeaderId];\n\n    if (_isNumberValue(value)) {\n      const numberValue = Number(value);\n\n      // Numbers beyond `MAX_SAFE_INTEGER` can't be compared reliably so we leave them as strings.\n      // See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/MAX_SAFE_INTEGER\n      return numberValue < MAX_SAFE_INTEGER ? numberValue : value;\n    }\n\n    return value;\n  };\n\n  /**\n   * Gets a sorted copy of the data array based on the state of the MatSort. Called\n   * after changes are made to the filtered data or when sort changes are emitted from MatSort.\n   * By default, the function retrieves the active sort and its direction and compares data\n   * by retrieving data using the sortingDataAccessor. May be overridden for a custom implementation\n   * of data ordering.\n   * @param data The array of data that should be sorted.\n   * @param sort The connected MatSort that holds the current sort state.\n   */\n  sortData: (data: T[], sort: MatSort) => T[] = (data: T[], sort: MatSort): T[] => {\n    const active = sort.active;\n    const direction = sort.direction;\n    if (!active || direction == '') {\n      return data;\n    }\n\n    return data.sort((a, b) => {\n      let valueA = this.sortingDataAccessor(a, active);\n      let valueB = this.sortingDataAccessor(b, active);\n\n      // If there are data in the column that can be converted to a number,\n      // it must be ensured that the rest of the data\n      // is of the same type so as not to order incorrectly.\n      const valueAType = typeof valueA;\n      const valueBType = typeof valueB;\n\n      if (valueAType !== valueBType) {\n        if (valueAType === 'number') {\n          valueA += '';\n        }\n        if (valueBType === 'number') {\n          valueB += '';\n        }\n      }\n\n      // If both valueA and valueB exist (truthy), then compare the two. Otherwise, check if\n      // one value exists while the other doesn't. In this case, existing value should come last.\n      // This avoids inconsistent results when comparing values to undefined/null.\n      // If neither value exists, return 0 (equal).\n      let comparatorResult = 0;\n      if (valueA != null && valueB != null) {\n        // Check if one value is greater than the other; if equal, comparatorResult should remain 0.\n        if (valueA > valueB) {\n          comparatorResult = 1;\n        } else if (valueA < valueB) {\n          comparatorResult = -1;\n        }\n      } else if (valueA != null) {\n        comparatorResult = 1;\n      } else if (valueB != null) {\n        comparatorResult = -1;\n      }\n\n      return comparatorResult * (direction == 'asc' ? 1 : -1);\n    });\n  };\n\n  /**\n   * Checks if a data object matches the data source's filter string. By default, each data object\n   * is converted to a string of its properties and returns true if the filter has\n   * at least one occurrence in that string. By default, the filter string has its whitespace\n   * trimmed and the match is case-insensitive. May be overridden for a custom implementation of\n   * filter matching.\n   * @param data Data object used to check against the filter.\n   * @param filter Filter string that has been set on the data source.\n   * @returns Whether the filter matches against the data\n   */\n  filterPredicate: (data: T, filter: string) => boolean = (data: T, filter: string): boolean => {\n    // Transform the filter by converting it to lowercase and removing whitespace.\n    const transformedFilter = filter.trim().toLowerCase();\n    // Loops over the values in the array and returns true if any of them match the filter string\n    return Object.values(data as {[key: string]: any}).some(value =>\n      `${value}`.toLowerCase().includes(transformedFilter),\n    );\n  };\n\n  constructor(initialData: T[] = []) {\n    super();\n    this._data = new BehaviorSubject<T[]>(initialData);\n    this._updateChangeSubscription();\n  }\n\n  /**\n   * Subscribe to changes that should trigger an update to the table's rendered rows. When the\n   * changes occur, process the current state of the filter, sort, and pagination along with\n   * the provided base data and send it to the table for rendering.\n   */\n  _updateChangeSubscription() {\n    // Sorting and/or pagination should be watched if sort and/or paginator are provided.\n    // The events should emit whenever the component emits a change or initializes, or if no\n    // component is provided, a stream with just a null event should be provided.\n    // The `sortChange` and `pageChange` acts as a signal to the combineLatests below so that the\n    // pipeline can progress to the next step. Note that the value from these streams are not used,\n    // they purely act as a signal to progress in the pipeline.\n    const sortChange: Observable<Sort | null | void> = this._sort\n      ? (merge(this._sort.sortChange, this._sort.initialized) as Observable<Sort | void>)\n      : observableOf(null);\n    const pageChange: Observable<PageEvent | null | void> = this._paginator\n      ? (merge(\n          this._paginator.page,\n          this._internalPageChanges,\n          this._paginator.initialized,\n        ) as Observable<PageEvent | void>)\n      : observableOf(null);\n    const dataStream = this._data;\n    // Watch for base data or filter changes to provide a filtered set of data.\n    const filteredData = combineLatest([dataStream, this._filter]).pipe(\n      map(([data]) => this._filterData(data)),\n    );\n    // Watch for filtered data or sort changes to provide an ordered set of data.\n    const orderedData = combineLatest([filteredData, sortChange]).pipe(\n      map(([data]) => this._orderData(data)),\n    );\n    // Watch for ordered data or page changes to provide a paged set of data.\n    const paginatedData = combineLatest([orderedData, pageChange]).pipe(\n      map(([data]) => this._pageData(data)),\n    );\n    // Watched for paged data changes and send the result to the table to render.\n    this._renderChangesSubscription?.unsubscribe();\n    this._renderChangesSubscription = paginatedData.subscribe(data => this._renderData.next(data));\n  }\n\n  /**\n   * Returns a filtered data array where each filter object contains the filter string within\n   * the result of the filterPredicate function. If no filter is set, returns the data array\n   * as provided.\n   */\n  _filterData(data: T[]) {\n    // If there is a filter string, filter out data that does not contain it.\n    // Each data object is converted to a string using the function defined by filterPredicate.\n    // May be overridden for customization.\n    this.filteredData =\n      this.filter == null || this.filter === ''\n        ? data\n        : data.filter(obj => this.filterPredicate(obj, this.filter));\n\n    if (this.paginator) {\n      this._updatePaginator(this.filteredData.length);\n    }\n\n    return this.filteredData;\n  }\n\n  /**\n   * Returns a sorted copy of the data if MatSort has a sort applied, otherwise just returns the\n   * data array as provided. Uses the default data accessor for data lookup, unless a\n   * sortDataAccessor function is defined.\n   */\n  _orderData(data: T[]): T[] {\n    // If there is no active sort or direction, return the data without trying to sort.\n    if (!this.sort) {\n      return data;\n    }\n\n    return this.sortData(data.slice(), this.sort);\n  }\n\n  /**\n   * Returns a paged slice of the provided data array according to the provided paginator's page\n   * index and length. If there is no paginator provided, returns the data array as provided.\n   */\n  _pageData(data: T[]): T[] {\n    if (!this.paginator) {\n      return data;\n    }\n\n    const startIndex = this.paginator.pageIndex * this.paginator.pageSize;\n    return data.slice(startIndex, startIndex + this.paginator.pageSize);\n  }\n\n  /**\n   * Updates the paginator to reflect the length of the filtered data, and makes sure that the page\n   * index does not exceed the paginator's last page. Values are changed in a resolved promise to\n   * guard against making property changes within a round of change detection.\n   */\n  _updatePaginator(filteredDataLength: number) {\n    Promise.resolve().then(() => {\n      const paginator = this.paginator;\n\n      if (!paginator) {\n        return;\n      }\n\n      paginator.length = filteredDataLength;\n\n      // If the page index is set beyond the page, reduce it to the last page.\n      if (paginator.pageIndex > 0) {\n        const lastPageIndex = Math.ceil(paginator.length / paginator.pageSize) - 1 || 0;\n        const newPageIndex = Math.min(paginator.pageIndex, lastPageIndex);\n\n        if (newPageIndex !== paginator.pageIndex) {\n          paginator.pageIndex = newPageIndex;\n\n          // Since the paginator only emits after user-generated changes,\n          // we need our own stream so we know to should re-render the data.\n          this._internalPageChanges.next();\n        }\n      }\n    });\n  }\n\n  /**\n   * Used by the MatTable. Called when it connects to the data source.\n   * @docs-private\n   */\n  connect() {\n    if (!this._renderChangesSubscription) {\n      this._updateChangeSubscription();\n    }\n\n    return this._renderData;\n  }\n\n  /**\n   * Used by the MatTable. Called when it disconnects from the data source.\n   * @docs-private\n   */\n  disconnect() {\n    this._renderChangesSubscription?.unsubscribe();\n    this._renderChangesSubscription = null;\n  }\n}\n"], "names": ["observableOf"], "mappings": ";;;;;;;;;;;AA0BA;;;AAGG;MAKU,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,uDAAA,EAAA,SAAA,EAFd,CAAC,EAAC,OAAO,EAAE,uBAAuB,EAAE,QAAQ,EAAE,4BAA4B,EAAC,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAE5E,cAAc,EAAA,UAAA,EAAA,CAAA;kBAJ1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,uDAAuD;oBACjE,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,uBAAuB,EAAE,QAAQ,EAAE,4BAA4B,EAAC,CAAC;AACxF,iBAAA;;AA4DK,MAAO,QAAY,SAAQ,QAAW,CAAA;;IAEvB,cAAc,GAAG,sBAAsB;;IAGvC,4BAA4B,GAAG,KAAK;uGAL5C,QAAQ,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAR,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,QAAQ,EAhBR,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,6BAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,8BAAA,EAAA,aAAA,EAAA,EAAA,cAAA,EAAA,qCAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA,EAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAC;AAC1C,YAAA,EAAC,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAC;AAC3C,YAAA,EAAC,OAAO,EAAE,0BAA0B,EAAE,QAAQ,EAAE,wBAAwB,EAAC;;;AAGzE,YAAA,EAAC,OAAO,EAAE,uBAAuB,EAAE,QAAQ,EAAE,4BAA4B,EAAC;;AAE1E,YAAA,EAAC,OAAO,EAAE,2BAA2B,EAAE,QAAQ,EAAE,IAAI,EAAC;SACvD,EA5CS,QAAA,EAAA,CAAA,UAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,+mKAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAoBS,eAAe,EAAE,QAAA,EAAA,mBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,aAAa,EAAE,QAAA,EAAA,aAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,eAAe,8DAAE,eAAe,EAAA,QAAA,EAAA,mBAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAE/D,QAAQ,EAAA,UAAA,EAAA,CAAA;kBAzDpB,SAAS;+BACE,6BAA6B,EAAA,QAAA,EAC7B,UAAU,EAIV,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6BT,EAEK,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,qCAAqC;AAC9C,wBAAA,gCAAgC,EAAE,aAAa;qBAChD,EACU,SAAA,EAAA;AACT,wBAAA,EAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,UAAU,EAAC;AAC1C,wBAAA,EAAC,OAAO,EAAE,SAAS,EAAE,WAAW,UAAU,EAAC;AAC3C,wBAAA,EAAC,OAAO,EAAE,0BAA0B,EAAE,QAAQ,EAAE,wBAAwB,EAAC;;;AAGzE,wBAAA,EAAC,OAAO,EAAE,uBAAuB,EAAE,QAAQ,EAAE,4BAA4B,EAAC;;AAE1E,wBAAA,EAAC,OAAO,EAAE,2BAA2B,EAAE,QAAQ,EAAE,IAAI,EAAC;AACvD,qBAAA,EAAA,aAAA,EACc,iBAAiB,CAAC,IAAI,EAGpB,eAAA,EAAA,uBAAuB,CAAC,OAAO,EAAA,OAAA,EACvC,CAAC,eAAe,EAAE,aAAa,EAAE,eAAe,EAAE,eAAe,CAAC,EAAA,MAAA,EAAA,CAAA,+mKAAA,CAAA,EAAA;;;ACxE7E;;;AAGG;AAKG,MAAO,UAAW,SAAQ,UAAU,CAAA;uGAA7B,UAAU,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAV,UAAU,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,SAAA,EAFV,CAAC,EAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAC,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAEhD,UAAU,EAAA,UAAA,EAAA,CAAA;kBAJtB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,cAAc;oBACxB,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAY,UAAA,EAAC,CAAC;AAC5D,iBAAA;;AAGD;;;AAGG;AAKG,MAAO,gBAAiB,SAAQ,gBAAgB,CAAA;uGAAzC,gBAAgB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAhB,gBAAgB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,SAAA,EAFhB,CAAC,EAAC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,gBAAgB,EAAC,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAE5D,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAJ5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oBAAoB;oBAC9B,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAkB,gBAAA,EAAC,CAAC;AACxE,iBAAA;;AAGD;;;AAGG;AAKG,MAAO,gBAAiB,SAAQ,gBAAgB,CAAA;uGAAzC,gBAAgB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAhB,gBAAgB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,SAAA,EAFhB,CAAC,EAAC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,gBAAgB,EAAC,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAE5D,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAJ5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oBAAoB;oBAC9B,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAkB,gBAAA,EAAC,CAAC;AACxE,iBAAA;;AAGD;;;AAGG;AAQG,MAAO,YAAa,SAAQ,YAAY,CAAA;;AAE5C,IAAA,IACa,IAAI,GAAA;QACf,OAAO,IAAI,CAAC,KAAK;;IAEnB,IAAa,IAAI,CAAC,IAAY,EAAA;AAC5B,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;;AAG1B;;;;;AAKG;IACgB,yBAAyB,GAAA;QAC1C,KAAK,CAAC,yBAAyB,EAAE;QACjC,IAAI,CAAC,mBAAoB,CAAC,IAAI,CAAC,CAAc,WAAA,EAAA,IAAI,CAAC,oBAAoB,CAAE,CAAA,CAAC;;uGAlBhE,YAAY,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAZ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAY,EALZ,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,CAAA,cAAA,EAAA,MAAA,CAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA,EAAC,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAC;AAClD,YAAA,EAAC,OAAO,EAAE,4BAA4B,EAAE,WAAW,EAAE,YAAY,EAAC;AACnE,SAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAEU,YAAY,EAAA,UAAA,EAAA,CAAA;kBAPxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,SAAS,EAAE;AACT,wBAAA,EAAC,OAAO,EAAE,YAAY,EAAE,WAAW,cAAc,EAAC;AAClD,wBAAA,EAAC,OAAO,EAAE,4BAA4B,EAAE,WAAW,cAAc,EAAC;AACnE,qBAAA;AACF,iBAAA;8BAIc,IAAI,EAAA,CAAA;sBADhB,KAAK;uBAAC,cAAc;;AAoBvB;AAQM,MAAO,aAAc,SAAQ,aAAa,CAAA;uGAAnC,aAAa,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sCAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,cAAA,EAAA,EAAA,cAAA,EAAA,iDAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBAPzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sCAAsC;AAChD,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,iDAAiD;AAC1D,wBAAA,MAAM,EAAE,cAAc;AACvB,qBAAA;AACF,iBAAA;;AAGD;AAOM,MAAO,aAAc,SAAQ,aAAa,CAAA;uGAAnC,aAAa,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sCAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,0CAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBANzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sCAAsC;AAChD,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,0CAA0C;AACpD,qBAAA;AACF,iBAAA;;AAGD;AAOM,MAAO,OAAQ,SAAQ,OAAO,CAAA;uGAAvB,OAAO,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAP,OAAO,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,wBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,mCAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAP,OAAO,EAAA,UAAA,EAAA,CAAA;kBANnB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,wBAAwB;AAClC,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,mCAAmC;AAC7C,qBAAA;AACF,iBAAA;;;ACjFD;AACA,MAAM,YAAY,GAAG,CAAA,2CAAA,CAA6C;AAElE;;;AAGG;AASG,MAAO,eAAgB,SAAQ,eAAe,CAAA;uGAAvC,eAAe,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAe,EAHoC,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,CAAA,iBAAA,EAAA,SAAA,CAAA,EAAA,MAAA,EAAA,CAAA,uBAAA,EAAA,QAAA,EAAA,gBAAgB,CAHnE,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,eAAe,EAAE,WAAW,EAAE,eAAe,EAAC,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAM1D,eAAe,EAAA,UAAA,EAAA,CAAA;kBAR3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,mBAAmB;oBAC7B,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,eAAe,EAAE,WAAW,EAAiB,eAAA,EAAC,CAAC;AACrE,oBAAA,MAAM,EAAE;AACN,wBAAA,EAAC,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,iBAAiB,EAAC;wBAC3C,EAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,uBAAuB,EAAE,SAAS,EAAE,gBAAgB,EAAC;AAC9E,qBAAA;AACF,iBAAA;;AAGD;;;AAGG;AASG,MAAO,eAAgB,SAAQ,eAAe,CAAA;uGAAvC,eAAe,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAe,EAHoC,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,CAAA,iBAAA,EAAA,SAAA,CAAA,EAAA,MAAA,EAAA,CAAA,uBAAA,EAAA,QAAA,EAAA,gBAAgB,CAHnE,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,eAAe,EAAE,WAAW,EAAE,eAAe,EAAC,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAM1D,eAAe,EAAA,UAAA,EAAA,CAAA;kBAR3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,mBAAmB;oBAC7B,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,eAAe,EAAE,WAAW,EAAiB,eAAA,EAAC,CAAC;AACrE,oBAAA,MAAM,EAAE;AACN,wBAAA,EAAC,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,iBAAiB,EAAC;wBAC3C,EAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,uBAAuB,EAAE,SAAS,EAAE,gBAAgB,EAAC;AAC9E,qBAAA;AACF,iBAAA;;AAGD;;;;AAIG;AASG,MAAO,SAAa,SAAQ,SAAY,CAAA;uGAAjC,SAAS,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAT,SAAS,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,CAAA,kBAAA,EAAA,SAAA,CAAA,EAAA,IAAA,EAAA,CAAA,eAAA,EAAA,MAAA,CAAA,EAAA,EAAA,SAAA,EANT,CAAC,EAAC,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAC,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAM9C,SAAS,EAAA,UAAA,EAAA,CAAA;kBARrB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,aAAa;oBACvB,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,SAAS,EAAE,WAAW,EAAW,SAAA,EAAC,CAAC;AACzD,oBAAA,MAAM,EAAE;AACN,wBAAA,EAAC,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,kBAAkB,EAAC;AAC5C,wBAAA,EAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,eAAe,EAAC;AACvC,qBAAA;AACF,iBAAA;;AAGD;AAgBM,MAAO,YAAa,SAAQ,YAAY,CAAA;uGAAjC,YAAY,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAZ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAY,EAHZ,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,oCAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,KAAA,EAAA,EAAA,cAAA,EAAA,+CAAA,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAC,CAAC,sLACrD,aAAa,EAAA,QAAA,EAAA,iBAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBAfxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oCAAoC;AAC9C,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,+CAA+C;AACxD,wBAAA,MAAM,EAAE,KAAK;AACd,qBAAA;;;oBAGD,eAAe,EAAE,uBAAuB,CAAC,OAAO;oBAChD,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,QAAQ,EAAE,cAAc;oBACxB,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,YAAY,EAAE,WAAW,EAAc,YAAA,EAAC,CAAC;oBAC/D,OAAO,EAAE,CAAC,aAAa,CAAC;AACzB,iBAAA;;AAGD;AAgBM,MAAO,YAAa,SAAQ,YAAY,CAAA;uGAAjC,YAAY,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAZ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAY,EAHZ,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,oCAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,KAAA,EAAA,EAAA,cAAA,EAAA,wCAAA,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAC,CAAC,sLACrD,aAAa,EAAA,QAAA,EAAA,iBAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBAfxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oCAAoC;AAC9C,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,wCAAwC;AACjD,wBAAA,MAAM,EAAE,KAAK;AACd,qBAAA;;;oBAGD,eAAe,EAAE,uBAAuB,CAAC,OAAO;oBAChD,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,QAAQ,EAAE,cAAc;oBACxB,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,YAAY,EAAE,WAAW,EAAc,YAAA,EAAC,CAAC;oBAC/D,OAAO,EAAE,CAAC,aAAa,CAAC;AACzB,iBAAA;;AAGD;AAgBM,MAAO,MAAO,SAAQ,MAAM,CAAA;uGAArB,MAAM,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAN,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAM,EAHN,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sBAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,KAAA,EAAA,EAAA,cAAA,EAAA,iCAAA,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAC,CAAC,gLACzC,aAAa,EAAA,QAAA,EAAA,iBAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEZ,MAAM,EAAA,UAAA,EAAA,CAAA;kBAflB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sBAAsB;AAChC,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,iCAAiC;AAC1C,wBAAA,MAAM,EAAE,KAAK;AACd,qBAAA;;;oBAGD,eAAe,EAAE,uBAAuB,CAAC,OAAO;oBAChD,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,QAAQ,EAAE,QAAQ;oBAClB,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,MAAM,EAAE,WAAW,EAAQ,MAAA,EAAC,CAAC;oBACnD,OAAO,EAAE,CAAC,aAAa,CAAC;AACzB,iBAAA;;AAGD;AAKM,MAAO,YAAa,SAAQ,YAAY,CAAA;IACnC,iBAAiB,GAAG,qBAAqB;uGADvC,YAAY,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAZ,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,2BAAA,EAAA,SAAA,EAFZ,CAAC,EAAC,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAC,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAEpD,YAAY,EAAA,UAAA,EAAA,CAAA;kBAJxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,2BAA2B;oBACrC,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,YAAY,EAAE,WAAW,EAAc,YAAA,EAAC,CAAC;AAChE,iBAAA;;;ACtHD;;;;;;;;AAQG;AAuBG,MAAO,aAAiB,SAAQ,aAAgB,CAAA;uGAAzC,aAAa,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,EApBd,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;GAST,EASS,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,YAAY,qFAAE,gBAAgB,EAAA,QAAA,EAAA,oBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,aAAa,EAAE,QAAA,EAAA,sCAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,UAAU,yDAAE,OAAO,EAAA,QAAA,EAAA,wBAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEjE,aAAa,EAAA,UAAA,EAAA,CAAA;kBAtBzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iBAAiB;AAC3B,oBAAA,QAAQ,EAAE;;;;;;;;;AAST,EAAA,CAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;;;;;;;oBAOrC,eAAe,EAAE,uBAAuB,CAAC,OAAO;oBAChD,OAAO,EAAE,CAAC,YAAY,EAAE,gBAAgB,EAAE,aAAa,EAAE,UAAU,EAAE,OAAO,CAAC;AAC9E,iBAAA;;;ACVD,MAAM,qBAAqB,GAAG;;IAE5B,QAAQ;IACR,cAAc;;IAGd,gBAAgB;IAChB,eAAe;IACf,YAAY;IACZ,UAAU;IACV,SAAS;IACT,gBAAgB;IAChB,eAAe;;IAGf,aAAa;IACb,OAAO;IACP,aAAa;;IAGb,YAAY;IACZ,MAAM;IACN,YAAY;IACZ,YAAY;IAEZ,aAAa;CACd;MAMY,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAd,cAAc,EAAA,OAAA,EAAA,CAHf,eAAe,EAAE,cAAc;;YA3BzC,QAAQ;YACR,cAAc;;YAGd,gBAAgB;YAChB,eAAe;YACf,YAAY;YACZ,UAAU;YACV,SAAS;YACT,gBAAgB;YAChB,eAAe;;YAGf,aAAa;YACb,OAAO;YACP,aAAa;;YAGb,YAAY;YACZ,MAAM;YACN,YAAY;YACZ,YAAY;AAEZ,YAAA,aAAa,aAKH,eAAe;;YA5BzB,QAAQ;YACR,cAAc;;YAGd,gBAAgB;YAChB,eAAe;YACf,YAAY;YACZ,UAAU;YACV,SAAS;YACT,gBAAgB;YAChB,eAAe;;YAGf,aAAa;YACb,OAAO;YACP,aAAa;;YAGb,YAAY;YACZ,MAAM;YACN,YAAY;YACZ,YAAY;YAEZ,aAAa,CAAA,EAAA,CAAA;AAOF,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,EAHf,OAAA,EAAA,CAAA,eAAe,EAAE,cAAc,EAC/B,eAAe,CAAA,EAAA,CAAA;;2FAEd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAJ1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,eAAe,EAAE,cAAc,EAAE,GAAG,qBAAqB,CAAC;AACpE,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,qBAAqB,CAAC;AAClD,iBAAA;;;ACxCD;;;AAGG;AACH,MAAM,gBAAgB,GAAG,gBAAgB;AAEzC;;;;;;;;;;;;AAYG;AACG,MAAO,kBAA6D,SAAQ,UAAa,CAAA;;AAE5E,IAAA,KAAK;;AAGL,IAAA,WAAW,GAAG,IAAI,eAAe,CAAM,EAAE,CAAC;;AAG1C,IAAA,OAAO,GAAG,IAAI,eAAe,CAAS,EAAE,CAAC;;AAGzC,IAAA,oBAAoB,GAAG,IAAI,OAAO,EAAQ;AAE3D;;;AAGG;IACH,0BAA0B,GAAwB,IAAI;AAEtD;;;;;AAKG;AACH,IAAA,YAAY;;AAGZ,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;;IAGzB,IAAI,IAAI,CAAC,IAAS,EAAA;AAChB,QAAA,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,EAAE;AACtC,QAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;;;AAGrB,QAAA,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE;AACpC,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;;;AAI1B;;;AAGG;AACH,IAAA,IAAI,MAAM,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK;;IAG3B,IAAI,MAAM,CAAC,MAAc,EAAA;AACvB,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;;;AAGzB,QAAA,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE;AACpC,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;;;AAI/B;;;AAGG;AACH,IAAA,IAAI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,KAAK;;IAGnB,IAAI,IAAI,CAAC,IAAoB,EAAA;AAC3B,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI;QACjB,IAAI,CAAC,yBAAyB,EAAE;;AAG1B,IAAA,KAAK;AAEb;;;;;;;;;AASG;AACH,IAAA,IAAI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU;;IAGxB,IAAI,SAAS,CAAC,SAAmB,EAAA;AAC/B,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS;QAC3B,IAAI,CAAC,yBAAyB,EAAE;;AAG1B,IAAA,UAAU;AAElB;;;;;;;;AAQG;AACH,IAAA,mBAAmB,GAAuD,CACxE,IAAO,EACP,YAAoB,KACD;AACnB,QAAA,MAAM,KAAK,GAAI,IAAuC,CAAC,YAAY,CAAC;AAEpE,QAAA,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE;AACzB,YAAA,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC;;;YAIjC,OAAO,WAAW,GAAG,gBAAgB,GAAG,WAAW,GAAG,KAAK;;AAG7D,QAAA,OAAO,KAAK;AACd,KAAC;AAED;;;;;;;;AAQG;AACH,IAAA,QAAQ,GAAsC,CAAC,IAAS,EAAE,IAAa,KAAS;AAC9E,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM;AAC1B,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS;AAChC,QAAA,IAAI,CAAC,MAAM,IAAI,SAAS,IAAI,EAAE,EAAE;AAC9B,YAAA,OAAO,IAAI;;QAGb,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAI;YACxB,IAAI,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,MAAM,CAAC;YAChD,IAAI,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,MAAM,CAAC;;;;AAKhD,YAAA,MAAM,UAAU,GAAG,OAAO,MAAM;AAChC,YAAA,MAAM,UAAU,GAAG,OAAO,MAAM;AAEhC,YAAA,IAAI,UAAU,KAAK,UAAU,EAAE;AAC7B,gBAAA,IAAI,UAAU,KAAK,QAAQ,EAAE;oBAC3B,MAAM,IAAI,EAAE;;AAEd,gBAAA,IAAI,UAAU,KAAK,QAAQ,EAAE;oBAC3B,MAAM,IAAI,EAAE;;;;;;;YAQhB,IAAI,gBAAgB,GAAG,CAAC;YACxB,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,EAAE;;AAEpC,gBAAA,IAAI,MAAM,GAAG,MAAM,EAAE;oBACnB,gBAAgB,GAAG,CAAC;;AACf,qBAAA,IAAI,MAAM,GAAG,MAAM,EAAE;oBAC1B,gBAAgB,GAAG,CAAC,CAAC;;;AAElB,iBAAA,IAAI,MAAM,IAAI,IAAI,EAAE;gBACzB,gBAAgB,GAAG,CAAC;;AACf,iBAAA,IAAI,MAAM,IAAI,IAAI,EAAE;gBACzB,gBAAgB,GAAG,CAAC,CAAC;;AAGvB,YAAA,OAAO,gBAAgB,IAAI,SAAS,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACzD,SAAC,CAAC;AACJ,KAAC;AAED;;;;;;;;;AASG;AACH,IAAA,eAAe,GAAyC,CAAC,IAAO,EAAE,MAAc,KAAa;;QAE3F,MAAM,iBAAiB,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE;;QAErD,OAAO,MAAM,CAAC,MAAM,CAAC,IAA4B,CAAC,CAAC,IAAI,CAAC,KAAK,IAC3D,CAAG,EAAA,KAAK,CAAE,CAAA,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CACrD;AACH,KAAC;AAED,IAAA,WAAA,CAAY,cAAmB,EAAE,EAAA;AAC/B,QAAA,KAAK,EAAE;QACP,IAAI,CAAC,KAAK,GAAG,IAAI,eAAe,CAAM,WAAW,CAAC;QAClD,IAAI,CAAC,yBAAyB,EAAE;;AAGlC;;;;AAIG;IACH,yBAAyB,GAAA;;;;;;;AAOvB,QAAA,MAAM,UAAU,GAAmC,IAAI,CAAC;AACtD,cAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;AACtD,cAAEA,EAAY,CAAC,IAAI,CAAC;AACtB,QAAA,MAAM,UAAU,GAAwC,IAAI,CAAC;AAC3D,cAAG,KAAK,CACJ,IAAI,CAAC,UAAU,CAAC,IAAI,EACpB,IAAI,CAAC,oBAAoB,EACzB,IAAI,CAAC,UAAU,CAAC,WAAW;AAE/B,cAAEA,EAAY,CAAC,IAAI,CAAC;AACtB,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK;;AAE7B,QAAA,MAAM,YAAY,GAAG,aAAa,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CACjE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CACxC;;AAED,QAAA,MAAM,WAAW,GAAG,aAAa,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,CAAC,IAAI,CAChE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CACvC;;AAED,QAAA,MAAM,aAAa,GAAG,aAAa,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC,CAAC,IAAI,CACjE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CACtC;;AAED,QAAA,IAAI,CAAC,0BAA0B,EAAE,WAAW,EAAE;QAC9C,IAAI,CAAC,0BAA0B,GAAG,aAAa,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;AAGhG;;;;AAIG;AACH,IAAA,WAAW,CAAC,IAAS,EAAA;;;;AAInB,QAAA,IAAI,CAAC,YAAY;YACf,IAAI,CAAC,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK;AACrC,kBAAE;kBACA,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AAEhE,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;;QAGjD,OAAO,IAAI,CAAC,YAAY;;AAG1B;;;;AAIG;AACH,IAAA,UAAU,CAAC,IAAS,EAAA;;AAElB,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;AACd,YAAA,OAAO,IAAI;;AAGb,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC;;AAG/C;;;AAGG;AACH,IAAA,SAAS,CAAC,IAAS,EAAA;AACjB,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnB,YAAA,OAAO,IAAI;;AAGb,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ;AACrE,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;;AAGrE;;;;AAIG;AACH,IAAA,gBAAgB,CAAC,kBAA0B,EAAA;AACzC,QAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;AAC1B,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS;YAEhC,IAAI,CAAC,SAAS,EAAE;gBACd;;AAGF,YAAA,SAAS,CAAC,MAAM,GAAG,kBAAkB;;AAGrC,YAAA,IAAI,SAAS,CAAC,SAAS,GAAG,CAAC,EAAE;AAC3B,gBAAA,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC;AAC/E,gBAAA,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,EAAE,aAAa,CAAC;AAEjE,gBAAA,IAAI,YAAY,KAAK,SAAS,CAAC,SAAS,EAAE;AACxC,oBAAA,SAAS,CAAC,SAAS,GAAG,YAAY;;;AAIlC,oBAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE;;;AAGtC,SAAC,CAAC;;AAGJ;;;AAGG;IACH,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE;YACpC,IAAI,CAAC,yBAAyB,EAAE;;QAGlC,OAAO,IAAI,CAAC,WAAW;;AAGzB;;;AAGG;IACH,UAAU,GAAA;AACR,QAAA,IAAI,CAAC,0BAA0B,EAAE,WAAW,EAAE;AAC9C,QAAA,IAAI,CAAC,0BAA0B,GAAG,IAAI;;AAEzC;;;;"}