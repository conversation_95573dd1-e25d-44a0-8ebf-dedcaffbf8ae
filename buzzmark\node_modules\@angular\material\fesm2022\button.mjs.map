{"version": 3, "file": "button.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/button/button.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/button/button.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/button/fab.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/button/module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ChangeDetectionStrategy, Component, ViewEncapsulation} from '@angular/core';\nimport {MAT_ANCHOR_HOST, MAT_BUTTON_HOST, MatAnchorBase, MatButtonBase} from './button-base';\n\n/**\n * Material Design button component. Users interact with a button to perform an action.\n * See https://material.io/components/buttons\n *\n * The `MatButton` class applies to native button elements and captures the appearances for\n * \"text button\", \"outlined button\", and \"contained button\" per the Material Design\n * specification. `MatButton` additionally captures an additional \"flat\" appearance, which matches\n * \"contained\" but without elevation.\n */\n@Component({\n  selector: `\n    button[mat-button], button[mat-raised-button], button[mat-flat-button],\n    button[mat-stroked-button]\n  `,\n  templateUrl: 'button.html',\n  styleUrls: ['button.css', 'button-high-contrast.css'],\n  host: MAT_BUTTON_HOST,\n  exportAs: 'matButton',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class MatButton extends MatButtonBase {}\n\n/**\n * Material Design button component for anchor elements. Anchor elements are used to provide\n * links for the user to navigate across different routes or pages.\n * See https://material.io/components/buttons\n *\n * The `MatAnchor` class applies to native anchor elements and captures the appearances for\n * \"text button\", \"outlined button\", and \"contained button\" per the Material Design\n * specification. `MatAnchor` additionally captures an additional \"flat\" appearance, which matches\n * \"contained\" but without elevation.\n */\n@Component({\n  selector: `a[mat-button], a[mat-raised-button], a[mat-flat-button], a[mat-stroked-button]`,\n  exportAs: 'matButton, matAnchor',\n  host: MAT_ANCHOR_HOST,\n  templateUrl: 'button.html',\n  styleUrls: ['button.css', 'button-high-contrast.css'],\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class MatAnchor extends MatAnchorBase {}\n", "<span\n    class=\"mat-mdc-button-persistent-ripple\"\n    [class.mdc-button__ripple]=\"!_isFab\"\n    [class.mdc-fab__ripple]=\"_isFab\"></span>\n\n<ng-content select=\".material-icons:not([iconPositionEnd]), mat-icon:not([iconPositionEnd]), [matButtonIcon]:not([iconPositionEnd])\">\n</ng-content>\n\n<span class=\"mdc-button__label\"><ng-content></ng-content></span>\n\n<ng-content select=\".material-icons[iconPositionEnd], mat-icon[iconPositionEnd], [matButtonIcon][iconPositionEnd]\">\n</ng-content>\n\n<!--\n  The indicator can't be directly on the button, because MDC uses ::before for high contrast\n  indication and it can't be on the ripple, because it has a border radius and overflow: hidden.\n-->\n<span class=\"mat-focus-indicator\"></span>\n\n<span class=\"mat-mdc-button-touch-target\"></span>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  InjectionToken,\n  Input,\n  ViewEncapsulation,\n  booleanAttribute,\n  inject,\n} from '@angular/core';\n\nimport {<PERSON>Anchor} from './button';\nimport {MAT_ANCHOR_HOST, MAT_BUTTON_HOST, MatButtonBase} from './button-base';\nimport {ThemePalette} from '../core';\n\n/** Default FAB options that can be overridden. */\nexport interface MatFabDefaultOptions {\n  /**\n   * Default theme color of the button. This API is supported in M2 themes\n   * only, it has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/button/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants.\n   */\n  color?: ThemePalette;\n}\n\n/** Injection token to be used to override the default options for FAB. */\nexport const MAT_FAB_DEFAULT_OPTIONS = new InjectionToken<MatFabDefaultOptions>(\n  'mat-mdc-fab-default-options',\n  {\n    providedIn: 'root',\n    factory: MAT_FAB_DEFAULT_OPTIONS_FACTORY,\n  },\n);\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function MAT_FAB_DEFAULT_OPTIONS_FACTORY(): MatFabDefaultOptions {\n  return {\n    // The FAB by default has its color set to accent.\n    color: 'accent',\n  };\n}\n\n// Default FAB configuration.\nconst defaults = MAT_FAB_DEFAULT_OPTIONS_FACTORY();\n\n/**\n * Material Design floating action button (FAB) component. These buttons represent the primary\n * or most common action for users to interact with.\n * See https://material.io/components/buttons-floating-action-button/\n *\n * The `MatFabButton` class has two appearances: normal and extended.\n */\n@Component({\n  selector: `button[mat-fab]`,\n  templateUrl: 'button.html',\n  styleUrl: 'fab.css',\n  host: {\n    ...MAT_BUTTON_HOST,\n    '[class.mdc-fab--extended]': 'extended',\n    '[class.mat-mdc-extended-fab]': 'extended',\n  },\n  exportAs: 'matButton',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class MatFabButton extends MatButtonBase {\n  private _options = inject<MatFabDefaultOptions>(MAT_FAB_DEFAULT_OPTIONS, {optional: true});\n\n  override _isFab = true;\n\n  @Input({transform: booleanAttribute}) extended: boolean;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n    this._options = this._options || defaults;\n    this.color = this._options!.color || defaults.color;\n  }\n}\n\n/**\n * Material Design mini floating action button (FAB) component. These buttons represent the primary\n * or most common action for users to interact with.\n * See https://material.io/components/buttons-floating-action-button/\n */\n@Component({\n  selector: `button[mat-mini-fab]`,\n  templateUrl: 'button.html',\n  styleUrl: 'fab.css',\n  host: MAT_BUTTON_HOST,\n  exportAs: 'matButton',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class MatMiniFabButton extends MatButtonBase {\n  private _options = inject<MatFabDefaultOptions>(MAT_FAB_DEFAULT_OPTIONS, {optional: true});\n\n  override _isFab = true;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n    this._options = this._options || defaults;\n    this.color = this._options!.color || defaults.color;\n  }\n}\n\n/**\n * Material Design floating action button (FAB) component for anchor elements. Anchor elements\n * are used to provide links for the user to navigate across different routes or pages.\n * See https://material.io/components/buttons-floating-action-button/\n *\n * The `MatFabAnchor` class has two appearances: normal and extended.\n */\n@Component({\n  selector: `a[mat-fab]`,\n  templateUrl: 'button.html',\n  styleUrl: 'fab.css',\n  host: {\n    ...MAT_ANCHOR_HOST,\n    '[class.mdc-fab--extended]': 'extended',\n    '[class.mat-mdc-extended-fab]': 'extended',\n  },\n  exportAs: 'matButton, matAnchor',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class MatFabAnchor extends MatAnchor {\n  private _options = inject<MatFabDefaultOptions>(MAT_FAB_DEFAULT_OPTIONS, {optional: true});\n\n  override _isFab = true;\n\n  @Input({transform: booleanAttribute}) extended: boolean;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n    this._options = this._options || defaults;\n    this.color = this._options!.color || defaults.color;\n  }\n}\n\n/**\n * Material Design mini floating action button (FAB) component for anchor elements. Anchor elements\n * are used to provide links for the user to navigate across different routes or pages.\n * See https://material.io/components/buttons-floating-action-button/\n */\n@Component({\n  selector: `a[mat-mini-fab]`,\n  templateUrl: 'button.html',\n  styleUrl: 'fab.css',\n  host: MAT_ANCHOR_HOST,\n  exportAs: 'matButton, matAnchor',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class MatMiniFabAnchor extends MatAnchor {\n  private _options = inject<MatFabDefaultOptions>(MAT_FAB_DEFAULT_OPTIONS, {optional: true});\n\n  override _isFab = true;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n    this._options = this._options || defaults;\n    this.color = this._options!.color || defaults.color;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule, MatRippleModule} from '../core';\nimport {<PERSON><PERSON><PERSON><PERSON>, MatButton} from './button';\nimport {MatFabAnchor, MatFabButton, MatMiniFabAnchor, MatMiniFabButton} from './fab';\nimport {MatIconAnchor, MatIconButton} from './icon-button';\n\n@NgModule({\n  imports: [\n    MatCommonModule,\n    MatRippleModule,\n    MatAnchor,\n    MatButton,\n    MatIconAnchor,\n    MatMiniFabAnchor,\n    MatMiniFabButton,\n    MatIconButton,\n    MatFabAnchor,\n    MatFabButton,\n  ],\n  exports: [\n    MatAnchor,\n    MatButton,\n    MatIconAnchor,\n    MatIconButton,\n    MatMiniFabAnchor,\n    MatMiniFabButton,\n    MatFabAnchor,\n    MatFabButton,\n    MatCommonModule,\n  ],\n})\nexport class MatButtonModule {}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAWA;;;;;;;;AAQG;AAaG,MAAO,SAAU,SAAQ,aAAa,CAAA;uGAA/B,SAAS,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAT,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAS,2nBChCtB,8yBAoBA,EAAA,MAAA,EAAA,CAAA,ggkBAAA,EAAA,kVAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FDYa,SAAS,EAAA,UAAA,EAAA,CAAA;kBAZrB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA;;;GAGT,EAGK,IAAA,EAAA,eAAe,EACX,QAAA,EAAA,WAAW,EACN,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,8yBAAA,EAAA,MAAA,EAAA,CAAA,ggkBAAA,EAAA,kVAAA,CAAA,EAAA;;AAIjD;;;;;;;;;AASG;AAUG,MAAO,SAAU,SAAQ,aAAa,CAAA;uGAA/B,SAAS,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAT,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAS,0qBCrDtB,8yBAoBA,EAAA,MAAA,EAAA,CAAA,ggkBAAA,EAAA,kVAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FDiCa,SAAS,EAAA,UAAA,EAAA,CAAA;kBATrB,SAAS;+BACE,CAAgF,8EAAA,CAAA,EAAA,QAAA,EAChF,sBAAsB,EAAA,IAAA,EAC1B,eAAe,EAAA,aAAA,EAGN,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,8yBAAA,EAAA,MAAA,EAAA,CAAA,ggkBAAA,EAAA,kVAAA,CAAA,EAAA;;;AEjBjD;MACa,uBAAuB,GAAG,IAAI,cAAc,CACvD,6BAA6B,EAC7B;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,+BAA+B;AACzC,CAAA;AAGH;;;;AAIG;SACa,+BAA+B,GAAA;IAC7C,OAAO;;AAEL,QAAA,KAAK,EAAE,QAAQ;KAChB;AACH;AAEA;AACA,MAAM,QAAQ,GAAG,+BAA+B,EAAE;AAElD;;;;;;AAMG;AAcG,MAAO,YAAa,SAAQ,aAAa,CAAA;IACrC,QAAQ,GAAG,MAAM,CAAuB,uBAAuB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;IAEjF,MAAM,GAAG,IAAI;AAEgB,IAAA,QAAQ;AAI9C,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;QACP,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,QAAQ;AACzC,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAS,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK;;uGAZ1C,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAZ,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAKJ,gBAAgB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,eAAA,EAAA,yBAAA,EAAA,oBAAA,EAAA,oBAAA,EAAA,+BAAA,EAAA,UAAA,EAAA,2CAAA,EAAA,qBAAA,EAAA,+BAAA,EAAA,uCAAA,EAAA,oBAAA,EAAA,QAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,OAAA,EAAA,iCAAA,EAAA,yBAAA,EAAA,UAAA,EAAA,4BAAA,EAAA,UAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EDnFrC,8yBAoBA,EAAA,MAAA,EAAA,CAAA,+lSAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FC0Da,YAAY,EAAA,UAAA,EAAA,CAAA;kBAbxB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,eAAA,CAAiB,EAGrB,IAAA,EAAA;AACJ,wBAAA,GAAG,eAAe;AAClB,wBAAA,2BAA2B,EAAE,UAAU;AACvC,wBAAA,8BAA8B,EAAE,UAAU;qBAC3C,EACS,QAAA,EAAA,WAAW,iBACN,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,8yBAAA,EAAA,MAAA,EAAA,CAAA,+lSAAA,CAAA,EAAA;wDAOT,QAAQ,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;;AAWtC;;;;AAIG;AAUG,MAAO,gBAAiB,SAAQ,aAAa,CAAA;IACzC,QAAQ,GAAG,MAAM,CAAuB,uBAAuB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;IAEjF,MAAM,GAAG,IAAI;AAItB,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;QACP,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,QAAQ;AACzC,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAS,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK;;uGAV1C,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,gBAAgB,8hBD5G7B,8yBAoBA,EAAA,MAAA,EAAA,CAAA,+lSAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FCwFa,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAT5B,SAAS;+BACE,CAAsB,oBAAA,CAAA,EAAA,IAAA,EAG1B,eAAe,EAAA,QAAA,EACX,WAAW,EAAA,aAAA,EACN,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,8yBAAA,EAAA,MAAA,EAAA,CAAA,+lSAAA,CAAA,EAAA;;AAgBjD;;;;;;AAMG;AAcG,MAAO,YAAa,SAAQ,SAAS,CAAA;IACjC,QAAQ,GAAG,MAAM,CAAuB,uBAAuB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;IAEjF,MAAM,GAAG,IAAI;AAEgB,IAAA,QAAQ;AAI9C,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;QACP,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,QAAQ;AACzC,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAS,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK;;uGAZ1C,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAZ,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAKJ,gBAAgB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,eAAA,EAAA,yBAAA,EAAA,+BAAA,EAAA,UAAA,EAAA,2CAAA,EAAA,qBAAA,EAAA,+BAAA,EAAA,uCAAA,EAAA,eAAA,EAAA,kDAAA,EAAA,oBAAA,EAAA,oBAAA,EAAA,oBAAA,EAAA,QAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,OAAA,EAAA,iCAAA,EAAA,yBAAA,EAAA,UAAA,EAAA,4BAAA,EAAA,UAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA,WAAA,EAAA,WAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EDnJrC,8yBAoBA,EAAA,MAAA,EAAA,CAAA,+lSAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FC0Ha,YAAY,EAAA,UAAA,EAAA,CAAA;kBAbxB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,CAAY,EAGhB,IAAA,EAAA;AACJ,wBAAA,GAAG,eAAe;AAClB,wBAAA,2BAA2B,EAAE,UAAU;AACvC,wBAAA,8BAA8B,EAAE,UAAU;qBAC3C,EACS,QAAA,EAAA,sBAAsB,iBACjB,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,8yBAAA,EAAA,MAAA,EAAA,CAAA,+lSAAA,CAAA,EAAA;wDAOT,QAAQ,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;;AAWtC;;;;AAIG;AAUG,MAAO,gBAAiB,SAAQ,SAAS,CAAA;IACrC,QAAQ,GAAG,MAAM,CAAuB,uBAAuB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;IAEjF,MAAM,GAAG,IAAI;AAItB,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;QACP,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,QAAQ;AACzC,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAS,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK;;uGAV1C,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,gBAAgB,2mBD5K7B,8yBAoBA,EAAA,MAAA,EAAA,CAAA,+lSAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FCwJa,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAT5B,SAAS;+BACE,CAAiB,eAAA,CAAA,EAAA,IAAA,EAGrB,eAAe,EAAA,QAAA,EACX,sBAAsB,EAAA,aAAA,EACjB,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,8yBAAA,EAAA,MAAA,EAAA,CAAA,+lSAAA,CAAA,EAAA;;;MCnIpC,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YAvBxB,eAAe;YACf,eAAe;YACf,SAAS;YACT,SAAS;YACT,aAAa;YACb,gBAAgB;YAChB,gBAAgB;YAChB,aAAa;YACb,YAAY;AACZ,YAAA,YAAY,aAGZ,SAAS;YACT,SAAS;YACT,aAAa;YACb,aAAa;YACb,gBAAgB;YAChB,gBAAgB;YAChB,YAAY;YACZ,YAAY;YACZ,eAAe,CAAA,EAAA,CAAA;AAGN,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YAvBxB,eAAe;AACf,YAAA,eAAe,EAmBf,eAAe,CAAA,EAAA,CAAA;;2FAGN,eAAe,EAAA,UAAA,EAAA,CAAA;kBAzB3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE;wBACP,eAAe;wBACf,eAAe;wBACf,SAAS;wBACT,SAAS;wBACT,aAAa;wBACb,gBAAgB;wBAChB,gBAAgB;wBAChB,aAAa;wBACb,YAAY;wBACZ,YAAY;AACb,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,SAAS;wBACT,SAAS;wBACT,aAAa;wBACb,aAAa;wBACb,gBAAgB;wBAChB,gBAAgB;wBAChB,YAAY;wBACZ,YAAY;wBACZ,eAAe;AAChB,qBAAA;AACF,iBAAA;;;;;"}