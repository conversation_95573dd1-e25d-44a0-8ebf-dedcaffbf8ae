{"version": 3, "file": "radio.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/radio/radio.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/radio/radio.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/radio/module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {_IdGenerator, FocusMonitor, FocusOrigin} from '@angular/cdk/a11y';\nimport {UniqueSelectionDispatcher} from '@angular/cdk/collections';\nimport {\n  ANIMATION_MODULE_TYPE,\n  AfterContentInit,\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChildren,\n  Directive,\n  DoCheck,\n  ElementRef,\n  EventEmitter,\n  InjectionToken,\n  Injector,\n  Input,\n  NgZone,\n  OnDestroy,\n  OnInit,\n  Output,\n  QueryList,\n  ViewChild,\n  ViewEncapsulation,\n  afterNextRender,\n  booleanAttribute,\n  forwardRef,\n  inject,\n  numberAttribute,\n  HostAttributeToken,\n  Renderer2,\n} from '@angular/core';\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from '@angular/forms';\nimport {MatR<PERSON>ple, ThemePalette, _MatInternalFormField, _StructuralStylesLoader} from '../core';\nimport {Subscription} from 'rxjs';\nimport {_CdkPrivateStyleLoader} from '@angular/cdk/private';\n\n/** Change event object emitted by radio button and radio group. */\nexport class MatRadioChange<T = any> {\n  constructor(\n    /** The radio button that emits the change event. */\n    public source: MatRadioButton,\n    /** The value of the radio button. */\n    public value: T,\n  ) {}\n}\n\n/**\n * Provider Expression that allows mat-radio-group to register as a ControlValueAccessor. This\n * allows it to support [(ngModel)] and ngControl.\n * @docs-private\n */\nexport const MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR: any = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatRadioGroup),\n  multi: true,\n};\n\n/**\n * Injection token that can be used to inject instances of `MatRadioGroup`. It serves as\n * alternative token to the actual `MatRadioGroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nexport const MAT_RADIO_GROUP = new InjectionToken<MatRadioGroup>('MatRadioGroup');\n\nexport interface MatRadioDefaultOptions {\n  /**\n   * Theme color of the radio button. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/radio/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  color: ThemePalette;\n\n  /** Whether disabled radio buttons should be interactive. */\n  disabledInteractive?: boolean;\n}\n\nexport const MAT_RADIO_DEFAULT_OPTIONS = new InjectionToken<MatRadioDefaultOptions>(\n  'mat-radio-default-options',\n  {\n    providedIn: 'root',\n    factory: MAT_RADIO_DEFAULT_OPTIONS_FACTORY,\n  },\n);\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function MAT_RADIO_DEFAULT_OPTIONS_FACTORY(): MatRadioDefaultOptions {\n  return {\n    color: 'accent',\n    disabledInteractive: false,\n  };\n}\n\n/**\n * A group of radio buttons. May contain one or more `<mat-radio-button>` elements.\n */\n@Directive({\n  selector: 'mat-radio-group',\n  exportAs: 'matRadioGroup',\n  providers: [\n    MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR,\n    {provide: MAT_RADIO_GROUP, useExisting: MatRadioGroup},\n  ],\n  host: {\n    'role': 'radiogroup',\n    'class': 'mat-mdc-radio-group',\n  },\n})\nexport class MatRadioGroup implements AfterContentInit, OnDestroy, ControlValueAccessor {\n  private _changeDetector = inject(ChangeDetectorRef);\n\n  /** Selected value for the radio group. */\n  private _value: any = null;\n\n  /** The HTML name attribute applied to radio buttons in this group. */\n  private _name: string = inject(_IdGenerator).getId('mat-radio-group-');\n\n  /** The currently selected radio button. Should match value. */\n  private _selected: MatRadioButton | null = null;\n\n  /** Whether the `value` has been set to its initial value. */\n  private _isInitialized: boolean = false;\n\n  /** Whether the labels should appear after or before the radio-buttons. Defaults to 'after' */\n  private _labelPosition: 'before' | 'after' = 'after';\n\n  /** Whether the radio group is disabled. */\n  private _disabled: boolean = false;\n\n  /** Whether the radio group is required. */\n  private _required: boolean = false;\n\n  /** Subscription to changes in amount of radio buttons. */\n  private _buttonChanges: Subscription;\n\n  /** The method to be called in order to update ngModel */\n  _controlValueAccessorChangeFn: (value: any) => void = () => {};\n\n  /**\n   * onTouch function registered via registerOnTouch (ControlValueAccessor).\n   * @docs-private\n   */\n  onTouched: () => any = () => {};\n\n  /**\n   * Event emitted when the group value changes.\n   * Change events are only emitted when the value changes due to user interaction with\n   * a radio button (the same behavior as `<input type-\"radio\">`).\n   */\n  @Output() readonly change: EventEmitter<MatRadioChange> = new EventEmitter<MatRadioChange>();\n\n  /** Child radio buttons. */\n  @ContentChildren(forwardRef(() => MatRadioButton), {descendants: true})\n  _radios: QueryList<MatRadioButton>;\n\n  /**\n   * Theme color of the radio buttons in the group. This API is supported in M2\n   * themes only, it has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/radio/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  @Input() color: ThemePalette;\n\n  /** Name of the radio button group. All radio buttons inside this group will use this name. */\n  @Input()\n  get name(): string {\n    return this._name;\n  }\n  set name(value: string) {\n    this._name = value;\n    this._updateRadioButtonNames();\n  }\n\n  /** Whether the labels should appear after or before the radio-buttons. Defaults to 'after' */\n  @Input()\n  get labelPosition(): 'before' | 'after' {\n    return this._labelPosition;\n  }\n  set labelPosition(v) {\n    this._labelPosition = v === 'before' ? 'before' : 'after';\n    this._markRadiosForCheck();\n  }\n\n  /**\n   * Value for the radio-group. Should equal the value of the selected radio button if there is\n   * a corresponding radio button with a matching value. If there is not such a corresponding\n   * radio button, this value persists to be applied in case a new radio button is added with a\n   * matching value.\n   */\n  @Input()\n  get value(): any {\n    return this._value;\n  }\n  set value(newValue: any) {\n    if (this._value !== newValue) {\n      // Set this before proceeding to ensure no circular loop occurs with selection.\n      this._value = newValue;\n\n      this._updateSelectedRadioFromValue();\n      this._checkSelectedRadioButton();\n    }\n  }\n\n  _checkSelectedRadioButton() {\n    if (this._selected && !this._selected.checked) {\n      this._selected.checked = true;\n    }\n  }\n\n  /**\n   * The currently selected radio button. If set to a new radio button, the radio group value\n   * will be updated to match the new selected button.\n   */\n  @Input()\n  get selected() {\n    return this._selected;\n  }\n  set selected(selected: MatRadioButton | null) {\n    this._selected = selected;\n    this.value = selected ? selected.value : null;\n    this._checkSelectedRadioButton();\n  }\n\n  /** Whether the radio group is disabled */\n  @Input({transform: booleanAttribute})\n  get disabled(): boolean {\n    return this._disabled;\n  }\n  set disabled(value: boolean) {\n    this._disabled = value;\n    this._markRadiosForCheck();\n  }\n\n  /** Whether the radio group is required */\n  @Input({transform: booleanAttribute})\n  get required(): boolean {\n    return this._required;\n  }\n  set required(value: boolean) {\n    this._required = value;\n    this._markRadiosForCheck();\n  }\n\n  /** Whether buttons in the group should be interactive while they're disabled. */\n  @Input({transform: booleanAttribute})\n  get disabledInteractive(): boolean {\n    return this._disabledInteractive;\n  }\n  set disabledInteractive(value: boolean) {\n    this._disabledInteractive = value;\n    this._markRadiosForCheck();\n  }\n  private _disabledInteractive = false;\n\n  constructor(...args: unknown[]);\n\n  constructor() {}\n\n  /**\n   * Initialize properties once content children are available.\n   * This allows us to propagate relevant attributes to associated buttons.\n   */\n  ngAfterContentInit() {\n    // Mark this component as initialized in AfterContentInit because the initial value can\n    // possibly be set by NgModel on MatRadioGroup, and it is possible that the OnInit of the\n    // NgModel occurs *after* the OnInit of the MatRadioGroup.\n    this._isInitialized = true;\n\n    // Clear the `selected` button when it's destroyed since the tabindex of the rest of the\n    // buttons depends on it. Note that we don't clear the `value`, because the radio button\n    // may be swapped out with a similar one and there are some internal apps that depend on\n    // that behavior.\n    this._buttonChanges = this._radios.changes.subscribe(() => {\n      if (this.selected && !this._radios.find(radio => radio === this.selected)) {\n        this._selected = null;\n      }\n    });\n  }\n\n  ngOnDestroy() {\n    this._buttonChanges?.unsubscribe();\n  }\n\n  /**\n   * Mark this group as being \"touched\" (for ngModel). Meant to be called by the contained\n   * radio buttons upon their blur.\n   */\n  _touch() {\n    if (this.onTouched) {\n      this.onTouched();\n    }\n  }\n\n  private _updateRadioButtonNames(): void {\n    if (this._radios) {\n      this._radios.forEach(radio => {\n        radio.name = this.name;\n        radio._markForCheck();\n      });\n    }\n  }\n\n  /** Updates the `selected` radio button from the internal _value state. */\n  private _updateSelectedRadioFromValue(): void {\n    // If the value already matches the selected radio, do nothing.\n    const isAlreadySelected = this._selected !== null && this._selected.value === this._value;\n\n    if (this._radios && !isAlreadySelected) {\n      this._selected = null;\n      this._radios.forEach(radio => {\n        radio.checked = this.value === radio.value;\n        if (radio.checked) {\n          this._selected = radio;\n        }\n      });\n    }\n  }\n\n  /** Dispatch change event with current selection and group value. */\n  _emitChangeEvent(): void {\n    if (this._isInitialized) {\n      this.change.emit(new MatRadioChange(this._selected!, this._value));\n    }\n  }\n\n  _markRadiosForCheck() {\n    if (this._radios) {\n      this._radios.forEach(radio => radio._markForCheck());\n    }\n  }\n\n  /**\n   * Sets the model value. Implemented as part of ControlValueAccessor.\n   * @param value\n   */\n  writeValue(value: any) {\n    this.value = value;\n    this._changeDetector.markForCheck();\n  }\n\n  /**\n   * Registers a callback to be triggered when the model value changes.\n   * Implemented as part of ControlValueAccessor.\n   * @param fn Callback to be registered.\n   */\n  registerOnChange(fn: (value: any) => void) {\n    this._controlValueAccessorChangeFn = fn;\n  }\n\n  /**\n   * Registers a callback to be triggered when the control is touched.\n   * Implemented as part of ControlValueAccessor.\n   * @param fn Callback to be registered.\n   */\n  registerOnTouched(fn: any) {\n    this.onTouched = fn;\n  }\n\n  /**\n   * Sets the disabled state of the control. Implemented as a part of ControlValueAccessor.\n   * @param isDisabled Whether the control should be disabled.\n   */\n  setDisabledState(isDisabled: boolean) {\n    this.disabled = isDisabled;\n    this._changeDetector.markForCheck();\n  }\n}\n\n@Component({\n  selector: 'mat-radio-button',\n  templateUrl: 'radio.html',\n  styleUrl: 'radio.css',\n  host: {\n    'class': 'mat-mdc-radio-button',\n    '[attr.id]': 'id',\n    '[class.mat-primary]': 'color === \"primary\"',\n    '[class.mat-accent]': 'color === \"accent\"',\n    '[class.mat-warn]': 'color === \"warn\"',\n    '[class.mat-mdc-radio-checked]': 'checked',\n    '[class.mat-mdc-radio-disabled]': 'disabled',\n    '[class.mat-mdc-radio-disabled-interactive]': 'disabledInteractive',\n    '[class._mat-animation-noopable]': '_noopAnimations',\n    // Needs to be removed since it causes some a11y issues (see #21266).\n    '[attr.tabindex]': 'null',\n    '[attr.aria-label]': 'null',\n    '[attr.aria-labelledby]': 'null',\n    '[attr.aria-describedby]': 'null',\n    // Note: under normal conditions focus shouldn't land on this element, however it may be\n    // programmatically set, for example inside of a focus trap, in this case we want to forward\n    // the focus to the native element.\n    '(focus)': '_inputElement.nativeElement.focus()',\n  },\n  exportAs: 'matRadioButton',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [MatRipple, _MatInternalFormField],\n})\nexport class MatRadioButton implements OnInit, AfterViewInit, DoCheck, OnDestroy {\n  protected _elementRef = inject(ElementRef);\n  private _changeDetector = inject(ChangeDetectorRef);\n  private _focusMonitor = inject(FocusMonitor);\n  private _radioDispatcher = inject(UniqueSelectionDispatcher);\n  private _defaultOptions = inject<MatRadioDefaultOptions>(MAT_RADIO_DEFAULT_OPTIONS, {\n    optional: true,\n  });\n\n  private _ngZone = inject(NgZone);\n  private _renderer = inject(Renderer2);\n  private _uniqueId = inject(_IdGenerator).getId('mat-radio-');\n  private _cleanupClick: (() => void) | undefined;\n\n  /** The unique ID for the radio button. */\n  @Input() id: string = this._uniqueId;\n\n  /** Analog to HTML 'name' attribute used to group radios for unique selection. */\n  @Input() name: string;\n\n  /** Used to set the 'aria-label' attribute on the underlying input element. */\n  @Input('aria-label') ariaLabel: string;\n\n  /** The 'aria-labelledby' attribute takes precedence as the element's text alternative. */\n  @Input('aria-labelledby') ariaLabelledby: string;\n\n  /** The 'aria-describedby' attribute is read after the element's label and field type. */\n  @Input('aria-describedby') ariaDescribedby: string;\n\n  /** Whether ripples are disabled inside the radio button */\n  @Input({transform: booleanAttribute})\n  disableRipple: boolean = false;\n\n  /** Tabindex of the radio button. */\n  @Input({\n    transform: (value: unknown) => (value == null ? 0 : numberAttribute(value)),\n  })\n  tabIndex: number = 0;\n\n  /** Whether this radio button is checked. */\n  @Input({transform: booleanAttribute})\n  get checked(): boolean {\n    return this._checked;\n  }\n  set checked(value: boolean) {\n    if (this._checked !== value) {\n      this._checked = value;\n      if (value && this.radioGroup && this.radioGroup.value !== this.value) {\n        this.radioGroup.selected = this;\n      } else if (!value && this.radioGroup && this.radioGroup.value === this.value) {\n        // When unchecking the selected radio button, update the selected radio\n        // property on the group.\n        this.radioGroup.selected = null;\n      }\n\n      if (value) {\n        // Notify all radio buttons with the same name to un-check.\n        this._radioDispatcher.notify(this.id, this.name);\n      }\n      this._changeDetector.markForCheck();\n    }\n  }\n\n  /** The value of this radio button. */\n  @Input()\n  get value(): any {\n    return this._value;\n  }\n  set value(value: any) {\n    if (this._value !== value) {\n      this._value = value;\n      if (this.radioGroup !== null) {\n        if (!this.checked) {\n          // Update checked when the value changed to match the radio group's value\n          this.checked = this.radioGroup.value === value;\n        }\n        if (this.checked) {\n          this.radioGroup.selected = this;\n        }\n      }\n    }\n  }\n\n  /** Whether the label should appear after or before the radio button. Defaults to 'after' */\n  @Input()\n  get labelPosition(): 'before' | 'after' {\n    return this._labelPosition || (this.radioGroup && this.radioGroup.labelPosition) || 'after';\n  }\n  set labelPosition(value) {\n    this._labelPosition = value;\n  }\n  private _labelPosition: 'before' | 'after';\n\n  /** Whether the radio button is disabled. */\n  @Input({transform: booleanAttribute})\n  get disabled(): boolean {\n    return this._disabled || (this.radioGroup !== null && this.radioGroup.disabled);\n  }\n  set disabled(value: boolean) {\n    this._setDisabled(value);\n  }\n\n  /** Whether the radio button is required. */\n  @Input({transform: booleanAttribute})\n  get required(): boolean {\n    return this._required || (this.radioGroup && this.radioGroup.required);\n  }\n  set required(value: boolean) {\n    if (value !== this._required) {\n      this._changeDetector.markForCheck();\n    }\n    this._required = value;\n  }\n\n  /**\n   * Theme color of the radio button. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/radio/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  @Input()\n  get color(): ThemePalette {\n    // As per M2 design specifications the selection control radio should use the accent color\n    // palette by default. https://m2.material.io/components/radio-buttons#specs\n    return (\n      this._color ||\n      (this.radioGroup && this.radioGroup.color) ||\n      (this._defaultOptions && this._defaultOptions.color) ||\n      'accent'\n    );\n  }\n  set color(newValue: ThemePalette) {\n    this._color = newValue;\n  }\n  private _color: ThemePalette;\n\n  /** Whether the radio button should remain interactive when it is disabled. */\n  @Input({transform: booleanAttribute})\n  get disabledInteractive(): boolean {\n    return (\n      this._disabledInteractive || (this.radioGroup !== null && this.radioGroup.disabledInteractive)\n    );\n  }\n  set disabledInteractive(value: boolean) {\n    this._disabledInteractive = value;\n  }\n  private _disabledInteractive: boolean;\n\n  /**\n   * Event emitted when the checked state of this radio button changes.\n   * Change events are only emitted when the value changes due to user interaction with\n   * the radio button (the same behavior as `<input type-\"radio\">`).\n   */\n  @Output() readonly change: EventEmitter<MatRadioChange> = new EventEmitter<MatRadioChange>();\n\n  /** The parent radio group. May or may not be present. */\n  radioGroup: MatRadioGroup;\n\n  /** ID of the native input element inside `<mat-radio-button>` */\n  get inputId(): string {\n    return `${this.id || this._uniqueId}-input`;\n  }\n\n  /** Whether this radio is checked. */\n  private _checked: boolean = false;\n\n  /** Whether this radio is disabled. */\n  private _disabled: boolean;\n\n  /** Whether this radio is required. */\n  private _required: boolean;\n\n  /** Value assigned to this radio. */\n  private _value: any = null;\n\n  /** Unregister function for _radioDispatcher */\n  private _removeUniqueSelectionListener: () => void = () => {};\n\n  /** Previous value of the input's tabindex. */\n  private _previousTabIndex: number | undefined;\n\n  /** The native `<input type=radio>` element */\n  @ViewChild('input') _inputElement: ElementRef<HTMLInputElement>;\n\n  /** Trigger elements for the ripple events. */\n  @ViewChild('formField', {read: ElementRef, static: true})\n  _rippleTrigger: ElementRef<HTMLElement>;\n\n  /** Whether animations are disabled. */\n  _noopAnimations: boolean;\n\n  private _injector = inject(Injector);\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    const radioGroup = inject<MatRadioGroup>(MAT_RADIO_GROUP, {optional: true})!;\n    const animationMode = inject(ANIMATION_MODULE_TYPE, {optional: true});\n    const tabIndex = inject(new HostAttributeToken('tabindex'), {optional: true});\n\n    // Assertions. Ideally these should be stripped out by the compiler.\n    // TODO(jelbourn): Assert that there's no name binding AND a parent radio group.\n    this.radioGroup = radioGroup;\n    this._noopAnimations = animationMode === 'NoopAnimations';\n    this._disabledInteractive = this._defaultOptions?.disabledInteractive ?? false;\n\n    if (tabIndex) {\n      this.tabIndex = numberAttribute(tabIndex, 0);\n    }\n  }\n\n  /** Focuses the radio button. */\n  focus(options?: FocusOptions, origin?: FocusOrigin): void {\n    if (origin) {\n      this._focusMonitor.focusVia(this._inputElement, origin, options);\n    } else {\n      this._inputElement.nativeElement.focus(options);\n    }\n  }\n\n  /**\n   * Marks the radio button as needing checking for change detection.\n   * This method is exposed because the parent radio group will directly\n   * update bound properties of the radio button.\n   */\n  _markForCheck() {\n    // When group value changes, the button will not be notified. Use `markForCheck` to explicit\n    // update radio button's status\n    this._changeDetector.markForCheck();\n  }\n\n  ngOnInit() {\n    if (this.radioGroup) {\n      // If the radio is inside a radio group, determine if it should be checked\n      this.checked = this.radioGroup.value === this._value;\n\n      if (this.checked) {\n        this.radioGroup.selected = this;\n      }\n\n      // Copy name from parent radio group\n      this.name = this.radioGroup.name;\n    }\n\n    this._removeUniqueSelectionListener = this._radioDispatcher.listen((id, name) => {\n      if (id !== this.id && name === this.name) {\n        this.checked = false;\n      }\n    });\n  }\n\n  ngDoCheck(): void {\n    this._updateTabIndex();\n  }\n\n  ngAfterViewInit() {\n    this._updateTabIndex();\n    this._focusMonitor.monitor(this._elementRef, true).subscribe(focusOrigin => {\n      if (!focusOrigin && this.radioGroup) {\n        this.radioGroup._touch();\n      }\n    });\n\n    // We bind this outside of the zone, because:\n    // 1. Its logic is completely DOM-related so we can avoid some change detections.\n    // 2. There appear to be some internal tests that break when this triggers a change detection.\n    this._ngZone.runOutsideAngular(() => {\n      this._cleanupClick = this._renderer.listen(\n        this._inputElement.nativeElement,\n        'click',\n        this._onInputClick,\n      );\n    });\n  }\n\n  ngOnDestroy() {\n    this._cleanupClick?.();\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    this._removeUniqueSelectionListener();\n  }\n\n  /** Dispatch change event with current value. */\n  private _emitChangeEvent(): void {\n    this.change.emit(new MatRadioChange(this, this._value));\n  }\n\n  _isRippleDisabled() {\n    return this.disableRipple || this.disabled;\n  }\n\n  /** Triggered when the radio button receives an interaction from the user. */\n  _onInputInteraction(event: Event) {\n    // We always have to stop propagation on the change event.\n    // Otherwise the change event, from the input element, will bubble up and\n    // emit its event object to the `change` output.\n    event.stopPropagation();\n\n    if (!this.checked && !this.disabled) {\n      const groupValueChanged = this.radioGroup && this.value !== this.radioGroup.value;\n      this.checked = true;\n      this._emitChangeEvent();\n\n      if (this.radioGroup) {\n        this.radioGroup._controlValueAccessorChangeFn(this.value);\n        if (groupValueChanged) {\n          this.radioGroup._emitChangeEvent();\n        }\n      }\n    }\n  }\n\n  /** Triggered when the user clicks on the touch target. */\n  _onTouchTargetClick(event: Event) {\n    this._onInputInteraction(event);\n\n    if (!this.disabled || this.disabledInteractive) {\n      // Normally the input should be focused already, but if the click\n      // comes from the touch target, then we might have to focus it ourselves.\n      this._inputElement?.nativeElement.focus();\n    }\n  }\n\n  /** Sets the disabled state and marks for check if a change occurred. */\n  protected _setDisabled(value: boolean) {\n    if (this._disabled !== value) {\n      this._disabled = value;\n      this._changeDetector.markForCheck();\n    }\n  }\n\n  /** Called when the input is clicked. */\n  private _onInputClick = (event: Event) => {\n    // If the input is disabled while interactive, we need to prevent the\n    // selection from happening in this event handler. Note that even though\n    // this happens on `click` events, the logic applies when the user is\n    // navigating with the keyboard as well. An alternative way of doing\n    // this is by resetting the `checked` state in the `change` callback but\n    // it isn't optimal, because it can allow a pre-checked disabled button\n    // to be un-checked. This approach seems to cover everything.\n    if (this.disabled && this.disabledInteractive) {\n      event.preventDefault();\n    }\n  };\n\n  /** Gets the tabindex for the underlying input element. */\n  private _updateTabIndex() {\n    const group = this.radioGroup;\n    let value: number;\n\n    // Implement a roving tabindex if the button is inside a group. For most cases this isn't\n    // necessary, because the browser handles the tab order for inputs inside a group automatically,\n    // but we need an explicitly higher tabindex for the selected button in order for things like\n    // the focus trap to pick it up correctly.\n    if (!group || !group.selected || this.disabled) {\n      value = this.tabIndex;\n    } else {\n      value = group.selected === this ? this.tabIndex : -1;\n    }\n\n    if (value !== this._previousTabIndex) {\n      // We have to set the tabindex directly on the DOM node, because it depends on\n      // the selected state which is prone to \"changed after checked errors\".\n      const input: HTMLInputElement | undefined = this._inputElement?.nativeElement;\n\n      if (input) {\n        input.setAttribute('tabindex', value + '');\n        this._previousTabIndex = value;\n        // Wait for any pending tabindex changes to be applied\n        afterNextRender(\n          () => {\n            queueMicrotask(() => {\n              // The radio group uses a \"selection follows focus\" pattern for tab management, so if this\n              // radio button is currently focused and another radio button in the group becomes\n              // selected, we should move focus to the newly selected radio button to maintain\n              // consistency between the focused and selected states.\n              if (\n                group &&\n                group.selected &&\n                group.selected !== this &&\n                document.activeElement === input\n              ) {\n                group.selected?._inputElement.nativeElement.focus();\n                // If this radio button still has focus, the selected one must be disabled. In this\n                // case the radio group as a whole should lose focus.\n                if (document.activeElement === input) {\n                  this._inputElement.nativeElement.blur();\n                }\n              }\n            });\n          },\n          {injector: this._injector},\n        );\n      }\n    }\n  }\n}\n", "<div mat-internal-form-field [labelPosition]=\"labelPosition\" #formField>\n  <div class=\"mdc-radio\" [class.mdc-radio--disabled]=\"disabled\">\n    <!-- Render this element first so the input is on top. -->\n    <div class=\"mat-mdc-radio-touch-target\" (click)=\"_onTouchTargetClick($event)\"></div>\n    <!--\n      Note that we set `aria-invalid=\"false\"` on the input, because otherwise some screen readers\n      will read out \"required, invalid data\" for each radio button that hasn't been checked.\n      An alternate approach is to use `aria-required` instead of `required`, however we have an\n      internal check which enforces that elements marked as `aria-required` also have the `required`\n      attribute which ends up re-introducing the issue for us.\n    -->\n    <input #input class=\"mdc-radio__native-control\" type=\"radio\"\n           [id]=\"inputId\"\n           [checked]=\"checked\"\n           [disabled]=\"disabled && !disabledInteractive\"\n           [attr.name]=\"name\"\n           [attr.value]=\"value\"\n           [required]=\"required\"\n           aria-invalid=\"false\"\n           [attr.aria-label]=\"ariaLabel\"\n           [attr.aria-labelledby]=\"ariaLabelledby\"\n           [attr.aria-describedby]=\"ariaDescribedby\"\n           [attr.aria-disabled]=\"disabled && disabledInteractive ? 'true' : null\"\n           (change)=\"_onInputInteraction($event)\">\n    <div class=\"mdc-radio__background\">\n      <div class=\"mdc-radio__outer-circle\"></div>\n      <div class=\"mdc-radio__inner-circle\"></div>\n    </div>\n    <div mat-ripple class=\"mat-radio-ripple mat-focus-indicator\"\n         [matRippleTrigger]=\"_rippleTrigger.nativeElement\"\n         [matRippleDisabled]=\"_isRippleDisabled()\"\n         [matRippleCentered]=\"true\">\n      <div class=\"mat-ripple-element mat-radio-persistent-ripple\"></div>\n    </div>\n  </div>\n  <label class=\"mdc-label\" [for]=\"inputId\">\n    <ng-content></ng-content>\n  </label>\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule, MatRippleModule} from '../core';\nimport {MatRadioButton, MatRadioGroup} from './radio';\n\n@NgModule({\n  imports: [MatCommonModule, MatRippleModule, MatRadioGroup, MatRadioButton],\n  exports: [MatCommonModule, MatRadioGroup, MatRadioButton],\n})\nexport class MatRadioModule {}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AA6CA;MACa,cAAc,CAAA;AAGhB,IAAA,MAAA;AAEA,IAAA,KAAA;AAJT,IAAA,WAAA;;IAES,MAAsB;;IAEtB,KAAQ,EAAA;QAFR,IAAM,CAAA,MAAA,GAAN,MAAM;QAEN,IAAK,CAAA,KAAA,GAAL,KAAK;;AAEf;AAED;;;;AAIG;AACU,MAAA,sCAAsC,GAAQ;AACzD,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,aAAa,CAAC;AAC5C,IAAA,KAAK,EAAE,IAAI;;AAGb;;;;AAIG;MACU,eAAe,GAAG,IAAI,cAAc,CAAgB,eAAe;MAgBnE,yBAAyB,GAAG,IAAI,cAAc,CACzD,2BAA2B,EAC3B;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,iCAAiC;AAC3C,CAAA;AAGH;;;;AAIG;SACa,iCAAiC,GAAA;IAC/C,OAAO;AACL,QAAA,KAAK,EAAE,QAAQ;AACf,QAAA,mBAAmB,EAAE,KAAK;KAC3B;AACH;AAEA;;AAEG;MAaU,aAAa,CAAA;AAChB,IAAA,eAAe,GAAG,MAAM,CAAC,iBAAiB,CAAC;;IAG3C,MAAM,GAAQ,IAAI;;IAGlB,KAAK,GAAW,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,kBAAkB,CAAC;;IAG9D,SAAS,GAA0B,IAAI;;IAGvC,cAAc,GAAY,KAAK;;IAG/B,cAAc,GAAuB,OAAO;;IAG5C,SAAS,GAAY,KAAK;;IAG1B,SAAS,GAAY,KAAK;;AAG1B,IAAA,cAAc;;AAGtB,IAAA,6BAA6B,GAAyB,MAAK,GAAG;AAE9D;;;AAGG;AACH,IAAA,SAAS,GAAc,MAAK,GAAG;AAE/B;;;;AAIG;AACgB,IAAA,MAAM,GAAiC,IAAI,YAAY,EAAkB;;AAI5F,IAAA,OAAO;AAEP;;;;;;AAMG;AACM,IAAA,KAAK;;AAGd,IAAA,IACI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,KAAK;;IAEnB,IAAI,IAAI,CAAC,KAAa,EAAA;AACpB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK;QAClB,IAAI,CAAC,uBAAuB,EAAE;;;AAIhC,IAAA,IACI,aAAa,GAAA;QACf,OAAO,IAAI,CAAC,cAAc;;IAE5B,IAAI,aAAa,CAAC,CAAC,EAAA;AACjB,QAAA,IAAI,CAAC,cAAc,GAAG,CAAC,KAAK,QAAQ,GAAG,QAAQ,GAAG,OAAO;QACzD,IAAI,CAAC,mBAAmB,EAAE;;AAG5B;;;;;AAKG;AACH,IAAA,IACI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,MAAM;;IAEpB,IAAI,KAAK,CAAC,QAAa,EAAA;AACrB,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE;;AAE5B,YAAA,IAAI,CAAC,MAAM,GAAG,QAAQ;YAEtB,IAAI,CAAC,6BAA6B,EAAE;YACpC,IAAI,CAAC,yBAAyB,EAAE;;;IAIpC,yBAAyB,GAAA;QACvB,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;AAC7C,YAAA,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,IAAI;;;AAIjC;;;AAGG;AACH,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,QAA+B,EAAA;AAC1C,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ;AACzB,QAAA,IAAI,CAAC,KAAK,GAAG,QAAQ,GAAG,QAAQ,CAAC,KAAK,GAAG,IAAI;QAC7C,IAAI,CAAC,yBAAyB,EAAE;;;AAIlC,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC,mBAAmB,EAAE;;;AAI5B,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC,mBAAmB,EAAE;;;AAI5B,IAAA,IACI,mBAAmB,GAAA;QACrB,OAAO,IAAI,CAAC,oBAAoB;;IAElC,IAAI,mBAAmB,CAAC,KAAc,EAAA;AACpC,QAAA,IAAI,CAAC,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAAC,mBAAmB,EAAE;;IAEpB,oBAAoB,GAAG,KAAK;AAIpC,IAAA,WAAA,GAAA;AAEA;;;AAGG;IACH,kBAAkB,GAAA;;;;AAIhB,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI;;;;;AAM1B,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,MAAK;YACxD,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE;AACzE,gBAAA,IAAI,CAAC,SAAS,GAAG,IAAI;;AAEzB,SAAC,CAAC;;IAGJ,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,cAAc,EAAE,WAAW,EAAE;;AAGpC;;;AAGG;IACH,MAAM,GAAA;AACJ,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,SAAS,EAAE;;;IAIZ,uBAAuB,GAAA;AAC7B,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,IAAG;AAC3B,gBAAA,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;gBACtB,KAAK,CAAC,aAAa,EAAE;AACvB,aAAC,CAAC;;;;IAKE,6BAA6B,GAAA;;AAEnC,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK,IAAI,CAAC,MAAM;AAEzF,QAAA,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,iBAAiB,EAAE;AACtC,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,YAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,IAAG;gBAC3B,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK;AAC1C,gBAAA,IAAI,KAAK,CAAC,OAAO,EAAE;AACjB,oBAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;AAE1B,aAAC,CAAC;;;;IAKN,gBAAgB,GAAA;AACd,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE;AACvB,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,SAAU,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;;;IAItE,mBAAmB,GAAA;AACjB,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC;;;AAIxD;;;AAGG;AACH,IAAA,UAAU,CAAC,KAAU,EAAA;AACnB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK;AAClB,QAAA,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE;;AAGrC;;;;AAIG;AACH,IAAA,gBAAgB,CAAC,EAAwB,EAAA;AACvC,QAAA,IAAI,CAAC,6BAA6B,GAAG,EAAE;;AAGzC;;;;AAIG;AACH,IAAA,iBAAiB,CAAC,EAAO,EAAA;AACvB,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE;;AAGrB;;;AAGG;AACH,IAAA,gBAAgB,CAAC,UAAmB,EAAA;AAClC,QAAA,IAAI,CAAC,QAAQ,GAAG,UAAU;AAC1B,QAAA,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE;;uGAjQ1B,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,oMAqHL,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAUhB,gBAAgB,CAAA,EAAA,mBAAA,EAAA,CAAA,qBAAA,EAAA,qBAAA,EAUhB,gBAAgB,CAlJxB,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,YAAA,EAAA,EAAA,cAAA,EAAA,qBAAA,EAAA,EAAA,SAAA,EAAA;YACT,sCAAsC;AACtC,YAAA,EAAC,OAAO,EAAE,eAAe,EAAE,WAAW,EAAE,aAAa,EAAC;AACvD,SAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,SAAA,EAAA,SAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAkDiC,cAAc,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,eAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FA5CrC,aAAa,EAAA,UAAA,EAAA,CAAA;kBAZzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iBAAiB;AAC3B,oBAAA,QAAQ,EAAE,eAAe;AACzB,oBAAA,SAAS,EAAE;wBACT,sCAAsC;AACtC,wBAAA,EAAC,OAAO,EAAE,eAAe,EAAE,WAAW,eAAe,EAAC;AACvD,qBAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,MAAM,EAAE,YAAY;AACpB,wBAAA,OAAO,EAAE,qBAAqB;AAC/B,qBAAA;AACF,iBAAA;wDA0CoB,MAAM,EAAA,CAAA;sBAAxB;gBAID,OAAO,EAAA,CAAA;sBADN,eAAe;uBAAC,UAAU,CAAC,MAAM,cAAc,CAAC,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;gBAU7D,KAAK,EAAA,CAAA;sBAAb;gBAIG,IAAI,EAAA,CAAA;sBADP;gBAWG,aAAa,EAAA,CAAA;sBADhB;gBAgBG,KAAK,EAAA,CAAA;sBADR;gBAyBG,QAAQ,EAAA,CAAA;sBADX;gBAYG,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAWhC,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAWhC,mBAAmB,EAAA,CAAA;sBADtB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;;MAyJzB,cAAc,CAAA;AACf,IAAA,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC;AAClC,IAAA,eAAe,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAC3C,IAAA,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC;AACpC,IAAA,gBAAgB,GAAG,MAAM,CAAC,yBAAyB,CAAC;AACpD,IAAA,eAAe,GAAG,MAAM,CAAyB,yBAAyB,EAAE;AAClF,QAAA,QAAQ,EAAE,IAAI;AACf,KAAA,CAAC;AAEM,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,IAAA,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;IAC7B,SAAS,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC;AACpD,IAAA,aAAa;;AAGZ,IAAA,EAAE,GAAW,IAAI,CAAC,SAAS;;AAG3B,IAAA,IAAI;;AAGQ,IAAA,SAAS;;AAGJ,IAAA,cAAc;;AAGb,IAAA,eAAe;;IAI1C,aAAa,GAAY,KAAK;;IAM9B,QAAQ,GAAW,CAAC;;AAGpB,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ;;IAEtB,IAAI,OAAO,CAAC,KAAc,EAAA;AACxB,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,EAAE;AAC3B,YAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;AACrB,YAAA,IAAI,KAAK,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE;AACpE,gBAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI;;AAC1B,iBAAA,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE;;;AAG5E,gBAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI;;YAGjC,IAAI,KAAK,EAAE;;AAET,gBAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC;;AAElD,YAAA,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE;;;;AAKvC,IAAA,IACI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,MAAM;;IAEpB,IAAI,KAAK,CAAC,KAAU,EAAA;AAClB,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE;AACzB,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK;AACnB,YAAA,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;AAC5B,gBAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;;oBAEjB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,KAAK,KAAK;;AAEhD,gBAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,oBAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI;;;;;;AAOvC,IAAA,IACI,aAAa,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,cAAc,KAAK,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,OAAO;;IAE7F,IAAI,aAAa,CAAC,KAAK,EAAA;AACrB,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK;;AAErB,IAAA,cAAc;;AAGtB,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;;IAEjF,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;;;AAI1B,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;;IAExE,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,EAAE;AAC5B,YAAA,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE;;AAErC,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;AAGxB;;;;;;AAMG;AACH,IAAA,IACI,KAAK,GAAA;;;QAGP,QACE,IAAI,CAAC,MAAM;aACV,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;aACzC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;AACpD,YAAA,QAAQ;;IAGZ,IAAI,KAAK,CAAC,QAAsB,EAAA;AAC9B,QAAA,IAAI,CAAC,MAAM,GAAG,QAAQ;;AAEhB,IAAA,MAAM;;AAGd,IAAA,IACI,mBAAmB,GAAA;AACrB,QAAA,QACE,IAAI,CAAC,oBAAoB,KAAK,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC;;IAGlG,IAAI,mBAAmB,CAAC,KAAc,EAAA;AACpC,QAAA,IAAI,CAAC,oBAAoB,GAAG,KAAK;;AAE3B,IAAA,oBAAoB;AAE5B;;;;AAIG;AACgB,IAAA,MAAM,GAAiC,IAAI,YAAY,EAAkB;;AAG5F,IAAA,UAAU;;AAGV,IAAA,IAAI,OAAO,GAAA;QACT,OAAO,CAAA,EAAG,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,SAAS,CAAA,MAAA,CAAQ;;;IAIrC,QAAQ,GAAY,KAAK;;AAGzB,IAAA,SAAS;;AAGT,IAAA,SAAS;;IAGT,MAAM,GAAQ,IAAI;;AAGlB,IAAA,8BAA8B,GAAe,MAAK,GAAG;;AAGrD,IAAA,iBAAiB;;AAGL,IAAA,aAAa;;AAIjC,IAAA,cAAc;;AAGd,IAAA,eAAe;AAEP,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAIpC,IAAA,WAAA,GAAA;QACE,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC;AAC5D,QAAA,MAAM,UAAU,GAAG,MAAM,CAAgB,eAAe,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAE;AAC5E,QAAA,MAAM,aAAa,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AACrE,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,kBAAkB,CAAC,UAAU,CAAC,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;;;AAI7E,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU;AAC5B,QAAA,IAAI,CAAC,eAAe,GAAG,aAAa,KAAK,gBAAgB;QACzD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,eAAe,EAAE,mBAAmB,IAAI,KAAK;QAE9E,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC,QAAQ,EAAE,CAAC,CAAC;;;;IAKhD,KAAK,CAAC,OAAsB,EAAE,MAAoB,EAAA;QAChD,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,EAAE,OAAO,CAAC;;aAC3D;YACL,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC;;;AAInD;;;;AAIG;IACH,aAAa,GAAA;;;AAGX,QAAA,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE;;IAGrC,QAAQ,GAAA;AACN,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;;AAEnB,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,KAAK,IAAI,CAAC,MAAM;AAEpD,YAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,gBAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI;;;YAIjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI;;AAGlC,QAAA,IAAI,CAAC,8BAA8B,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,IAAI,KAAI;AAC9E,YAAA,IAAI,EAAE,KAAK,IAAI,CAAC,EAAE,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;AACxC,gBAAA,IAAI,CAAC,OAAO,GAAG,KAAK;;AAExB,SAAC,CAAC;;IAGJ,SAAS,GAAA;QACP,IAAI,CAAC,eAAe,EAAE;;IAGxB,eAAe,GAAA;QACb,IAAI,CAAC,eAAe,EAAE;AACtB,QAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,SAAS,CAAC,WAAW,IAAG;AACzE,YAAA,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,EAAE;AACnC,gBAAA,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;;AAE5B,SAAC,CAAC;;;;AAKF,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;YAClC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CACxC,IAAI,CAAC,aAAa,CAAC,aAAa,EAChC,OAAO,EACP,IAAI,CAAC,aAAa,CACnB;AACH,SAAC,CAAC;;IAGJ,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,aAAa,IAAI;QACtB,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC;QACnD,IAAI,CAAC,8BAA8B,EAAE;;;IAI/B,gBAAgB,GAAA;AACtB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;;IAGzD,iBAAiB,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ;;;AAI5C,IAAA,mBAAmB,CAAC,KAAY,EAAA;;;;QAI9B,KAAK,CAAC,eAAe,EAAE;QAEvB,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AACnC,YAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,UAAU,CAAC,KAAK;AACjF,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI;YACnB,IAAI,CAAC,gBAAgB,EAAE;AAEvB,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,IAAI,CAAC,UAAU,CAAC,6BAA6B,CAAC,IAAI,CAAC,KAAK,CAAC;gBACzD,IAAI,iBAAiB,EAAE;AACrB,oBAAA,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE;;;;;;AAO1C,IAAA,mBAAmB,CAAC,KAAY,EAAA;AAC9B,QAAA,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC;QAE/B,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,mBAAmB,EAAE;;;AAG9C,YAAA,IAAI,CAAC,aAAa,EAAE,aAAa,CAAC,KAAK,EAAE;;;;AAKnC,IAAA,YAAY,CAAC,KAAc,EAAA;AACnC,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;AAC5B,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,YAAA,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE;;;;AAK/B,IAAA,aAAa,GAAG,CAAC,KAAY,KAAI;;;;;;;;QAQvC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC7C,KAAK,CAAC,cAAc,EAAE;;AAE1B,KAAC;;IAGO,eAAe,GAAA;AACrB,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU;AAC7B,QAAA,IAAI,KAAa;;;;;AAMjB,QAAA,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC9C,YAAA,KAAK,GAAG,IAAI,CAAC,QAAQ;;aAChB;AACL,YAAA,KAAK,GAAG,KAAK,CAAC,QAAQ,KAAK,IAAI,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;;AAGtD,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,iBAAiB,EAAE;;;AAGpC,YAAA,MAAM,KAAK,GAAiC,IAAI,CAAC,aAAa,EAAE,aAAa;YAE7E,IAAI,KAAK,EAAE;gBACT,KAAK,CAAC,YAAY,CAAC,UAAU,EAAE,KAAK,GAAG,EAAE,CAAC;AAC1C,gBAAA,IAAI,CAAC,iBAAiB,GAAG,KAAK;;gBAE9B,eAAe,CACb,MAAK;oBACH,cAAc,CAAC,MAAK;;;;;AAKlB,wBAAA,IACE,KAAK;AACL,4BAAA,KAAK,CAAC,QAAQ;4BACd,KAAK,CAAC,QAAQ,KAAK,IAAI;AACvB,4BAAA,QAAQ,CAAC,aAAa,KAAK,KAAK,EAChC;4BACA,KAAK,CAAC,QAAQ,EAAE,aAAa,CAAC,aAAa,CAAC,KAAK,EAAE;;;AAGnD,4BAAA,IAAI,QAAQ,CAAC,aAAa,KAAK,KAAK,EAAE;AACpC,gCAAA,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,EAAE;;;AAG7C,qBAAC,CAAC;iBACH,EACD,EAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAC,CAC3B;;;;uGAzYI,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,cAAc,iSA8BN,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAKtB,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,mCAK1D,gBAAgB,CAAA,EAAA,KAAA,EAAA,OAAA,EAAA,aAAA,EAAA,eAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAsDhB,gBAAgB,CAShB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAmChB,EAAA,KAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,CAAA,qBAAA,EAAA,qBAAA,EAAA,gBAAgB,m0BAgDJ,UAAU,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECtlB3C,y8DAuCA,EDmXY,MAAA,EAAA,CAAA,goTAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,SAAS,wPAAE,qBAAqB,EAAA,QAAA,EAAA,8BAAA,EAAA,MAAA,EAAA,CAAA,eAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAE/B,cAAc,EAAA,UAAA,EAAA,CAAA;kBA7B1B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,kBAAkB,EAGtB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,sBAAsB;AAC/B,wBAAA,WAAW,EAAE,IAAI;AACjB,wBAAA,qBAAqB,EAAE,qBAAqB;AAC5C,wBAAA,oBAAoB,EAAE,oBAAoB;AAC1C,wBAAA,kBAAkB,EAAE,kBAAkB;AACtC,wBAAA,+BAA+B,EAAE,SAAS;AAC1C,wBAAA,gCAAgC,EAAE,UAAU;AAC5C,wBAAA,4CAA4C,EAAE,qBAAqB;AACnE,wBAAA,iCAAiC,EAAE,iBAAiB;;AAEpD,wBAAA,iBAAiB,EAAE,MAAM;AACzB,wBAAA,mBAAmB,EAAE,MAAM;AAC3B,wBAAA,wBAAwB,EAAE,MAAM;AAChC,wBAAA,yBAAyB,EAAE,MAAM;;;;AAIjC,wBAAA,SAAS,EAAE,qCAAqC;AACjD,qBAAA,EAAA,QAAA,EACS,gBAAgB,EAAA,aAAA,EACX,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EACtC,OAAA,EAAA,CAAC,SAAS,EAAE,qBAAqB,CAAC,EAAA,QAAA,EAAA,y8DAAA,EAAA,MAAA,EAAA,CAAA,goTAAA,CAAA,EAAA;wDAiBlC,EAAE,EAAA,CAAA;sBAAV;gBAGQ,IAAI,EAAA,CAAA;sBAAZ;gBAGoB,SAAS,EAAA,CAAA;sBAA7B,KAAK;uBAAC,YAAY;gBAGO,cAAc,EAAA,CAAA;sBAAvC,KAAK;uBAAC,iBAAiB;gBAGG,eAAe,EAAA,CAAA;sBAAzC,KAAK;uBAAC,kBAAkB;gBAIzB,aAAa,EAAA,CAAA;sBADZ,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAOpC,QAAQ,EAAA,CAAA;sBAHP,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA;wBACL,SAAS,EAAE,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;AAC5E,qBAAA;gBAKG,OAAO,EAAA,CAAA;sBADV,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAyBhC,KAAK,EAAA,CAAA;sBADR;gBAqBG,aAAa,EAAA,CAAA;sBADhB;gBAWG,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAUhC,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAmBhC,KAAK,EAAA,CAAA;sBADR;gBAkBG,mBAAmB,EAAA,CAAA;sBADtB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAgBjB,MAAM,EAAA,CAAA;sBAAxB;gBA6BmB,aAAa,EAAA,CAAA;sBAAhC,SAAS;uBAAC,OAAO;gBAIlB,cAAc,EAAA,CAAA;sBADb,SAAS;uBAAC,WAAW,EAAE,EAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,IAAI,EAAC;;;MEtkB7C,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,EAHf,OAAA,EAAA,CAAA,eAAe,EAAE,eAAe,EAAE,aAAa,EAAE,cAAc,CAC/D,EAAA,OAAA,EAAA,CAAA,eAAe,EAAE,aAAa,EAAE,cAAc,CAAA,EAAA,CAAA;AAE7C,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,YAHf,eAAe,EAAE,eAAe,EAAiB,cAAc,EAC/D,eAAe,CAAA,EAAA,CAAA;;2FAEd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAJ1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,aAAa,EAAE,cAAc,CAAC;AAC1E,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,aAAa,EAAE,cAAc,CAAC;AAC1D,iBAAA;;;;;"}