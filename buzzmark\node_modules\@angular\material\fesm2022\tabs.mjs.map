{"version": 3, "file": "tabs.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tabs/tab-content.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tabs/tab-label.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tabs/tab.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tabs/tab.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tabs/ink-bar.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tabs/tab-label-wrapper.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tabs/paginated-tab-header.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tabs/tab-header.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tabs/tab-header.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tabs/tab-config.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tabs/tab-body.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tabs/tab-body.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tabs/tab-group.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tabs/tab-group.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tabs/tab-nav-bar/tab-nav-bar.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tabs/tab-nav-bar/tab-nav-bar.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tabs/tab-nav-bar/tab-link.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tabs/module.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tabs/tabs-animations.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, InjectionToken, TemplateRef, inject} from '@angular/core';\n\n/**\n * Injection token that can be used to reference instances of `MatTabContent`. It serves as\n * alternative token to the actual `MatTabContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const MAT_TAB_CONTENT = new InjectionToken<MatTabContent>('MatTabContent');\n\n/** Decorates the `ng-template` tags and reads out the template from it. */\n@Directive({\n  selector: '[matTabContent]',\n  providers: [{provide: MAT_TAB_CONTENT, useExisting: MatTabContent}],\n})\nexport class MatTabContent {\n  template = inject<TemplateRef<any>>(TemplateRef);\n\n  constructor(...args: unknown[]);\n  constructor() {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, InjectionToken, inject} from '@angular/core';\nimport {CdkPortal} from '@angular/cdk/portal';\n\n/**\n * Injection token that can be used to reference instances of `MatTabLabel`. It serves as\n * alternative token to the actual `MatTabLabel` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const MAT_TAB_LABEL = new InjectionToken<MatTabLabel>('MatTabLabel');\n\n/**\n * Used to provide a tab label to a tab without causing a circular dependency.\n * @docs-private\n */\nexport const MAT_TAB = new InjectionToken<any>('MAT_TAB');\n\n/** Used to flag tab labels for use with the portal directive */\n@Directive({\n  selector: '[mat-tab-label], [matTabLabel]',\n  providers: [{provide: MAT_TAB_LABEL, useExisting: MatTabLabel}],\n})\nexport class MatTabLabel extends CdkPortal {\n  _closestTab = inject(MAT_TAB, {optional: true});\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  ContentChild,\n  InjectionToken,\n  Input,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  SimpleChanges,\n  TemplateRef,\n  ViewChild,\n  ViewContainerRef,\n  ViewEncapsulation,\n  booleanAttribute,\n  inject,\n} from '@angular/core';\nimport {MatTabContent} from './tab-content';\nimport {MAT_TAB, MatTabLabel} from './tab-label';\nimport {TemplatePortal} from '@angular/cdk/portal';\nimport {Subject} from 'rxjs';\nimport {_CdkPrivateStyleLoader} from '@angular/cdk/private';\nimport {_StructuralStylesLoader} from '../core';\n\n/**\n * Used to provide a tab group to a tab without causing a circular dependency.\n * @docs-private\n */\nexport const MAT_TAB_GROUP = new InjectionToken<any>('MAT_TAB_GROUP');\n\n@Component({\n  selector: 'mat-tab',\n  // Note that usually we'd go through a bit more trouble and set up another class so that\n  // the inlined template of `MatTab` isn't duplicated, however the template is small enough\n  // that creating the extra class will generate more code than just duplicating the template.\n  templateUrl: 'tab.html',\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  encapsulation: ViewEncapsulation.None,\n  exportAs: 'matTab',\n  providers: [{provide: MAT_TAB, useExisting: MatTab}],\n  host: {\n    // This element will be rendered on the server in order to support hydration.\n    // Hide it so it doesn't cause a layout shift when it's removed on the client.\n    'hidden': '',\n\n    // Clear any custom IDs from the tab since they'll be forwarded to the actual tab.\n    '[attr.id]': 'null',\n  },\n})\nexport class MatTab implements OnInit, OnChanges, OnDestroy {\n  private _viewContainerRef = inject(ViewContainerRef);\n  _closestTabGroup = inject(MAT_TAB_GROUP, {optional: true});\n\n  /** whether the tab is disabled. */\n  @Input({transform: booleanAttribute})\n  disabled: boolean = false;\n\n  /** Content for the tab label given by `<ng-template mat-tab-label>`. */\n  @ContentChild(MatTabLabel)\n  get templateLabel(): MatTabLabel {\n    return this._templateLabel;\n  }\n  set templateLabel(value: MatTabLabel) {\n    this._setTemplateLabelInput(value);\n  }\n  private _templateLabel: MatTabLabel;\n\n  /**\n   * Template provided in the tab content that will be used if present, used to enable lazy-loading\n   */\n  @ContentChild(MatTabContent, {read: TemplateRef, static: true})\n  // We need an initializer here to avoid a TS error. The value will be set in `ngAfterViewInit`.\n  private _explicitContent: TemplateRef<any> = undefined!;\n\n  /** Template inside the MatTab view that contains an `<ng-content>`. */\n  @ViewChild(TemplateRef, {static: true}) _implicitContent: TemplateRef<any>;\n\n  /** Plain text label for the tab, used when there is no template label. */\n  @Input('label') textLabel: string = '';\n\n  /** Aria label for the tab. */\n  @Input('aria-label') ariaLabel: string;\n\n  /**\n   * Reference to the element that the tab is labelled by.\n   * Will be cleared if `aria-label` is set at the same time.\n   */\n  @Input('aria-labelledby') ariaLabelledby: string;\n\n  /** Classes to be passed to the tab label inside the mat-tab-header container. */\n  @Input() labelClass: string | string[];\n\n  /** Classes to be passed to the tab mat-tab-body container. */\n  @Input() bodyClass: string | string[];\n\n  /**\n   * Custom ID for the tab, overriding the auto-generated one by Material.\n   * Note that when using this input, it's your responsibility to ensure that the ID is unique.\n   */\n  @Input() id: string | null = null;\n\n  /** Portal that will be the hosted content of the tab */\n  private _contentPortal: TemplatePortal | null = null;\n\n  /** @docs-private */\n  get content(): TemplatePortal | null {\n    return this._contentPortal;\n  }\n\n  /** Emits whenever the internal state of the tab changes. */\n  readonly _stateChanges = new Subject<void>();\n\n  /**\n   * The relatively indexed position where 0 represents the center, negative is left, and positive\n   * represents the right.\n   */\n  position: number | null = null;\n\n  // TODO(crisbeto): we no longer use this, but some internal apps appear to rely on it.\n  /**\n   * The initial relatively index origin of the tab if it was created and selected after there\n   * was already a selected tab. Provides context of what position the tab should originate from.\n   */\n  origin: number | null = null;\n\n  /**\n   * Whether the tab is currently active.\n   */\n  isActive = false;\n\n  constructor(...args: unknown[]);\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    if (changes.hasOwnProperty('textLabel') || changes.hasOwnProperty('disabled')) {\n      this._stateChanges.next();\n    }\n  }\n\n  ngOnDestroy(): void {\n    this._stateChanges.complete();\n  }\n\n  ngOnInit(): void {\n    this._contentPortal = new TemplatePortal(\n      this._explicitContent || this._implicitContent,\n      this._viewContainerRef,\n    );\n  }\n\n  /**\n   * This has been extracted to a util because of TS 4 and VE.\n   * View Engine doesn't support property rename inheritance.\n   * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n   * @docs-private\n   */\n  private _setTemplateLabelInput(value: MatTabLabel | undefined) {\n    // Only update the label if the query managed to find one. This works around an issue where a\n    // user may have manually set `templateLabel` during creation mode, which would then get\n    // clobbered by `undefined` when the query resolves. Also note that we check that the closest\n    // tab matches the current one so that we don't pick up labels from nested tabs.\n    if (value && value._closestTab === this) {\n      this._templateLabel = value;\n    }\n  }\n}\n", "<!-- Create a template for the content of the <mat-tab> so that we can grab a reference to this\n    TemplateRef and use it in a Portal to render the tab content in the appropriate place in the\n    tab-group. -->\n<ng-template><ng-content></ng-content></ng-template>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Directive,\n  ElementRef,\n  InjectionToken,\n  Input,\n  OnDestroy,\n  OnInit,\n  QueryList,\n  booleanAttribute,\n  inject,\n} from '@angular/core';\n\n/**\n * Item inside a tab header relative to which the ink bar can be aligned.\n * @docs-private\n */\nexport interface MatInkBarItem extends OnInit, OnDestroy {\n  elementRef: ElementRef<HTMLElement>;\n  activateInkBar(previousIndicatorClientRect?: DOMRect): void;\n  deactivateInkBar(): void;\n  fitInkBarToContent: boolean;\n}\n\n/** Class that is applied when a tab indicator is active. */\nconst ACTIVE_CLASS = 'mdc-tab-indicator--active';\n\n/** Class that is applied when the tab indicator should not transition. */\nconst NO_TRANSITION_CLASS = 'mdc-tab-indicator--no-transition';\n\n/**\n * Abstraction around the MDC tab indicator that acts as the tab header's ink bar.\n * @docs-private\n */\nexport class MatInkBar {\n  /** Item to which the ink bar is aligned currently. */\n  private _currentItem: MatInkBarItem | undefined;\n\n  constructor(private _items: QueryList<MatInkBarItem>) {}\n\n  /** Hides the ink bar. */\n  hide() {\n    this._items.forEach(item => item.deactivateInkBar());\n    this._currentItem = undefined;\n  }\n\n  /** Aligns the ink bar to a DOM node. */\n  alignToElement(element: HTMLElement) {\n    const correspondingItem = this._items.find(item => item.elementRef.nativeElement === element);\n    const currentItem = this._currentItem;\n\n    if (correspondingItem === currentItem) {\n      return;\n    }\n\n    currentItem?.deactivateInkBar();\n\n    if (correspondingItem) {\n      const domRect = currentItem?.elementRef.nativeElement.getBoundingClientRect?.();\n\n      // The ink bar won't animate unless we give it the `DOMRect` of the previous item.\n      correspondingItem.activateInkBar(domRect);\n      this._currentItem = correspondingItem;\n    }\n  }\n}\n\n@Directive()\nexport abstract class InkBarItem implements OnInit, OnDestroy {\n  private _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _inkBarElement: HTMLElement | null;\n  private _inkBarContentElement: HTMLElement | null;\n  private _fitToContent = false;\n\n  /** Whether the ink bar should fit to the entire tab or just its content. */\n  @Input({transform: booleanAttribute})\n  get fitInkBarToContent(): boolean {\n    return this._fitToContent;\n  }\n  set fitInkBarToContent(newValue: boolean) {\n    if (this._fitToContent !== newValue) {\n      this._fitToContent = newValue;\n\n      if (this._inkBarElement) {\n        this._appendInkBarElement();\n      }\n    }\n  }\n\n  /** Aligns the ink bar to the current item. */\n  activateInkBar(previousIndicatorClientRect?: DOMRect) {\n    const element = this._elementRef.nativeElement;\n\n    // Early exit if no indicator is present to handle cases where an indicator\n    // may be activated without a prior indicator state\n    if (\n      !previousIndicatorClientRect ||\n      !element.getBoundingClientRect ||\n      !this._inkBarContentElement\n    ) {\n      element.classList.add(ACTIVE_CLASS);\n      return;\n    }\n\n    // This animation uses the FLIP approach. You can read more about it at the link below:\n    // https://aerotwist.com/blog/flip-your-animations/\n\n    // Calculate the dimensions based on the dimensions of the previous indicator\n    const currentClientRect = element.getBoundingClientRect();\n    const widthDelta = previousIndicatorClientRect.width / currentClientRect.width;\n    const xPosition = previousIndicatorClientRect.left - currentClientRect.left;\n    element.classList.add(NO_TRANSITION_CLASS);\n    this._inkBarContentElement.style.setProperty(\n      'transform',\n      `translateX(${xPosition}px) scaleX(${widthDelta})`,\n    );\n\n    // Force repaint before updating classes and transform to ensure the transform properly takes effect\n    element.getBoundingClientRect();\n\n    element.classList.remove(NO_TRANSITION_CLASS);\n    element.classList.add(ACTIVE_CLASS);\n    this._inkBarContentElement.style.setProperty('transform', '');\n  }\n\n  /** Removes the ink bar from the current item. */\n  deactivateInkBar() {\n    this._elementRef.nativeElement.classList.remove(ACTIVE_CLASS);\n  }\n\n  /** Initializes the foundation. */\n  ngOnInit() {\n    this._createInkBarElement();\n  }\n\n  /** Destroys the foundation. */\n  ngOnDestroy() {\n    this._inkBarElement?.remove();\n    this._inkBarElement = this._inkBarContentElement = null!;\n  }\n\n  /** Creates and appends the ink bar element. */\n  private _createInkBarElement() {\n    const documentNode = this._elementRef.nativeElement.ownerDocument || document;\n    const inkBarElement = (this._inkBarElement = documentNode.createElement('span'));\n    const inkBarContentElement = (this._inkBarContentElement = documentNode.createElement('span'));\n\n    inkBarElement.className = 'mdc-tab-indicator';\n    inkBarContentElement.className =\n      'mdc-tab-indicator__content mdc-tab-indicator__content--underline';\n\n    inkBarElement.appendChild(this._inkBarContentElement);\n    this._appendInkBarElement();\n  }\n\n  /**\n   * Appends the ink bar to the tab host element or content, depending on whether\n   * the ink bar should fit to content.\n   */\n  private _appendInkBarElement() {\n    if (!this._inkBarElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Ink bar element has not been created and cannot be appended');\n    }\n\n    const parentElement = this._fitToContent\n      ? this._elementRef.nativeElement.querySelector('.mdc-tab__content')\n      : this._elementRef.nativeElement;\n\n    if (!parentElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Missing element to host the ink bar');\n    }\n\n    parentElement!.appendChild(this._inkBarElement!);\n  }\n}\n\n/**\n * Interface for a MatInkBar positioner method, defining the positioning and width of the ink\n * bar in a set of tabs.\n */\nexport interface _MatInkBarPositioner {\n  (element: HTMLElement): {left: string; width: string};\n}\n\n/**\n * The default positioner function for the MatInkBar.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function _MAT_INK_BAR_POSITIONER_FACTORY(): _MatInkBarPositioner {\n  const method = (element: HTMLElement) => ({\n    left: element ? (element.offsetLeft || 0) + 'px' : '0',\n    width: element ? (element.offsetWidth || 0) + 'px' : '0',\n  });\n\n  return method;\n}\n\n/** Injection token for the MatInkBar's Positioner. */\nexport const _MAT_INK_BAR_POSITIONER = new InjectionToken<_MatInkBarPositioner>(\n  'MatInkBarPositioner',\n  {\n    providedIn: 'root',\n    factory: _MAT_INK_BAR_POSITIONER_FACTORY,\n  },\n);\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, ElementRef, Input, booleanAttribute, inject} from '@angular/core';\nimport {InkBarItem} from './ink-bar';\n\n/**\n * Used in the `mat-tab-group` view to display tab labels.\n * @docs-private\n */\n@Directive({\n  selector: '[matTabLabelWrapper]',\n  host: {\n    '[class.mat-mdc-tab-disabled]': 'disabled',\n    '[attr.aria-disabled]': '!!disabled',\n  },\n})\nexport class MatTabLabelWrapper extends InkBarItem {\n  elementRef = inject(ElementRef);\n\n  /** Whether the tab is disabled. */\n  @Input({transform: booleanAttribute})\n  disabled: boolean = false;\n\n  /** Sets focus on the wrapper element */\n  focus(): void {\n    this.elementRef.nativeElement.focus();\n  }\n\n  getOffsetLeft(): number {\n    return this.elementRef.nativeElement.offsetLeft;\n  }\n\n  getOffsetWidth(): number {\n    return this.elementRef.nativeElement.offsetWidth;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {FocusKeyManager, FocusableOption} from '@angular/cdk/a11y';\nimport {Direction, Directionality} from '@angular/cdk/bidi';\nimport {ENTER, SPACE, hasModifierKey} from '@angular/cdk/keycodes';\nimport {SharedResizeObserver} from '@angular/cdk/observers/private';\nimport {Platform, _bindEventWithOptions} from '@angular/cdk/platform';\nimport {ViewportRuler} from '@angular/cdk/scrolling';\nimport {\n  ANIMATION_MODULE_TYPE,\n  AfterContentChecked,\n  AfterContentInit,\n  AfterViewInit,\n  ChangeDetectorRef,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  Injector,\n  Input,\n  NgZone,\n  OnDestroy,\n  Output,\n  QueryList,\n  Renderer2,\n  afterNextRender,\n  booleanAttribute,\n  inject,\n  numberAttribute,\n} from '@angular/core';\nimport {EMPTY, Observable, Observer, Subject, merge, of as observableOf, timer} from 'rxjs';\nimport {debounceTime, filter, skip, startWith, switchMap, takeUntil} from 'rxjs/operators';\n\n/** Config used to bind passive event listeners */\nconst passiveEventListenerOptions = {\n  passive: true,\n};\n\n/**\n * The directions that scrolling can go in when the header's tabs exceed the header width. 'After'\n * will scroll the header towards the end of the tabs list and 'before' will scroll towards the\n * beginning of the list.\n */\nexport type ScrollDirection = 'after' | 'before';\n\n/**\n * Amount of milliseconds to wait before starting to scroll the header automatically.\n * Set a little conservatively in order to handle fake events dispatched on touch devices.\n */\nconst HEADER_SCROLL_DELAY = 650;\n\n/**\n * Interval in milliseconds at which to scroll the header\n * while the user is holding their pointer.\n */\nconst HEADER_SCROLL_INTERVAL = 100;\n\n/** Item inside a paginated tab header. */\nexport type MatPaginatedTabHeaderItem = FocusableOption & {elementRef: ElementRef};\n\n/**\n * Base class for a tab header that supported pagination.\n * @docs-private\n */\n@Directive()\nexport abstract class MatPaginatedTabHeader\n  implements AfterContentChecked, AfterContentInit, AfterViewInit, OnDestroy\n{\n  protected _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  protected _changeDetectorRef = inject(ChangeDetectorRef);\n  private _viewportRuler = inject(ViewportRuler);\n  private _dir = inject(Directionality, {optional: true});\n  private _ngZone = inject(NgZone);\n  private _platform = inject(Platform);\n  private _sharedResizeObserver = inject(SharedResizeObserver);\n  private _injector = inject(Injector);\n  private _renderer = inject(Renderer2);\n  _animationMode = inject(ANIMATION_MODULE_TYPE, {optional: true});\n  private _eventCleanups: (() => void)[];\n\n  abstract _items: QueryList<MatPaginatedTabHeaderItem>;\n  abstract _inkBar: {hide: () => void; alignToElement: (element: HTMLElement) => void};\n  abstract _tabListContainer: ElementRef<HTMLElement>;\n  abstract _tabList: ElementRef<HTMLElement>;\n  abstract _tabListInner: ElementRef<HTMLElement>;\n  abstract _nextPaginator: ElementRef<HTMLElement>;\n  abstract _previousPaginator: ElementRef<HTMLElement>;\n\n  /** The distance in pixels that the tab labels should be translated to the left. */\n  private _scrollDistance = 0;\n\n  /** Whether the header should scroll to the selected index after the view has been checked. */\n  private _selectedIndexChanged = false;\n\n  /** Emits when the component is destroyed. */\n  protected readonly _destroyed = new Subject<void>();\n\n  /** Whether the controls for pagination should be displayed */\n  _showPaginationControls = false;\n\n  /** Whether the tab list can be scrolled more towards the end of the tab label list. */\n  _disableScrollAfter = true;\n\n  /** Whether the tab list can be scrolled more towards the beginning of the tab label list. */\n  _disableScrollBefore = true;\n\n  /**\n   * The number of tab labels that are displayed on the header. When this changes, the header\n   * should re-evaluate the scroll position.\n   */\n  private _tabLabelCount: number;\n\n  /** Whether the scroll distance has changed and should be applied after the view is checked. */\n  private _scrollDistanceChanged: boolean;\n\n  /** Used to manage focus between the tabs. */\n  protected _keyManager: FocusKeyManager<MatPaginatedTabHeaderItem> | undefined;\n\n  /** Cached text content of the header. */\n  private _currentTextContent: string;\n\n  /** Stream that will stop the automated scrolling. */\n  private _stopScrolling = new Subject<void>();\n\n  /**\n   * Whether pagination should be disabled. This can be used to avoid unnecessary\n   * layout recalculations if it's known that pagination won't be required.\n   */\n  @Input({transform: booleanAttribute})\n  disablePagination: boolean = false;\n\n  /** The index of the active tab. */\n  @Input({transform: numberAttribute})\n  get selectedIndex(): number {\n    return this._selectedIndex;\n  }\n  set selectedIndex(v: number) {\n    const value = isNaN(v) ? 0 : v;\n\n    if (this._selectedIndex != value) {\n      this._selectedIndexChanged = true;\n      this._selectedIndex = value;\n\n      if (this._keyManager) {\n        this._keyManager.updateActiveItem(value);\n      }\n    }\n  }\n  private _selectedIndex: number = 0;\n\n  /** Event emitted when the option is selected. */\n  @Output() readonly selectFocusedIndex: EventEmitter<number> = new EventEmitter<number>();\n\n  /** Event emitted when a label is focused. */\n  @Output() readonly indexFocused: EventEmitter<number> = new EventEmitter<number>();\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    // Bind the `mouseleave` event on the outside since it doesn't change anything in the view.\n    this._eventCleanups = this._ngZone.runOutsideAngular(() => [\n      this._renderer.listen(this._elementRef.nativeElement, 'mouseleave', () =>\n        this._stopInterval(),\n      ),\n    ]);\n  }\n\n  /** Called when the user has selected an item via the keyboard. */\n  protected abstract _itemSelected(event: KeyboardEvent): void;\n\n  ngAfterViewInit() {\n    // We need to handle these events manually, because we want to bind passive event listeners.\n\n    this._eventCleanups.push(\n      _bindEventWithOptions(\n        this._renderer,\n        this._previousPaginator.nativeElement,\n        'touchstart',\n        () => this._handlePaginatorPress('before'),\n        passiveEventListenerOptions,\n      ),\n      _bindEventWithOptions(\n        this._renderer,\n        this._nextPaginator.nativeElement,\n        'touchstart',\n        () => this._handlePaginatorPress('after'),\n        passiveEventListenerOptions,\n      ),\n    );\n  }\n\n  ngAfterContentInit() {\n    const dirChange = this._dir ? this._dir.change : observableOf('ltr');\n    // We need to debounce resize events because the alignment logic is expensive.\n    // If someone animates the width of tabs, we don't want to realign on every animation frame.\n    // Once we haven't seen any more resize events in the last 32ms (~2 animaion frames) we can\n    // re-align.\n    const resize = this._sharedResizeObserver\n      .observe(this._elementRef.nativeElement)\n      .pipe(debounceTime(32), takeUntil(this._destroyed));\n    // Note: We do not actually need to watch these events for proper functioning of the tabs,\n    // the resize events above should capture any viewport resize that we care about. However,\n    // removing this is fairly breaking for screenshot tests, so we're leaving it here for now.\n    const viewportResize = this._viewportRuler.change(150).pipe(takeUntil(this._destroyed));\n\n    const realign = () => {\n      this.updatePagination();\n      this._alignInkBarToSelectedTab();\n    };\n\n    this._keyManager = new FocusKeyManager<MatPaginatedTabHeaderItem>(this._items)\n      .withHorizontalOrientation(this._getLayoutDirection())\n      .withHomeAndEnd()\n      .withWrap()\n      // Allow focus to land on disabled tabs, as per https://w3c.github.io/aria-practices/#kbd_disabled_controls\n      .skipPredicate(() => false);\n\n    // Fall back to the first link as being active if there isn't a selected one.\n    // This is relevant primarily for the tab nav bar.\n    this._keyManager.updateActiveItem(Math.max(this._selectedIndex, 0));\n\n    // Note: We do not need to realign after the first render for proper functioning of the tabs\n    // the resize events above should fire when we first start observing the element. However,\n    // removing this is fairly breaking for screenshot tests, so we're leaving it here for now.\n    afterNextRender(realign, {injector: this._injector});\n\n    // On dir change or resize, realign the ink bar and update the orientation of\n    // the key manager if the direction has changed.\n    merge(dirChange, viewportResize, resize, this._items.changes, this._itemsResized())\n      .pipe(takeUntil(this._destroyed))\n      .subscribe(() => {\n        // We need to defer this to give the browser some time to recalculate\n        // the element dimensions. The call has to be wrapped in `NgZone.run`,\n        // because the viewport change handler runs outside of Angular.\n        this._ngZone.run(() => {\n          Promise.resolve().then(() => {\n            // Clamp the scroll distance, because it can change with the number of tabs.\n            this._scrollDistance = Math.max(\n              0,\n              Math.min(this._getMaxScrollDistance(), this._scrollDistance),\n            );\n            realign();\n          });\n        });\n        this._keyManager?.withHorizontalOrientation(this._getLayoutDirection());\n      });\n\n    // If there is a change in the focus key manager we need to emit the `indexFocused`\n    // event in order to provide a public event that notifies about focus changes. Also we realign\n    // the tabs container by scrolling the new focused tab into the visible section.\n    this._keyManager.change.subscribe(newFocusIndex => {\n      this.indexFocused.emit(newFocusIndex);\n      this._setTabFocus(newFocusIndex);\n    });\n  }\n\n  /** Sends any changes that could affect the layout of the items. */\n  private _itemsResized(): Observable<ResizeObserverEntry[]> {\n    if (typeof ResizeObserver !== 'function') {\n      return EMPTY;\n    }\n\n    return this._items.changes.pipe(\n      startWith(this._items),\n      switchMap(\n        (tabItems: QueryList<MatPaginatedTabHeaderItem>) =>\n          new Observable((observer: Observer<ResizeObserverEntry[]>) =>\n            this._ngZone.runOutsideAngular(() => {\n              const resizeObserver = new ResizeObserver(entries => observer.next(entries));\n              tabItems.forEach(item => resizeObserver.observe(item.elementRef.nativeElement));\n              return () => {\n                resizeObserver.disconnect();\n              };\n            }),\n          ),\n      ),\n      // Skip the first emit since the resize observer emits when an item\n      // is observed for new items when the tab is already inserted\n      skip(1),\n      // Skip emissions where all the elements are invisible since we don't want\n      // the header to try and re-render with invalid measurements. See #25574.\n      filter(entries => entries.some(e => e.contentRect.width > 0 && e.contentRect.height > 0)),\n    );\n  }\n\n  ngAfterContentChecked(): void {\n    // If the number of tab labels have changed, check if scrolling should be enabled\n    if (this._tabLabelCount != this._items.length) {\n      this.updatePagination();\n      this._tabLabelCount = this._items.length;\n      this._changeDetectorRef.markForCheck();\n    }\n\n    // If the selected index has changed, scroll to the label and check if the scrolling controls\n    // should be disabled.\n    if (this._selectedIndexChanged) {\n      this._scrollToLabel(this._selectedIndex);\n      this._checkScrollingControls();\n      this._alignInkBarToSelectedTab();\n      this._selectedIndexChanged = false;\n      this._changeDetectorRef.markForCheck();\n    }\n\n    // If the scroll distance has been changed (tab selected, focused, scroll controls activated),\n    // then translate the header to reflect this.\n    if (this._scrollDistanceChanged) {\n      this._updateTabScrollPosition();\n      this._scrollDistanceChanged = false;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n\n  ngOnDestroy() {\n    this._eventCleanups.forEach(cleanup => cleanup());\n    this._keyManager?.destroy();\n    this._destroyed.next();\n    this._destroyed.complete();\n    this._stopScrolling.complete();\n  }\n\n  /** Handles keyboard events on the header. */\n  _handleKeydown(event: KeyboardEvent) {\n    // We don't handle any key bindings with a modifier key.\n    if (hasModifierKey(event)) {\n      return;\n    }\n\n    switch (event.keyCode) {\n      case ENTER:\n      case SPACE:\n        if (this.focusIndex !== this.selectedIndex) {\n          const item = this._items.get(this.focusIndex);\n\n          if (item && !item.disabled) {\n            this.selectFocusedIndex.emit(this.focusIndex);\n            this._itemSelected(event);\n          }\n        }\n        break;\n      default:\n        this._keyManager?.onKeydown(event);\n    }\n  }\n\n  /**\n   * Callback for when the MutationObserver detects that the content has changed.\n   */\n  _onContentChanges() {\n    const textContent = this._elementRef.nativeElement.textContent;\n\n    // We need to diff the text content of the header, because the MutationObserver callback\n    // will fire even if the text content didn't change which is inefficient and is prone\n    // to infinite loops if a poorly constructed expression is passed in (see #14249).\n    if (textContent !== this._currentTextContent) {\n      this._currentTextContent = textContent || '';\n\n      // The content observer runs outside the `NgZone` by default, which\n      // means that we need to bring the callback back in ourselves.\n      this._ngZone.run(() => {\n        this.updatePagination();\n        this._alignInkBarToSelectedTab();\n        this._changeDetectorRef.markForCheck();\n      });\n    }\n  }\n\n  /**\n   * Updates the view whether pagination should be enabled or not.\n   *\n   * WARNING: Calling this method can be very costly in terms of performance. It should be called\n   * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n   * page.\n   */\n  updatePagination() {\n    this._checkPaginationEnabled();\n    this._checkScrollingControls();\n    this._updateTabScrollPosition();\n  }\n\n  /** Tracks which element has focus; used for keyboard navigation */\n  get focusIndex(): number {\n    return this._keyManager ? this._keyManager.activeItemIndex! : 0;\n  }\n\n  /** When the focus index is set, we must manually send focus to the correct label */\n  set focusIndex(value: number) {\n    if (!this._isValidIndex(value) || this.focusIndex === value || !this._keyManager) {\n      return;\n    }\n\n    this._keyManager.setActiveItem(value);\n  }\n\n  /**\n   * Determines if an index is valid.  If the tabs are not ready yet, we assume that the user is\n   * providing a valid index and return true.\n   */\n  _isValidIndex(index: number): boolean {\n    return this._items ? !!this._items.toArray()[index] : true;\n  }\n\n  /**\n   * Sets focus on the HTML element for the label wrapper and scrolls it into the view if\n   * scrolling is enabled.\n   */\n  _setTabFocus(tabIndex: number) {\n    if (this._showPaginationControls) {\n      this._scrollToLabel(tabIndex);\n    }\n\n    if (this._items && this._items.length) {\n      this._items.toArray()[tabIndex].focus();\n\n      // Do not let the browser manage scrolling to focus the element, this will be handled\n      // by using translation. In LTR, the scroll left should be 0. In RTL, the scroll width\n      // should be the full width minus the offset width.\n      const containerEl = this._tabListContainer.nativeElement;\n      const dir = this._getLayoutDirection();\n\n      if (dir == 'ltr') {\n        containerEl.scrollLeft = 0;\n      } else {\n        containerEl.scrollLeft = containerEl.scrollWidth - containerEl.offsetWidth;\n      }\n    }\n  }\n\n  /** The layout direction of the containing app. */\n  _getLayoutDirection(): Direction {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n\n  /** Performs the CSS transformation on the tab list that will cause the list to scroll. */\n  _updateTabScrollPosition() {\n    if (this.disablePagination) {\n      return;\n    }\n\n    const scrollDistance = this.scrollDistance;\n    const translateX = this._getLayoutDirection() === 'ltr' ? -scrollDistance : scrollDistance;\n\n    // Don't use `translate3d` here because we don't want to create a new layer. A new layer\n    // seems to cause flickering and overflow in Internet Explorer. For example, the ink bar\n    // and ripples will exceed the boundaries of the visible tab bar.\n    // See: https://github.com/angular/components/issues/10276\n    // We round the `transform` here, because transforms with sub-pixel precision cause some\n    // browsers to blur the content of the element.\n    this._tabList.nativeElement.style.transform = `translateX(${Math.round(translateX)}px)`;\n\n    // Setting the `transform` on IE will change the scroll offset of the parent, causing the\n    // position to be thrown off in some cases. We have to reset it ourselves to ensure that\n    // it doesn't get thrown off. Note that we scope it only to IE and Edge, because messing\n    // with the scroll position throws off Chrome 71+ in RTL mode (see #14689).\n    if (this._platform.TRIDENT || this._platform.EDGE) {\n      this._tabListContainer.nativeElement.scrollLeft = 0;\n    }\n  }\n\n  /** Sets the distance in pixels that the tab header should be transformed in the X-axis. */\n  get scrollDistance(): number {\n    return this._scrollDistance;\n  }\n  set scrollDistance(value: number) {\n    this._scrollTo(value);\n  }\n\n  /**\n   * Moves the tab list in the 'before' or 'after' direction (towards the beginning of the list or\n   * the end of the list, respectively). The distance to scroll is computed to be a third of the\n   * length of the tab list view window.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _scrollHeader(direction: ScrollDirection) {\n    const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n\n    // Move the scroll distance one-third the length of the tab list's viewport.\n    const scrollAmount = ((direction == 'before' ? -1 : 1) * viewLength) / 3;\n\n    return this._scrollTo(this._scrollDistance + scrollAmount);\n  }\n\n  /** Handles click events on the pagination arrows. */\n  _handlePaginatorClick(direction: ScrollDirection) {\n    this._stopInterval();\n    this._scrollHeader(direction);\n  }\n\n  /**\n   * Moves the tab list such that the desired tab label (marked by index) is moved into view.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _scrollToLabel(labelIndex: number) {\n    if (this.disablePagination) {\n      return;\n    }\n\n    const selectedLabel = this._items ? this._items.toArray()[labelIndex] : null;\n\n    if (!selectedLabel) {\n      return;\n    }\n\n    // The view length is the visible width of the tab labels.\n    const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n    const {offsetLeft, offsetWidth} = selectedLabel.elementRef.nativeElement;\n\n    let labelBeforePos: number, labelAfterPos: number;\n    if (this._getLayoutDirection() == 'ltr') {\n      labelBeforePos = offsetLeft;\n      labelAfterPos = labelBeforePos + offsetWidth;\n    } else {\n      labelAfterPos = this._tabListInner.nativeElement.offsetWidth - offsetLeft;\n      labelBeforePos = labelAfterPos - offsetWidth;\n    }\n\n    const beforeVisiblePos = this.scrollDistance;\n    const afterVisiblePos = this.scrollDistance + viewLength;\n\n    if (labelBeforePos < beforeVisiblePos) {\n      // Scroll header to move label to the before direction\n      this.scrollDistance -= beforeVisiblePos - labelBeforePos;\n    } else if (labelAfterPos > afterVisiblePos) {\n      // Scroll header to move label to the after direction\n      this.scrollDistance += Math.min(\n        labelAfterPos - afterVisiblePos,\n        labelBeforePos - beforeVisiblePos,\n      );\n    }\n  }\n\n  /**\n   * Evaluate whether the pagination controls should be displayed. If the scroll width of the\n   * tab list is wider than the size of the header container, then the pagination controls should\n   * be shown.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _checkPaginationEnabled() {\n    if (this.disablePagination) {\n      this._showPaginationControls = false;\n    } else {\n      const scrollWidth = this._tabListInner.nativeElement.scrollWidth;\n      const containerWidth = this._elementRef.nativeElement.offsetWidth;\n\n      // Usually checking that the scroll width is greater than the container width should be\n      // enough, but on Safari at specific widths the browser ends up rounding up when there's\n      // no pagination and rounding down once the pagination is added. This can throw the component\n      // into an infinite loop where the pagination shows up and disappears constantly. We work\n      // around it by adding a threshold to the calculation. From manual testing the threshold\n      // can be lowered to 2px and still resolve the issue, but we set a higher one to be safe.\n      // This shouldn't cause any content to be clipped, because tabs have a 24px horizontal\n      // padding. See b/316395154 for more information.\n      const isEnabled = scrollWidth - containerWidth >= 5;\n\n      if (!isEnabled) {\n        this.scrollDistance = 0;\n      }\n\n      if (isEnabled !== this._showPaginationControls) {\n        this._showPaginationControls = isEnabled;\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n  }\n\n  /**\n   * Evaluate whether the before and after controls should be enabled or disabled.\n   * If the header is at the beginning of the list (scroll distance is equal to 0) then disable the\n   * before button. If the header is at the end of the list (scroll distance is equal to the\n   * maximum distance we can scroll), then disable the after button.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _checkScrollingControls() {\n    if (this.disablePagination) {\n      this._disableScrollAfter = this._disableScrollBefore = true;\n    } else {\n      // Check if the pagination arrows should be activated.\n      this._disableScrollBefore = this.scrollDistance == 0;\n      this._disableScrollAfter = this.scrollDistance == this._getMaxScrollDistance();\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n\n  /**\n   * Determines what is the maximum length in pixels that can be set for the scroll distance. This\n   * is equal to the difference in width between the tab list container and tab header container.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _getMaxScrollDistance(): number {\n    const lengthOfTabList = this._tabListInner.nativeElement.scrollWidth;\n    const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n    return lengthOfTabList - viewLength || 0;\n  }\n\n  /** Tells the ink-bar to align itself to the current label wrapper */\n  _alignInkBarToSelectedTab(): void {\n    const selectedItem =\n      this._items && this._items.length ? this._items.toArray()[this.selectedIndex] : null;\n    const selectedLabelWrapper = selectedItem ? selectedItem.elementRef.nativeElement : null;\n\n    if (selectedLabelWrapper) {\n      this._inkBar.alignToElement(selectedLabelWrapper);\n    } else {\n      this._inkBar.hide();\n    }\n  }\n\n  /** Stops the currently-running paginator interval.  */\n  _stopInterval() {\n    this._stopScrolling.next();\n  }\n\n  /**\n   * Handles the user pressing down on one of the paginators.\n   * Starts scrolling the header after a certain amount of time.\n   * @param direction In which direction the paginator should be scrolled.\n   */\n  _handlePaginatorPress(direction: ScrollDirection, mouseEvent?: MouseEvent) {\n    // Don't start auto scrolling for right mouse button clicks. Note that we shouldn't have to\n    // null check the `button`, but we do it so we don't break tests that use fake events.\n    if (mouseEvent && mouseEvent.button != null && mouseEvent.button !== 0) {\n      return;\n    }\n\n    // Avoid overlapping timers.\n    this._stopInterval();\n\n    // Start a timer after the delay and keep firing based on the interval.\n    timer(HEADER_SCROLL_DELAY, HEADER_SCROLL_INTERVAL)\n      // Keep the timer going until something tells it to stop or the component is destroyed.\n      .pipe(takeUntil(merge(this._stopScrolling, this._destroyed)))\n      .subscribe(() => {\n        const {maxScrollDistance, distance} = this._scrollHeader(direction);\n\n        // Stop the timer if we've reached the start or the end.\n        if (distance === 0 || distance >= maxScrollDistance) {\n          this._stopInterval();\n        }\n      });\n  }\n\n  /**\n   * Scrolls the header to a given position.\n   * @param position Position to which to scroll.\n   * @returns Information on the current scroll distance and the maximum.\n   */\n  private _scrollTo(position: number) {\n    if (this.disablePagination) {\n      return {maxScrollDistance: 0, distance: 0};\n    }\n\n    const maxScrollDistance = this._getMaxScrollDistance();\n    this._scrollDistance = Math.max(0, Math.min(maxScrollDistance, position));\n\n    // Mark that the scroll distance has changed so that after the view is checked, the CSS\n    // transformation can move the header.\n    this._scrollDistanceChanged = true;\n    this._checkScrollingControls();\n\n    return {maxScrollDistance, distance: this._scrollDistance};\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  AfterContentChecked,\n  AfterContentInit,\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  Component,\n  ContentChildren,\n  ElementRef,\n  Input,\n  OnDestroy,\n  QueryList,\n  ViewChild,\n  ViewEncapsulation,\n  booleanAttribute,\n} from '@angular/core';\nimport {MatTabLabelWrapper} from './tab-label-wrapper';\nimport {MatInkBar} from './ink-bar';\nimport {MatPaginatedTabHeader} from './paginated-tab-header';\nimport {CdkObserveContent} from '@angular/cdk/observers';\nimport {MatRipple} from '../core';\n\n/**\n * The header of the tab group which displays a list of all the tabs in the tab group. Includes\n * an ink bar that follows the currently selected tab. When the tabs list's width exceeds the\n * width of the header container, then arrows will be displayed to allow the user to scroll\n * left and right across the header.\n * @docs-private\n */\n@Component({\n  selector: 'mat-tab-header',\n  templateUrl: 'tab-header.html',\n  styleUrl: 'tab-header.css',\n  encapsulation: ViewEncapsulation.None,\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  host: {\n    'class': 'mat-mdc-tab-header',\n    '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n    '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\",\n  },\n  imports: [MatRipple, CdkObserveContent],\n})\nexport class MatTabHeader\n  extends MatPaginatedTabHeader\n  implements AfterContentChecked, AfterContentInit, AfterViewInit, OnDestroy\n{\n  @ContentChildren(MatTabLabelWrapper, {descendants: false}) _items: QueryList<MatTabLabelWrapper>;\n  @ViewChild('tabListContainer', {static: true}) _tabListContainer: ElementRef;\n  @ViewChild('tabList', {static: true}) _tabList: ElementRef;\n  @ViewChild('tabListInner', {static: true}) _tabListInner: ElementRef;\n  @ViewChild('nextPaginator') _nextPaginator: ElementRef<HTMLElement>;\n  @ViewChild('previousPaginator') _previousPaginator: ElementRef<HTMLElement>;\n  _inkBar: MatInkBar;\n\n  /** Aria label of the header. */\n  @Input('aria-label') ariaLabel: string;\n\n  /** Sets the `aria-labelledby` of the header. */\n  @Input('aria-labelledby') ariaLabelledby: string;\n\n  /** Whether the ripple effect is disabled or not. */\n  @Input({transform: booleanAttribute})\n  disableRipple: boolean = false;\n\n  override ngAfterContentInit() {\n    this._inkBar = new MatInkBar(this._items);\n    super.ngAfterContentInit();\n  }\n\n  protected _itemSelected(event: KeyboardEvent) {\n    event.preventDefault();\n  }\n}\n", "<!--\n Note that this intentionally uses a `div` instead of a `button`, because it's not part of\n the regular tabs flow and is only here to support mouse users. It should also not be focusable.\n-->\n<div class=\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\"\n     #previousPaginator\n     mat-ripple\n     [matRippleDisabled]=\"_disableScrollBefore || disableRipple\"\n     [class.mat-mdc-tab-header-pagination-disabled]=\"_disableScrollBefore\"\n     (click)=\"_handlePaginatorClick('before')\"\n     (mousedown)=\"_handlePaginatorPress('before', $event)\"\n     (touchend)=\"_stopInterval()\">\n  <div class=\"mat-mdc-tab-header-pagination-chevron\"></div>\n</div>\n\n<div\n  class=\"mat-mdc-tab-label-container\"\n  #tabListContainer\n  (keydown)=\"_handleKeydown($event)\"\n  [class._mat-animation-noopable]=\"_animationMode === 'NoopAnimations'\">\n  <div\n    #tabList\n    class=\"mat-mdc-tab-list\"\n    role=\"tablist\"\n    [attr.aria-label]=\"ariaLabel || null\"\n    [attr.aria-labelledby]=\"ariaLabelledby || null\"\n    (cdkObserveContent)=\"_onContentChanges()\">\n    <div class=\"mat-mdc-tab-labels\" #tabListInner>\n      <ng-content></ng-content>\n    </div>\n  </div>\n</div>\n\n<div class=\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\"\n     #nextPaginator\n     mat-ripple\n     [matRippleDisabled]=\"_disableScrollAfter || disableRipple\"\n     [class.mat-mdc-tab-header-pagination-disabled]=\"_disableScrollAfter\"\n     (mousedown)=\"_handlePaginatorPress('after', $event)\"\n     (click)=\"_handlePaginatorClick('after')\"\n     (touchend)=\"_stopInterval()\">\n  <div class=\"mat-mdc-tab-header-pagination-chevron\"></div>\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {InjectionToken} from '@angular/core';\n\n/** Object that can be used to configure the default options for the tabs module. */\nexport interface MatTabsConfig {\n  /** Duration for the tab animation. Must be a valid CSS value (e.g. 600ms). */\n  animationDuration?: string;\n\n  /**\n   * Whether pagination should be disabled. This can be used to avoid unnecessary\n   * layout recalculations if it's known that pagination won't be required.\n   */\n  disablePagination?: boolean;\n\n  /**\n   * Whether the ink bar should fit its width to the size of the tab label content.\n   * This only applies to the MDC-based tabs.\n   */\n  fitInkBarToContent?: boolean;\n\n  /** Whether the tab group should grow to the size of the active tab. */\n  dynamicHeight?: boolean;\n\n  /** `tabindex` to be set on the inner element that wraps the tab content. */\n  contentTabIndex?: number;\n\n  /**\n   * By default tabs remove their content from the DOM while it's off-screen.\n   * Setting this to `true` will keep it in the DOM which will prevent elements\n   * like iframes and videos from reloading next time it comes back into the view.\n   */\n  preserveContent?: boolean;\n\n  /** Whether tabs should be stretched to fill the header. */\n  stretchTabs?: boolean;\n\n  /** Alignment for the tabs label. */\n  alignTabs?: 'start' | 'center' | 'end';\n}\n\n/** Injection token that can be used to provide the default options the tabs module. */\nexport const MAT_TABS_CONFIG = new InjectionToken<MatTabsConfig>('MAT_TABS_CONFIG');\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Direction, Directionality} from '@angular/cdk/bidi';\nimport {CdkPortalOutlet, TemplatePortal} from '@angular/cdk/portal';\nimport {CdkScrollable} from '@angular/cdk/scrolling';\nimport {\n  ANIMATION_MODULE_TYPE,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  Injector,\n  Input,\n  NgZone,\n  OnDestroy,\n  OnInit,\n  Output,\n  Renderer2,\n  ViewChild,\n  ViewEncapsulation,\n  afterNextRender,\n  inject,\n} from '@angular/core';\nimport {Subscription} from 'rxjs';\nimport {startWith} from 'rxjs/operators';\n\n/**\n * The portal host directive for the contents of the tab.\n * @docs-private\n */\n@Directive({selector: '[matTabBodyHost]'})\nexport class MatTabBodyPortal extends CdkPortalOutlet implements OnInit, OnDestroy {\n  private _host = inject(MatTabBody);\n\n  /** Subscription to events for when the tab body begins centering. */\n  private _centeringSub = Subscription.EMPTY;\n  /** Subscription to events for when the tab body finishes leaving from center position. */\n  private _leavingSub = Subscription.EMPTY;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n  }\n\n  /** Set initial visibility or set up subscription for changing visibility. */\n  override ngOnInit(): void {\n    super.ngOnInit();\n\n    this._centeringSub = this._host._beforeCentering\n      .pipe(startWith(this._host._isCenterPosition()))\n      .subscribe((isCentering: boolean) => {\n        if (this._host._content && isCentering && !this.hasAttached()) {\n          this.attach(this._host._content);\n        }\n      });\n\n    this._leavingSub = this._host._afterLeavingCenter.subscribe(() => {\n      if (!this._host.preserveContent) {\n        this.detach();\n      }\n    });\n  }\n\n  /** Clean up centering subscription. */\n  override ngOnDestroy(): void {\n    super.ngOnDestroy();\n    this._centeringSub.unsubscribe();\n    this._leavingSub.unsubscribe();\n  }\n}\n\n/**\n * These position states are used internally as animation states for the tab body. Setting the\n * position state to left, right, or center will transition the tab body from its current\n * position to its respective state. If there is not current position (void, in the case of a new\n * tab body), then there will be no transition animation to its state.\n *\n * In the case of a new tab body that should immediately be centered with an animating transition,\n * then left-origin-center or right-origin-center can be used, which will use left or right as its\n * pseudo-prior state.\n *\n * @deprecated Will stop being exported.\n * @breaking-change 21.0.0\n */\nexport type MatTabBodyPositionState = 'left' | 'center' | 'right';\n\n/**\n * The origin state is an internally used state that is set on a new tab body indicating if it\n * began to the left or right of the prior selected index. For example, if the selected index was\n * set to 1, and a new tab is created and selected at index 2, then the tab body would have an\n * origin of right because its index was greater than the prior selected index.\n *\n * @deprecated No longer being used. Will be removed.\n * @breaking-change 21.0.0\n */\nexport type MatTabBodyOriginState = 'left' | 'right';\n\n/**\n * Wrapper for the contents of a tab.\n * @docs-private\n */\n@Component({\n  selector: 'mat-tab-body',\n  templateUrl: 'tab-body.html',\n  styleUrl: 'tab-body.css',\n  encapsulation: ViewEncapsulation.None,\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  host: {\n    'class': 'mat-mdc-tab-body',\n    // In most cases the `visibility: hidden` that we set on the off-screen content is enough\n    // to stop interactions with it, but if a child element sets its own `visibility`, it'll\n    // override the one from the parent. This ensures that even those elements will be removed\n    // from the accessibility tree.\n    '[attr.inert]': '_position === \"center\" ? null : \"\"',\n  },\n  imports: [MatTabBodyPortal, CdkScrollable],\n})\nexport class MatTabBody implements OnInit, OnDestroy {\n  private _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _dir = inject(Directionality, {optional: true});\n  private _ngZone = inject(NgZone);\n  private _injector = inject(Injector);\n  private _renderer = inject(Renderer2);\n  private _animationsModule = inject(ANIMATION_MODULE_TYPE, {optional: true});\n  private _eventCleanups?: (() => void)[];\n  private _initialized: boolean;\n  private _fallbackTimer: ReturnType<typeof setTimeout>;\n\n  /** Current position of the tab-body in the tab-group. Zero means that the tab is visible. */\n  private _positionIndex: number;\n\n  /** Subscription to the directionality change observable. */\n  private _dirChangeSubscription = Subscription.EMPTY;\n\n  /** Current position of the body within the tab group. */\n  _position: MatTabBodyPositionState;\n\n  /** Previous position of the body. */\n  protected _previousPosition: MatTabBodyPositionState | undefined;\n\n  /** Event emitted when the tab begins to animate towards the center as the active tab. */\n  @Output() readonly _onCentering: EventEmitter<number> = new EventEmitter<number>();\n\n  /** Event emitted before the centering of the tab begins. */\n  @Output() readonly _beforeCentering: EventEmitter<boolean> = new EventEmitter<boolean>();\n\n  /** Event emitted before the centering of the tab begins. */\n  readonly _afterLeavingCenter: EventEmitter<void> = new EventEmitter<void>();\n\n  /** Event emitted when the tab completes its animation towards the center. */\n  @Output() readonly _onCentered: EventEmitter<void> = new EventEmitter<void>(true);\n\n  /** The portal host inside of this container into which the tab body content will be loaded. */\n  @ViewChild(MatTabBodyPortal) _portalHost: MatTabBodyPortal;\n\n  /** Element in which the content is rendered. */\n  @ViewChild('content') _contentElement: ElementRef<HTMLElement> | undefined;\n\n  /** The tab body content to display. */\n  @Input('content') _content: TemplatePortal;\n\n  // Note that the default value will always be overwritten by `MatTabBody`, but we need one\n  // anyway to prevent the animations module from throwing an error if the body is used on its own.\n  /** Duration for the tab's animation. */\n  @Input() animationDuration: string = '500ms';\n\n  /** Whether the tab's content should be kept in the DOM while it's off-screen. */\n  @Input() preserveContent: boolean = false;\n\n  /** The shifted index position of the tab body, where zero represents the active center tab. */\n  @Input()\n  set position(position: number) {\n    this._positionIndex = position;\n    this._computePositionAnimationState();\n  }\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    if (this._dir) {\n      const changeDetectorRef = inject(ChangeDetectorRef);\n      this._dirChangeSubscription = this._dir.change.subscribe((dir: Direction) => {\n        this._computePositionAnimationState(dir);\n        changeDetectorRef.markForCheck();\n      });\n    }\n  }\n\n  ngOnInit() {\n    this._bindTransitionEvents();\n\n    if (this._position === 'center') {\n      this._setActiveClass(true);\n\n      // Allows for the dynamic height to animate properly on the initial run.\n      afterNextRender(() => this._onCentering.emit(this._elementRef.nativeElement.clientHeight), {\n        injector: this._injector,\n      });\n    }\n\n    this._initialized = true;\n  }\n\n  ngOnDestroy() {\n    clearTimeout(this._fallbackTimer);\n    this._eventCleanups?.forEach(cleanup => cleanup());\n    this._dirChangeSubscription.unsubscribe();\n  }\n\n  /** Sets up the transition events. */\n  private _bindTransitionEvents() {\n    this._ngZone.runOutsideAngular(() => {\n      const element = this._elementRef.nativeElement;\n      const transitionDone = (event: TransitionEvent) => {\n        if (event.target === this._contentElement?.nativeElement) {\n          this._elementRef.nativeElement.classList.remove('mat-tab-body-animating');\n\n          // Only fire the actual callback when a transition is fully finished,\n          // otherwise the content can jump around when the next transition starts.\n          if (event.type === 'transitionend') {\n            this._transitionDone();\n          }\n        }\n      };\n\n      this._eventCleanups = [\n        this._renderer.listen(element, 'transitionstart', (event: TransitionEvent) => {\n          if (event.target === this._contentElement?.nativeElement) {\n            this._elementRef.nativeElement.classList.add('mat-tab-body-animating');\n            this._transitionStarted();\n          }\n        }),\n        this._renderer.listen(element, 'transitionend', transitionDone),\n        this._renderer.listen(element, 'transitioncancel', transitionDone),\n      ];\n    });\n  }\n\n  /** Called when a transition has started. */\n  private _transitionStarted() {\n    clearTimeout(this._fallbackTimer);\n    const isCentering = this._position === 'center';\n    this._beforeCentering.emit(isCentering);\n    if (isCentering) {\n      this._onCentering.emit(this._elementRef.nativeElement.clientHeight);\n    }\n  }\n\n  /** Called when a transition is done. */\n  private _transitionDone() {\n    if (this._position === 'center') {\n      this._onCentered.emit();\n    } else if (this._previousPosition === 'center') {\n      this._afterLeavingCenter.emit();\n    }\n  }\n\n  /** Sets the active styling on the tab body based on its current position. */\n  _setActiveClass(isActive: boolean) {\n    this._elementRef.nativeElement.classList.toggle('mat-mdc-tab-body-active', isActive);\n  }\n\n  /** The text direction of the containing app. */\n  _getLayoutDirection(): Direction {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n\n  /** Whether the provided position state is considered center, regardless of origin. */\n  _isCenterPosition(): boolean {\n    return this._positionIndex === 0;\n  }\n\n  /** Computes the position state that will be used for the tab-body animation trigger. */\n  private _computePositionAnimationState(dir: Direction = this._getLayoutDirection()) {\n    this._previousPosition = this._position;\n\n    if (this._positionIndex < 0) {\n      this._position = dir == 'ltr' ? 'left' : 'right';\n    } else if (this._positionIndex > 0) {\n      this._position = dir == 'ltr' ? 'right' : 'left';\n    } else {\n      this._position = 'center';\n    }\n\n    if (this._animationsDisabled()) {\n      this._simulateTransitionEvents();\n    } else if (\n      this._initialized &&\n      (this._position === 'center' || this._previousPosition === 'center')\n    ) {\n      // The transition events are load-bearing and in some cases they might not fire (e.g.\n      // tests setting `* {transition: none}` to disable animations). This timeout will simulate\n      // them if a transition doesn't start within a certain amount of time.\n      clearTimeout(this._fallbackTimer);\n      this._fallbackTimer = this._ngZone.runOutsideAngular(() =>\n        setTimeout(() => this._simulateTransitionEvents(), 100),\n      );\n    }\n  }\n\n  /** Simulates the body's transition events in an environment where they might not fire. */\n  private _simulateTransitionEvents() {\n    this._transitionStarted();\n    afterNextRender(() => this._transitionDone(), {injector: this._injector});\n  }\n\n  /** Whether animations are disabled for the tab group. */\n  private _animationsDisabled() {\n    return (\n      this._animationsModule === 'NoopAnimations' ||\n      this.animationDuration === '0ms' ||\n      this.animationDuration === '0s'\n    );\n  }\n}\n", "<div\n   class=\"mat-mdc-tab-body-content\"\n   #content\n   cdkScrollable\n   [class.mat-tab-body-content-left]=\"_position === 'left'\"\n   [class.mat-tab-body-content-right]=\"_position === 'right'\"\n   [class.mat-tab-body-content-can-animate]=\"_position === 'center' || _previousPosition === 'center'\">\n  <ng-template matTabBodyHost></ng-template>\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  AfterContentChecked,\n  AfterContentInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChildren,\n  ElementRef,\n  EventEmitter,\n  Input,\n  OnDestroy,\n  Output,\n  QueryList,\n  ViewChild,\n  ViewEncapsulation,\n  booleanAttribute,\n  inject,\n  numberAttribute,\n  ANIMATION_MODULE_TYPE,\n  ViewChildren,\n  AfterViewInit,\n  NgZone,\n} from '@angular/core';\nimport {MAT_TAB_GROUP, MatTab} from './tab';\nimport {MatTabHeader} from './tab-header';\nimport {ThemePalette, MatRipple} from '../core';\nimport {merge, Subscription} from 'rxjs';\nimport {MAT_TABS_CONFIG, MatTabsConfig} from './tab-config';\nimport {startWith} from 'rxjs/operators';\nimport {_IdGenerator, CdkMonitorFocus, FocusOrigin} from '@angular/cdk/a11y';\nimport {MatTabBody} from './tab-body';\nimport {CdkPortalOutlet} from '@angular/cdk/portal';\nimport {MatTabLabelWrapper} from './tab-label-wrapper';\nimport {Platform} from '@angular/cdk/platform';\n\n/** @docs-private */\nexport interface MatTabGroupBaseHeader {\n  _alignInkBarToSelectedTab(): void;\n  updatePagination(): void;\n  focusIndex: number;\n}\n\n/** Possible positions for the tab header. */\nexport type MatTabHeaderPosition = 'above' | 'below';\n\n/** Boolean constant that determines whether the tab group supports the `backgroundColor` input */\nconst ENABLE_BACKGROUND_INPUT = true;\n\n/**\n * Material design tab-group component. Supports basic tab pairs (label + content) and includes\n * animated ink-bar, keyboard navigation, and screen reader.\n * See: https://material.io/design/components/tabs.html\n */\n@Component({\n  selector: 'mat-tab-group',\n  exportAs: 'matTabGroup',\n  templateUrl: 'tab-group.html',\n  styleUrl: 'tab-group.css',\n  encapsulation: ViewEncapsulation.None,\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  providers: [\n    {\n      provide: MAT_TAB_GROUP,\n      useExisting: MatTabGroup,\n    },\n  ],\n  host: {\n    'class': 'mat-mdc-tab-group',\n    '[class]': '\"mat-\" + (color || \"primary\")',\n    '[class.mat-mdc-tab-group-dynamic-height]': 'dynamicHeight',\n    '[class.mat-mdc-tab-group-inverted-header]': 'headerPosition === \"below\"',\n    '[class.mat-mdc-tab-group-stretch-tabs]': 'stretchTabs',\n    '[attr.mat-align-tabs]': 'alignTabs',\n    '[style.--mat-tab-animation-duration]': 'animationDuration',\n  },\n  imports: [\n    MatTabHeader,\n    MatTabLabelWrapper,\n    CdkMonitorFocus,\n    MatRipple,\n    CdkPortalOutlet,\n    MatTabBody,\n  ],\n})\nexport class MatTabGroup\n  implements AfterViewInit, AfterContentInit, AfterContentChecked, OnDestroy\n{\n  readonly _elementRef = inject(ElementRef);\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n  private _ngZone = inject(NgZone);\n  private _tabsSubscription = Subscription.EMPTY;\n  private _tabLabelSubscription = Subscription.EMPTY;\n  private _tabBodySubscription = Subscription.EMPTY;\n  private _diAnimationsDisabled =\n    inject(ANIMATION_MODULE_TYPE, {optional: true}) === 'NoopAnimations';\n\n  /**\n   * All tabs inside the tab group. This includes tabs that belong to groups that are nested\n   * inside the current one. We filter out only the tabs that belong to this group in `_tabs`.\n   */\n  @ContentChildren(MatTab, {descendants: true}) _allTabs: QueryList<MatTab>;\n  @ViewChildren(MatTabBody) _tabBodies: QueryList<MatTabBody> | undefined;\n  @ViewChild('tabBodyWrapper') _tabBodyWrapper: ElementRef;\n  @ViewChild('tabHeader') _tabHeader: MatTabHeader;\n\n  /** All of the tabs that belong to the group. */\n  _tabs: QueryList<MatTab> = new QueryList<MatTab>();\n\n  /** The tab index that should be selected after the content has been checked. */\n  private _indexToSelect: number | null = 0;\n\n  /** Index of the tab that was focused last. */\n  private _lastFocusedTabIndex: number | null = null;\n\n  /** Snapshot of the height of the tab body wrapper before another tab is activated. */\n  private _tabBodyWrapperHeight: number = 0;\n\n  /**\n   * Theme color of the tab group. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/tabs/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  @Input()\n  color: ThemePalette;\n\n  /** Whether the ink bar should fit its width to the size of the tab label content. */\n  @Input({transform: booleanAttribute})\n  get fitInkBarToContent(): boolean {\n    return this._fitInkBarToContent;\n  }\n  set fitInkBarToContent(value: boolean) {\n    this._fitInkBarToContent = value;\n    this._changeDetectorRef.markForCheck();\n  }\n  private _fitInkBarToContent = false;\n\n  /** Whether tabs should be stretched to fill the header. */\n  @Input({alias: 'mat-stretch-tabs', transform: booleanAttribute})\n  stretchTabs: boolean = true;\n\n  /** Alignment for tabs label. */\n  @Input({alias: 'mat-align-tabs'})\n  alignTabs: string | null = null;\n\n  /** Whether the tab group should grow to the size of the active tab. */\n  @Input({transform: booleanAttribute})\n  dynamicHeight: boolean = false;\n\n  /** The index of the active tab. */\n  @Input({transform: numberAttribute})\n  get selectedIndex(): number | null {\n    return this._selectedIndex;\n  }\n  set selectedIndex(value: number) {\n    this._indexToSelect = isNaN(value) ? null : value;\n  }\n  private _selectedIndex: number | null = null;\n\n  /** Position of the tab header. */\n  @Input() headerPosition: MatTabHeaderPosition = 'above';\n\n  /** Duration for the tab animation. Will be normalized to milliseconds if no units are set. */\n  @Input()\n  get animationDuration(): string {\n    return this._animationDuration;\n  }\n  set animationDuration(value: string | number) {\n    const stringValue = value + '';\n    this._animationDuration = /^\\d+$/.test(stringValue) ? value + 'ms' : stringValue;\n  }\n  private _animationDuration: string;\n\n  /**\n   * `tabindex` to be set on the inner element that wraps the tab content. Can be used for improved\n   * accessibility when the tab does not have focusable elements or if it has scrollable content.\n   * The `tabindex` will be removed automatically for inactive tabs.\n   * Read more at https://www.w3.org/TR/wai-aria-practices/examples/tabs/tabs-2/tabs.html\n   */\n  @Input({transform: numberAttribute})\n  get contentTabIndex(): number | null {\n    return this._contentTabIndex;\n  }\n\n  set contentTabIndex(value: number) {\n    this._contentTabIndex = isNaN(value) ? null : value;\n  }\n\n  private _contentTabIndex: number | null;\n\n  /**\n   * Whether pagination should be disabled. This can be used to avoid unnecessary\n   * layout recalculations if it's known that pagination won't be required.\n   */\n  @Input({transform: booleanAttribute})\n  disablePagination: boolean = false;\n\n  /** Whether ripples in the tab group are disabled. */\n  @Input({transform: booleanAttribute})\n  disableRipple: boolean = false;\n\n  /**\n   * By default tabs remove their content from the DOM while it's off-screen.\n   * Setting this to `true` will keep it in the DOM which will prevent elements\n   * like iframes and videos from reloading next time it comes back into the view.\n   */\n  @Input({transform: booleanAttribute})\n  preserveContent: boolean = false;\n\n  /**\n   * Theme color of the background of the tab group. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/tabs/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   *\n   * @deprecated The background color should be customized through Sass theming APIs.\n   * @breaking-change 20.0.0 Remove this input\n   */\n  @Input()\n  get backgroundColor(): ThemePalette {\n    return this._backgroundColor;\n  }\n\n  set backgroundColor(value: ThemePalette) {\n    if (!ENABLE_BACKGROUND_INPUT) {\n      throw new Error(`mat-tab-group background color must be set through the Sass theming API`);\n    }\n\n    const classList: DOMTokenList = this._elementRef.nativeElement.classList;\n\n    classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n\n    if (value) {\n      classList.add('mat-tabs-with-background', `mat-background-${value}`);\n    }\n\n    this._backgroundColor = value;\n  }\n\n  private _backgroundColor: ThemePalette;\n\n  /** Aria label of the inner `tablist` of the group. */\n  @Input('aria-label') ariaLabel: string;\n\n  /** Sets the `aria-labelledby` of the inner `tablist` of the group. */\n  @Input('aria-labelledby') ariaLabelledby: string;\n\n  /** Output to enable support for two-way binding on `[(selectedIndex)]` */\n  @Output() readonly selectedIndexChange: EventEmitter<number> = new EventEmitter<number>();\n\n  /** Event emitted when focus has changed within a tab group. */\n  @Output() readonly focusChange: EventEmitter<MatTabChangeEvent> =\n    new EventEmitter<MatTabChangeEvent>();\n\n  /** Event emitted when the body animation has completed */\n  @Output() readonly animationDone: EventEmitter<void> = new EventEmitter<void>();\n\n  /** Event emitted when the tab selection has changed. */\n  @Output() readonly selectedTabChange: EventEmitter<MatTabChangeEvent> =\n    new EventEmitter<MatTabChangeEvent>(true);\n\n  private _groupId: string;\n\n  /** Whether the tab group is rendered on the server. */\n  protected _isServer: boolean = !inject(Platform).isBrowser;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const defaultConfig = inject<MatTabsConfig>(MAT_TABS_CONFIG, {optional: true});\n\n    this._groupId = inject(_IdGenerator).getId('mat-tab-group-');\n    this.animationDuration =\n      defaultConfig && defaultConfig.animationDuration ? defaultConfig.animationDuration : '500ms';\n    this.disablePagination =\n      defaultConfig && defaultConfig.disablePagination != null\n        ? defaultConfig.disablePagination\n        : false;\n    this.dynamicHeight =\n      defaultConfig && defaultConfig.dynamicHeight != null ? defaultConfig.dynamicHeight : false;\n    if (defaultConfig?.contentTabIndex != null) {\n      this.contentTabIndex = defaultConfig.contentTabIndex;\n    }\n    this.preserveContent = !!defaultConfig?.preserveContent;\n    this.fitInkBarToContent =\n      defaultConfig && defaultConfig.fitInkBarToContent != null\n        ? defaultConfig.fitInkBarToContent\n        : false;\n    this.stretchTabs =\n      defaultConfig && defaultConfig.stretchTabs != null ? defaultConfig.stretchTabs : true;\n    this.alignTabs =\n      defaultConfig && defaultConfig.alignTabs != null ? defaultConfig.alignTabs : null;\n  }\n\n  /**\n   * After the content is checked, this component knows what tabs have been defined\n   * and what the selected index should be. This is where we can know exactly what position\n   * each tab should be in according to the new selected index, and additionally we know how\n   * a new selected tab should transition in (from the left or right).\n   */\n  ngAfterContentChecked() {\n    // Don't clamp the `indexToSelect` immediately in the setter because it can happen that\n    // the amount of tabs changes before the actual change detection runs.\n    const indexToSelect = (this._indexToSelect = this._clampTabIndex(this._indexToSelect));\n\n    // If there is a change in selected index, emit a change event. Should not trigger if\n    // the selected index has not yet been initialized.\n    if (this._selectedIndex != indexToSelect) {\n      const isFirstRun = this._selectedIndex == null;\n\n      if (!isFirstRun) {\n        this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n        // Preserve the height so page doesn't scroll up during tab change.\n        // Fixes https://stackblitz.com/edit/mat-tabs-scroll-page-top-on-tab-change\n        const wrapper = this._tabBodyWrapper.nativeElement;\n        wrapper.style.minHeight = wrapper.clientHeight + 'px';\n      }\n\n      // Changing these values after change detection has run\n      // since the checked content may contain references to them.\n      Promise.resolve().then(() => {\n        this._tabs.forEach((tab, index) => (tab.isActive = index === indexToSelect));\n\n        if (!isFirstRun) {\n          this.selectedIndexChange.emit(indexToSelect);\n          // Clear the min-height, this was needed during tab change to avoid\n          // unnecessary scrolling.\n          this._tabBodyWrapper.nativeElement.style.minHeight = '';\n        }\n      });\n    }\n\n    // Setup the position for each tab and optionally setup an origin on the next selected tab.\n    this._tabs.forEach((tab: MatTab, index: number) => {\n      tab.position = index - indexToSelect;\n\n      // If there is already a selected tab, then set up an origin for the next selected tab\n      // if it doesn't have one already.\n      if (this._selectedIndex != null && tab.position == 0 && !tab.origin) {\n        tab.origin = indexToSelect - this._selectedIndex;\n      }\n    });\n\n    if (this._selectedIndex !== indexToSelect) {\n      this._selectedIndex = indexToSelect;\n      this._lastFocusedTabIndex = null;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n\n  ngAfterContentInit() {\n    this._subscribeToAllTabChanges();\n    this._subscribeToTabLabels();\n\n    // Subscribe to changes in the amount of tabs, in order to be\n    // able to re-render the content as new tabs are added or removed.\n    this._tabsSubscription = this._tabs.changes.subscribe(() => {\n      const indexToSelect = this._clampTabIndex(this._indexToSelect);\n\n      // Maintain the previously-selected tab if a new tab is added or removed and there is no\n      // explicit change that selects a different tab.\n      if (indexToSelect === this._selectedIndex) {\n        const tabs = this._tabs.toArray();\n        let selectedTab: MatTab | undefined;\n\n        for (let i = 0; i < tabs.length; i++) {\n          if (tabs[i].isActive) {\n            // Assign both to the `_indexToSelect` and `_selectedIndex` so we don't fire a changed\n            // event, otherwise the consumer may end up in an infinite loop in some edge cases like\n            // adding a tab within the `selectedIndexChange` event.\n            this._indexToSelect = this._selectedIndex = i;\n            this._lastFocusedTabIndex = null;\n            selectedTab = tabs[i];\n            break;\n          }\n        }\n\n        // If we haven't found an active tab and a tab exists at the selected index, it means\n        // that the active tab was swapped out. Since this won't be picked up by the rendering\n        // loop in `ngAfterContentChecked`, we need to sync it up manually.\n        if (!selectedTab && tabs[indexToSelect]) {\n          Promise.resolve().then(() => {\n            tabs[indexToSelect].isActive = true;\n            this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n          });\n        }\n      }\n\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n\n  ngAfterViewInit() {\n    this._tabBodySubscription = this._tabBodies!.changes.subscribe(() => this._bodyCentered(true));\n  }\n\n  /** Listens to changes in all of the tabs. */\n  private _subscribeToAllTabChanges() {\n    // Since we use a query with `descendants: true` to pick up the tabs, we may end up catching\n    // some that are inside of nested tab groups. We filter them out manually by checking that\n    // the closest group to the tab is the current one.\n    this._allTabs.changes.pipe(startWith(this._allTabs)).subscribe((tabs: QueryList<MatTab>) => {\n      this._tabs.reset(\n        tabs.filter(tab => {\n          return tab._closestTabGroup === this || !tab._closestTabGroup;\n        }),\n      );\n      this._tabs.notifyOnChanges();\n    });\n  }\n\n  ngOnDestroy() {\n    this._tabs.destroy();\n    this._tabsSubscription.unsubscribe();\n    this._tabLabelSubscription.unsubscribe();\n    this._tabBodySubscription.unsubscribe();\n  }\n\n  /** Re-aligns the ink bar to the selected tab element. */\n  realignInkBar() {\n    if (this._tabHeader) {\n      this._tabHeader._alignInkBarToSelectedTab();\n    }\n  }\n\n  /**\n   * Recalculates the tab group's pagination dimensions.\n   *\n   * WARNING: Calling this method can be very costly in terms of performance. It should be called\n   * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n   * page.\n   */\n  updatePagination() {\n    if (this._tabHeader) {\n      this._tabHeader.updatePagination();\n    }\n  }\n\n  /**\n   * Sets focus to a particular tab.\n   * @param index Index of the tab to be focused.\n   */\n  focusTab(index: number) {\n    const header = this._tabHeader;\n\n    if (header) {\n      header.focusIndex = index;\n    }\n  }\n\n  _focusChanged(index: number) {\n    this._lastFocusedTabIndex = index;\n    this.focusChange.emit(this._createChangeEvent(index));\n  }\n\n  private _createChangeEvent(index: number): MatTabChangeEvent {\n    const event = new MatTabChangeEvent();\n    event.index = index;\n    if (this._tabs && this._tabs.length) {\n      event.tab = this._tabs.toArray()[index];\n    }\n    return event;\n  }\n\n  /**\n   * Subscribes to changes in the tab labels. This is needed, because the @Input for the label is\n   * on the MatTab component, whereas the data binding is inside the MatTabGroup. In order for the\n   * binding to be updated, we need to subscribe to changes in it and trigger change detection\n   * manually.\n   */\n  private _subscribeToTabLabels() {\n    if (this._tabLabelSubscription) {\n      this._tabLabelSubscription.unsubscribe();\n    }\n\n    this._tabLabelSubscription = merge(...this._tabs.map(tab => tab._stateChanges)).subscribe(() =>\n      this._changeDetectorRef.markForCheck(),\n    );\n  }\n\n  /** Clamps the given index to the bounds of 0 and the tabs length. */\n  private _clampTabIndex(index: number | null): number {\n    // Note the `|| 0`, which ensures that values like NaN can't get through\n    // and which would otherwise throw the component into an infinite loop\n    // (since Math.max(NaN, 0) === NaN).\n    return Math.min(this._tabs.length - 1, Math.max(index || 0, 0));\n  }\n\n  /** Returns a unique id for each tab label element */\n  _getTabLabelId(tab: MatTab, index: number): string {\n    return tab.id || `${this._groupId}-label-${index}`;\n  }\n\n  /** Returns a unique id for each tab content element */\n  _getTabContentId(index: number): string {\n    return `${this._groupId}-content-${index}`;\n  }\n\n  /**\n   * Sets the height of the body wrapper to the height of the activating tab if dynamic\n   * height property is true.\n   */\n  _setTabBodyWrapperHeight(tabHeight: number): void {\n    if (!this.dynamicHeight || !this._tabBodyWrapperHeight) {\n      this._tabBodyWrapperHeight = tabHeight;\n      return;\n    }\n\n    const wrapper: HTMLElement = this._tabBodyWrapper.nativeElement;\n\n    wrapper.style.height = this._tabBodyWrapperHeight + 'px';\n\n    // This conditional forces the browser to paint the height so that\n    // the animation to the new height can have an origin.\n    if (this._tabBodyWrapper.nativeElement.offsetHeight) {\n      wrapper.style.height = tabHeight + 'px';\n    }\n  }\n\n  /** Removes the height of the tab body wrapper. */\n  _removeTabBodyWrapperHeight(): void {\n    const wrapper = this._tabBodyWrapper.nativeElement;\n    this._tabBodyWrapperHeight = wrapper.clientHeight;\n    wrapper.style.height = '';\n    this._ngZone.run(() => this.animationDone.emit());\n  }\n\n  /** Handle click events, setting new selected index if appropriate. */\n  _handleClick(tab: MatTab, tabHeader: MatTabGroupBaseHeader, index: number) {\n    tabHeader.focusIndex = index;\n\n    if (!tab.disabled) {\n      this.selectedIndex = index;\n    }\n  }\n\n  /** Retrieves the tabindex for the tab. */\n  _getTabIndex(index: number): number {\n    const targetIndex = this._lastFocusedTabIndex ?? this.selectedIndex;\n    return index === targetIndex ? 0 : -1;\n  }\n\n  /** Callback for when the focused state of a tab has changed. */\n  _tabFocusChanged(focusOrigin: FocusOrigin, index: number) {\n    // Mouse/touch focus happens during the `mousedown`/`touchstart` phase which\n    // can cause the tab to be moved out from under the pointer, interrupting the\n    // click sequence (see #21898). We don't need to scroll the tab into view for\n    // such cases anyway, because it will be done when the tab becomes selected.\n    if (focusOrigin && focusOrigin !== 'mouse' && focusOrigin !== 'touch') {\n      this._tabHeader.focusIndex = index;\n    }\n  }\n\n  /**\n   * Callback invoked when the centered state of a tab body changes.\n   * @param isCenter Whether the tab will be in the center.\n   */\n  protected _bodyCentered(isCenter: boolean) {\n    // Marks all the existing tabs as inactive and the center tab as active. Note that this can\n    // be achieved much easier by using a class binding on each body. The problem with\n    // doing so is that we can't control the timing of when the class is removed from the\n    // previously-active element and added to the newly-active one. If there's a tick between\n    // removing the class and adding the new one, the content will jump in a very jarring way.\n    // We go through the trouble of setting the classes ourselves to guarantee that they're\n    // swapped out at the same time.\n    if (isCenter) {\n      this._tabBodies?.forEach((body, i) => body._setActiveClass(i === this._selectedIndex));\n    }\n  }\n\n  protected _animationsDisabled(): boolean {\n    return (\n      this._diAnimationsDisabled ||\n      this.animationDuration === '0' ||\n      this.animationDuration === '0ms'\n    );\n  }\n}\n\n/** A simple change event emitted on focus or selection changes. */\nexport class MatTabChangeEvent {\n  /** Index of the currently-selected tab. */\n  index: number;\n  /** Reference to the currently-selected tab. */\n  tab: MatTab;\n}\n", "<mat-tab-header #tabHeader\n                [selectedIndex]=\"selectedIndex || 0\"\n                [disableRipple]=\"disableRipple\"\n                [disablePagination]=\"disablePagination\"\n                [aria-label]=\"ariaLabel\"\n                [aria-labelledby]=\"ariaLabelledby\"\n                (indexFocused)=\"_focusChanged($event)\"\n                (selectFocusedIndex)=\"selectedIndex = $event\">\n\n  @for (tab of _tabs; track tab) {\n    <div class=\"mdc-tab mat-mdc-tab mat-focus-indicator\"\n        #tabNode\n        role=\"tab\"\n        matTabLabelWrapper\n        cdkMonitorElementFocus\n        [id]=\"_getTabLabelId(tab, $index)\"\n        [attr.tabIndex]=\"_getTabIndex($index)\"\n        [attr.aria-posinset]=\"$index + 1\"\n        [attr.aria-setsize]=\"_tabs.length\"\n        [attr.aria-controls]=\"_getTabContentId($index)\"\n        [attr.aria-selected]=\"selectedIndex === $index\"\n        [attr.aria-label]=\"tab.ariaLabel || null\"\n        [attr.aria-labelledby]=\"(!tab.ariaLabel && tab.ariaLabelledby) ? tab.ariaLabelledby : null\"\n        [class.mdc-tab--active]=\"selectedIndex === $index\"\n        [class]=\"tab.labelClass\"\n        [disabled]=\"tab.disabled\"\n        [fitInkBarToContent]=\"fitInkBarToContent\"\n        (click)=\"_handleClick(tab, tabHeader, $index)\"\n        (cdkFocusChange)=\"_tabFocusChanged($event, $index)\">\n      <span class=\"mdc-tab__ripple\"></span>\n\n      <!-- Needs to be a separate element, because we can't put\n          `overflow: hidden` on tab due to the ink bar. -->\n      <div\n        class=\"mat-mdc-tab-ripple\"\n        mat-ripple\n        [matRippleTrigger]=\"tabNode\"\n        [matRippleDisabled]=\"tab.disabled || disableRipple\"></div>\n\n      <span class=\"mdc-tab__content\">\n        <span class=\"mdc-tab__text-label\">\n          <!--\n            If there is a label template, use it, otherwise fall back to the text label.\n            Note that we don't have indentation around the text label, because it adds\n            whitespace around the text which breaks some internal tests.\n          -->\n          @if (tab.templateLabel) {\n            <ng-template [cdkPortalOutlet]=\"tab.templateLabel\"></ng-template>\n          } @else {{{tab.textLabel}}}\n        </span>\n      </span>\n    </div>\n  }\n</mat-tab-header>\n\n<!--\n  We need to project the content somewhere to avoid hydration errors. Some observations:\n  1. This is only necessary on the server.\n  2. We get a hydration error if there aren't any nodes after the `ng-content`.\n  3. We get a hydration error if `ng-content` is wrapped in another element.\n-->\n@if (_isServer) {\n  <ng-content/>\n}\n\n<div\n  class=\"mat-mdc-tab-body-wrapper\"\n  [class._mat-animation-noopable]=\"_animationsDisabled()\"\n  #tabBodyWrapper>\n  @for (tab of _tabs; track tab;) {\n    <mat-tab-body role=\"tabpanel\"\n                 [id]=\"_getTabContentId($index)\"\n                 [attr.tabindex]=\"(contentTabIndex != null && selectedIndex === $index) ? contentTabIndex : null\"\n                 [attr.aria-labelledby]=\"_getTabLabelId(tab, $index)\"\n                 [attr.aria-hidden]=\"selectedIndex !== $index\"\n                 [class]=\"tab.bodyClass\"\n                 [content]=\"tab.content!\"\n                 [position]=\"tab.position!\"\n                 [animationDuration]=\"animationDuration\"\n                 [preserveContent]=\"preserveContent\"\n                 (_onCentered)=\"_removeTabBodyWrapperHeight()\"\n                 (_onCentering)=\"_setTabBodyWrapperHeight($event)\"\n                 (_beforeCentering)=\"_bodyCentered($event)\"/>\n  }\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {\n  AfterContentInit,\n  AfterViewInit,\n  booleanAttribute,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChildren,\n  ElementRef,\n  forwardRef,\n  Input,\n  NgZone,\n  numberAttribute,\n  OnDestroy,\n  QueryList,\n  ViewChild,\n  ViewEncapsulation,\n  ANIMATION_MODULE_TYPE,\n  inject,\n  HostAttributeToken,\n  signal,\n  computed,\n} from '@angular/core';\nimport {\n  MAT_RIPPLE_GLOBAL_OPTIONS,\n  MatRipple,\n  RippleConfig,\n  RippleGlobalOptions,\n  RippleTarget,\n  ThemePalette,\n  _StructuralStylesLoader,\n} from '../../core';\nimport {_IdGenerator, FocusableOption, FocusMonitor} from '@angular/cdk/a11y';\nimport {Directionality} from '@angular/cdk/bidi';\nimport {ViewportRuler} from '@angular/cdk/scrolling';\nimport {Platform} from '@angular/cdk/platform';\nimport {MatInkBar, InkBarItem} from '../ink-bar';\nimport {BehaviorSubject, Subject} from 'rxjs';\nimport {startWith, takeUntil} from 'rxjs/operators';\nimport {ENTER, SPACE} from '@angular/cdk/keycodes';\nimport {MAT_TABS_CONFIG, MatTabsConfig} from '../tab-config';\nimport {MatPaginatedTabHeader, MatPaginatedTabHeaderItem} from '../paginated-tab-header';\nimport {CdkObserveContent} from '@angular/cdk/observers';\nimport {_CdkPrivateStyleLoader} from '@angular/cdk/private';\n\n/**\n * Navigation component matching the styles of the tab group header.\n * Provides anchored navigation with animated ink bar.\n */\n@Component({\n  selector: '[mat-tab-nav-bar]',\n  exportAs: 'matTabNavBar, matTabNav',\n  templateUrl: 'tab-nav-bar.html',\n  styleUrl: 'tab-nav-bar.css',\n  host: {\n    '[attr.role]': '_getRole()',\n    'class': 'mat-mdc-tab-nav-bar mat-mdc-tab-header',\n    '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n    '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\",\n    '[class.mat-mdc-tab-nav-bar-stretch-tabs]': 'stretchTabs',\n    '[class.mat-primary]': 'color !== \"warn\" && color !== \"accent\"',\n    '[class.mat-accent]': 'color === \"accent\"',\n    '[class.mat-warn]': 'color === \"warn\"',\n    '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n    '[style.--mat-tab-animation-duration]': 'animationDuration',\n  },\n  encapsulation: ViewEncapsulation.None,\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  imports: [MatRipple, CdkObserveContent],\n})\nexport class MatTabNav extends MatPaginatedTabHeader implements AfterContentInit, AfterViewInit {\n  _focusedItem = signal<MatPaginatedTabHeaderItem | null>(null);\n\n  /** Whether the ink bar should fit its width to the size of the tab label content. */\n  @Input({transform: booleanAttribute})\n  get fitInkBarToContent(): boolean {\n    return this._fitInkBarToContent.value;\n  }\n  set fitInkBarToContent(value: boolean) {\n    this._fitInkBarToContent.next(value);\n    this._changeDetectorRef.markForCheck();\n  }\n  _fitInkBarToContent = new BehaviorSubject(false);\n\n  /** Whether tabs should be stretched to fill the header. */\n  @Input({alias: 'mat-stretch-tabs', transform: booleanAttribute})\n  stretchTabs: boolean = true;\n\n  @Input()\n  get animationDuration(): string {\n    return this._animationDuration;\n  }\n\n  set animationDuration(value: string | number) {\n    const stringValue = value + '';\n    this._animationDuration = /^\\d+$/.test(stringValue) ? value + 'ms' : stringValue;\n  }\n\n  private _animationDuration: string;\n\n  /** Query list of all tab links of the tab navigation. */\n  @ContentChildren(forwardRef(() => MatTabLink), {descendants: true}) _items: QueryList<MatTabLink>;\n\n  /**\n   * Theme color of the background of the tab nav. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/tabs/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  @Input()\n  get backgroundColor(): ThemePalette {\n    return this._backgroundColor;\n  }\n\n  set backgroundColor(value: ThemePalette) {\n    const classList = this._elementRef.nativeElement.classList;\n    classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n\n    if (value) {\n      classList.add('mat-tabs-with-background', `mat-background-${value}`);\n    }\n\n    this._backgroundColor = value;\n  }\n\n  private _backgroundColor: ThemePalette;\n\n  /** Whether the ripple effect is disabled or not. */\n  @Input({transform: booleanAttribute})\n  disableRipple: boolean = false;\n\n  /**\n   * Theme color of the nav bar. This API is supported in M2 themes only, it has\n   * no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/tabs/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  @Input() color: ThemePalette = 'primary';\n\n  /**\n   * Associated tab panel controlled by the nav bar. If not provided, then the nav bar\n   * follows the ARIA link / navigation landmark pattern. If provided, it follows the\n   * ARIA tabs design pattern.\n   */\n  @Input() tabPanel?: MatTabNavPanel;\n\n  @ViewChild('tabListContainer', {static: true}) _tabListContainer: ElementRef;\n  @ViewChild('tabList', {static: true}) _tabList: ElementRef;\n  @ViewChild('tabListInner', {static: true}) _tabListInner: ElementRef;\n  @ViewChild('nextPaginator') _nextPaginator: ElementRef<HTMLElement>;\n  @ViewChild('previousPaginator') _previousPaginator: ElementRef<HTMLElement>;\n  _inkBar: MatInkBar;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const elementRef = inject(ElementRef);\n    const dir = inject(Directionality, {optional: true});\n    const ngZone = inject(NgZone);\n    const changeDetectorRef = inject(ChangeDetectorRef);\n    const viewportRuler = inject(ViewportRuler);\n    const platform = inject(Platform);\n    const animationMode = inject(ANIMATION_MODULE_TYPE, {optional: true});\n    const defaultConfig = inject<MatTabsConfig>(MAT_TABS_CONFIG, {optional: true});\n\n    super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n    this.disablePagination =\n      defaultConfig && defaultConfig.disablePagination != null\n        ? defaultConfig.disablePagination\n        : false;\n    this.fitInkBarToContent =\n      defaultConfig && defaultConfig.fitInkBarToContent != null\n        ? defaultConfig.fitInkBarToContent\n        : false;\n    this.stretchTabs =\n      defaultConfig && defaultConfig.stretchTabs != null ? defaultConfig.stretchTabs : true;\n  }\n\n  protected _itemSelected() {\n    // noop\n  }\n\n  override ngAfterContentInit() {\n    this._inkBar = new MatInkBar(this._items);\n    // We need this to run before the `changes` subscription in parent to ensure that the\n    // selectedIndex is up-to-date by the time the super class starts looking for it.\n    this._items.changes\n      .pipe(startWith(null), takeUntil(this._destroyed))\n      .subscribe(() => this.updateActiveLink());\n\n    super.ngAfterContentInit();\n\n    // Turn the `change` stream into a signal to try and avoid \"changed after checked\" errors.\n    this._keyManager!.change.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() =>\n      this._focusedItem.set(this._keyManager?.activeItem || null),\n    );\n  }\n\n  override ngAfterViewInit() {\n    if (!this.tabPanel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw new Error('A mat-tab-nav-panel must be specified via [tabPanel].');\n    }\n    super.ngAfterViewInit();\n  }\n\n  /** Notifies the component that the active link has been changed. */\n  updateActiveLink() {\n    if (!this._items) {\n      return;\n    }\n\n    const items = this._items.toArray();\n\n    for (let i = 0; i < items.length; i++) {\n      if (items[i].active) {\n        this.selectedIndex = i;\n        if (this.tabPanel) {\n          this.tabPanel._activeTabId = items[i].id;\n        }\n        // Updating the `selectedIndex` won't trigger the `change` event on\n        // the key manager so we need to set the signal from here.\n        this._focusedItem.set(items[i]);\n        this._changeDetectorRef.markForCheck();\n        return;\n      }\n    }\n\n    this.selectedIndex = -1;\n  }\n\n  _getRole(): string | null {\n    return this.tabPanel ? 'tablist' : this._elementRef.nativeElement.getAttribute('role');\n  }\n\n  _hasFocus(link: MatTabLink): boolean {\n    return this._keyManager?.activeItem === link;\n  }\n}\n\n/**\n * Link inside a `mat-tab-nav-bar`.\n */\n@Component({\n  selector: '[mat-tab-link], [matTabLink]',\n  exportAs: 'matTabLink',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  templateUrl: 'tab-link.html',\n  styleUrl: 'tab-link.css',\n  host: {\n    'class': 'mdc-tab mat-mdc-tab-link mat-focus-indicator',\n    '[attr.aria-controls]': '_getAriaControls()',\n    '[attr.aria-current]': '_getAriaCurrent()',\n    '[attr.aria-disabled]': 'disabled',\n    '[attr.aria-selected]': '_getAriaSelected()',\n    '[attr.id]': 'id',\n    '[attr.tabIndex]': '_tabIndex()',\n    '[attr.role]': '_getRole()',\n    '[class.mat-mdc-tab-disabled]': 'disabled',\n    '[class.mdc-tab--active]': 'active',\n    '(focus)': '_handleFocus()',\n    '(keydown)': '_handleKeydown($event)',\n  },\n  imports: [MatRipple],\n})\nexport class MatTabLink\n  extends InkBarItem\n  implements AfterViewInit, OnDestroy, RippleTarget, FocusableOption\n{\n  private _tabNavBar = inject(MatTabNav);\n  elementRef = inject(ElementRef);\n  private _focusMonitor = inject(FocusMonitor);\n\n  private readonly _destroyed = new Subject<void>();\n\n  /** Whether the tab link is active or not. */\n  protected _isActive: boolean = false;\n\n  protected _tabIndex = computed(() =>\n    this._tabNavBar._focusedItem() === this ? this.tabIndex : -1,\n  );\n\n  /** Whether the link is active. */\n  @Input({transform: booleanAttribute})\n  get active(): boolean {\n    return this._isActive;\n  }\n\n  set active(value: boolean) {\n    if (value !== this._isActive) {\n      this._isActive = value;\n      this._tabNavBar.updateActiveLink();\n    }\n  }\n\n  /** Whether the tab link is disabled. */\n  @Input({transform: booleanAttribute})\n  disabled: boolean = false;\n\n  /** Whether ripples are disabled on the tab link. */\n  @Input({transform: booleanAttribute})\n  disableRipple: boolean = false;\n\n  @Input({\n    transform: (value: unknown) => (value == null ? 0 : numberAttribute(value)),\n  })\n  tabIndex: number = 0;\n\n  /**\n   * Ripple configuration for ripples that are launched on pointer down. The ripple config\n   * is set to the global ripple options since we don't have any configurable options for\n   * the tab link ripples.\n   * @docs-private\n   */\n  rippleConfig: RippleConfig & RippleGlobalOptions;\n\n  /**\n   * Whether ripples are disabled on interaction.\n   * @docs-private\n   */\n  get rippleDisabled(): boolean {\n    return (\n      this.disabled ||\n      this.disableRipple ||\n      this._tabNavBar.disableRipple ||\n      !!this.rippleConfig.disabled\n    );\n  }\n\n  /** Unique id for the tab. */\n  @Input() id: string = inject(_IdGenerator).getId('mat-tab-link-');\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    const globalRippleOptions = inject<RippleGlobalOptions | null>(MAT_RIPPLE_GLOBAL_OPTIONS, {\n      optional: true,\n    });\n    const tabIndex = inject(new HostAttributeToken('tabindex'), {optional: true});\n    const animationMode = inject(ANIMATION_MODULE_TYPE, {optional: true});\n\n    this.rippleConfig = globalRippleOptions || {};\n    this.tabIndex = tabIndex == null ? 0 : parseInt(tabIndex) || 0;\n\n    if (animationMode === 'NoopAnimations') {\n      this.rippleConfig.animation = {enterDuration: 0, exitDuration: 0};\n    }\n\n    this._tabNavBar._fitInkBarToContent\n      .pipe(takeUntil(this._destroyed))\n      .subscribe(fitInkBarToContent => {\n        this.fitInkBarToContent = fitInkBarToContent;\n      });\n  }\n\n  /** Focuses the tab link. */\n  focus() {\n    this.elementRef.nativeElement.focus();\n  }\n\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this.elementRef);\n  }\n\n  override ngOnDestroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n    super.ngOnDestroy();\n    this._focusMonitor.stopMonitoring(this.elementRef);\n  }\n\n  _handleFocus() {\n    // Since we allow navigation through tabbing in the nav bar, we\n    // have to update the focused index whenever the link receives focus.\n    this._tabNavBar.focusIndex = this._tabNavBar._items.toArray().indexOf(this);\n  }\n\n  _handleKeydown(event: KeyboardEvent) {\n    if (event.keyCode === SPACE || event.keyCode === ENTER) {\n      if (this.disabled) {\n        event.preventDefault();\n      } else if (this._tabNavBar.tabPanel) {\n        // Only prevent the default action on space since it can scroll the page.\n        // Don't prevent enter since it can break link navigation.\n        if (event.keyCode === SPACE) {\n          event.preventDefault();\n        }\n\n        this.elementRef.nativeElement.click();\n      }\n    }\n  }\n\n  _getAriaControls(): string | null {\n    return this._tabNavBar.tabPanel\n      ? this._tabNavBar.tabPanel?.id\n      : this.elementRef.nativeElement.getAttribute('aria-controls');\n  }\n\n  _getAriaSelected(): string | null {\n    if (this._tabNavBar.tabPanel) {\n      return this.active ? 'true' : 'false';\n    } else {\n      return this.elementRef.nativeElement.getAttribute('aria-selected');\n    }\n  }\n\n  _getAriaCurrent(): string | null {\n    return this.active && !this._tabNavBar.tabPanel ? 'page' : null;\n  }\n\n  _getRole(): string | null {\n    return this._tabNavBar.tabPanel ? 'tab' : this.elementRef.nativeElement.getAttribute('role');\n  }\n}\n\n/**\n * Tab panel component associated with MatTabNav.\n */\n@Component({\n  selector: 'mat-tab-nav-panel',\n  exportAs: 'matTabNavPanel',\n  template: '<ng-content></ng-content>',\n  host: {\n    '[attr.aria-labelledby]': '_activeTabId',\n    '[attr.id]': 'id',\n    'class': 'mat-mdc-tab-nav-panel',\n    'role': 'tabpanel',\n  },\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class MatTabNavPanel {\n  /** Unique id for the tab panel. */\n  @Input() id: string = inject(_IdGenerator).getId('mat-tab-nav-panel-');\n\n  /** Id of the active tab in the nav bar. */\n  _activeTabId?: string;\n}\n", "<!--\n Note that this intentionally uses a `div` instead of a `button`, because it's not part of\n the regular tabs flow and is only here to support mouse users. It should also not be focusable.\n-->\n<div class=\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\"\n     #previousPaginator\n     mat-ripple\n     [matRippleDisabled]=\"_disableScrollBefore || disableRipple\"\n     [class.mat-mdc-tab-header-pagination-disabled]=\"_disableScrollBefore\"\n     (click)=\"_handlePaginatorClick('before')\"\n     (mousedown)=\"_handlePaginatorPress('before', $event)\"\n     (touchend)=\"_stopInterval()\">\n  <div class=\"mat-mdc-tab-header-pagination-chevron\"></div>\n</div>\n\n<div class=\"mat-mdc-tab-link-container\" #tabListContainer (keydown)=\"_handleKeydown($event)\">\n  <div class=\"mat-mdc-tab-list\" #tabList (cdkObserveContent)=\"_onContentChanges()\">\n    <div class=\"mat-mdc-tab-links\" #tabListInner>\n      <ng-content></ng-content>\n    </div>\n  </div>\n</div>\n\n<div class=\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\"\n     #nextPaginator\n     mat-ripple\n     [matRippleDisabled]=\"_disableScrollAfter || disableRipple\"\n     [class.mat-mdc-tab-header-pagination-disabled]=\"_disableScrollAfter\"\n     (mousedown)=\"_handlePaginatorPress('after', $event)\"\n     (click)=\"_handlePaginatorClick('after')\"\n     (touchend)=\"_stopInterval()\">\n  <div class=\"mat-mdc-tab-header-pagination-chevron\"></div>\n</div>\n", "<span class=\"mdc-tab__ripple\"></span>\n\n<div\n  class=\"mat-mdc-tab-ripple\"\n  mat-ripple\n  [matRippleTrigger]=\"elementRef.nativeElement\"\n  [matRippleDisabled]=\"rippleDisabled\"></div>\n\n<span class=\"mdc-tab__content\">\n  <span class=\"mdc-tab__text-label\">\n    <ng-content></ng-content>\n  </span>\n</span>\n\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '../core';\nimport {MatTabContent} from './tab-content';\nimport {MatTabLabel} from './tab-label';\nimport {MatTab} from './tab';\nimport {MatTabGroup} from './tab-group';\nimport {MatTabNav, MatTabNavPanel, MatTabLink} from './tab-nav-bar/tab-nav-bar';\n\n@NgModule({\n  imports: [\n    MatCommonModule,\n    MatTabContent,\n    MatTabLabel,\n    MatTab,\n    MatTabGroup,\n    MatTabNav,\n    MatTabNavPanel,\n    MatTabLink,\n  ],\n  exports: [\n    MatCommonModule,\n    MatTabContent,\n    MatTabLabel,\n    MatTab,\n    MatTabGroup,\n    MatTabNav,\n    MatTabNavPanel,\n    MatTabLink,\n  ],\n})\nexport class MatTabsModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Animations used by the Material tabs.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0.\n */\nexport const matTabsAnimations: {\n  readonly translateTab: any;\n} = {\n  // Represents:\n  // trigger('translateTab', [\n  //   // Transitions to `none` instead of 0, because some browsers might blur the content.\n  //   state(\n  //     'center, void, left-origin-center, right-origin-center',\n  //     style({transform: 'none', visibility: 'visible'}),\n  //   ),\n\n  //   // If the tab is either on the left or right, we additionally add a `min-height` of 1px\n  //   // in order to ensure that the element has a height before its state changes. This is\n  //   // necessary because <PERSON><PERSON> does seem to skip the transition in RTL mode if the element does\n  //   // not have a static height and is not rendered. See related issue: #9465\n  //   state(\n  //     'left',\n  //     style({\n  //       transform: 'translate3d(-100%, 0, 0)',\n  //       minHeight: '1px',\n\n  //       // Normally this is redundant since we detach the content from the DOM, but if the user\n  //       // opted into keeping the content in the DOM, we have to hide it so it isn't focusable.\n  //       visibility: 'hidden',\n  //     }),\n  //   ),\n  //   state(\n  //     'right',\n  //     style({\n  //       transform: 'translate3d(100%, 0, 0)',\n  //       minHeight: '1px',\n  //       visibility: 'hidden',\n  //     }),\n  //   ),\n\n  //   transition(\n  //     '* => left, * => right, left => center, right => center',\n  //     animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n  //   ),\n  //   transition('void => left-origin-center', [\n  //     style({transform: 'translate3d(-100%, 0, 0)', visibility: 'hidden'}),\n  //     animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n  //   ]),\n  //   transition('void => right-origin-center', [\n  //     style({transform: 'translate3d(100%, 0, 0)', visibility: 'hidden'}),\n  //     animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n  //   ]),\n  // ])\n\n  /** Animation translates a tab along the X axis. */\n  translateTab: {\n    type: 7,\n    name: 'translateTab',\n    definitions: [\n      {\n        type: 0,\n        name: 'center, void, left-origin-center, right-origin-center',\n        styles: {\n          type: 6,\n          styles: {transform: 'none', visibility: 'visible'},\n          offset: null,\n        },\n      },\n      {\n        type: 0,\n        name: 'left',\n        styles: {\n          type: 6,\n          styles: {\n            transform: 'translate3d(-100%, 0, 0)',\n            minHeight: '1px',\n            visibility: 'hidden',\n          },\n          offset: null,\n        },\n      },\n      {\n        type: 0,\n        name: 'right',\n        styles: {\n          type: 6,\n          styles: {\n            transform: 'translate3d(100%, 0, 0)',\n            minHeight: '1px',\n            visibility: 'hidden',\n          },\n          offset: null,\n        },\n      },\n      {\n        type: 1,\n        expr: '* => left, * => right, left => center, right => center',\n        animation: {\n          type: 4,\n          styles: null,\n          timings: '{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)',\n        },\n        options: null,\n      },\n      {\n        type: 1,\n        expr: 'void => left-origin-center',\n        animation: [\n          {\n            type: 6,\n            styles: {transform: 'translate3d(-100%, 0, 0)', visibility: 'hidden'},\n            offset: null,\n          },\n          {\n            type: 4,\n            styles: null,\n            timings: '{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)',\n          },\n        ],\n        options: null,\n      },\n      {\n        type: 1,\n        expr: 'void => right-origin-center',\n        animation: [\n          {\n            type: 6,\n            styles: {transform: 'translate3d(100%, 0, 0)', visibility: 'hidden'},\n            offset: null,\n          },\n          {\n            type: 4,\n            styles: null,\n            timings: '{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)',\n          },\n        ],\n        options: null,\n      },\n    ],\n    options: {},\n  },\n};\n"], "names": ["observableOf"], "mappings": ";;;;;;;;;;;;;;;;;;AAUA;;;;AAIG;MACU,eAAe,GAAG,IAAI,cAAc,CAAgB,eAAe;AAEhF;MAKa,aAAa,CAAA;AACxB,IAAA,QAAQ,GAAG,MAAM,CAAmB,WAAW,CAAC;AAGhD,IAAA,WAAA,GAAA;uGAJW,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,SAAA,EAFb,CAAC,EAAC,OAAO,EAAE,eAAe,EAAE,WAAW,EAAE,aAAa,EAAC,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAExD,aAAa,EAAA,UAAA,EAAA,CAAA;kBAJzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iBAAiB;oBAC3B,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,eAAe,EAAE,WAAW,EAAe,aAAA,EAAC,CAAC;AACpE,iBAAA;;;ACVD;;;;AAIG;MACU,aAAa,GAAG,IAAI,cAAc,CAAc,aAAa;AAE1E;;;AAGG;MACU,OAAO,GAAG,IAAI,cAAc,CAAM,SAAS;AAExD;AAKM,MAAO,WAAY,SAAQ,SAAS,CAAA;IACxC,WAAW,GAAG,MAAM,CAAC,OAAO,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;uGADpC,WAAW,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAX,WAAW,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gCAAA,EAAA,SAAA,EAFX,CAAC,EAAC,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,WAAW,EAAC,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAEpD,WAAW,EAAA,UAAA,EAAA,CAAA;kBAJvB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gCAAgC;oBAC1C,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,aAAa,EAAE,WAAW,EAAa,WAAA,EAAC,CAAC;AAChE,iBAAA;;;ACID;;;AAGG;MACU,aAAa,GAAG,IAAI,cAAc,CAAM,eAAe;MAsBvD,MAAM,CAAA;AACT,IAAA,iBAAiB,GAAG,MAAM,CAAC,gBAAgB,CAAC;IACpD,gBAAgB,GAAG,MAAM,CAAC,aAAa,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;;IAI1D,QAAQ,GAAY,KAAK;;AAGzB,IAAA,IACI,aAAa,GAAA;QACf,OAAO,IAAI,CAAC,cAAc;;IAE5B,IAAI,aAAa,CAAC,KAAkB,EAAA;AAClC,QAAA,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC;;AAE5B,IAAA,cAAc;AAEtB;;AAEG;IAGK,gBAAgB,GAAqB,SAAU;;AAGf,IAAA,gBAAgB;;IAGxC,SAAS,GAAW,EAAE;;AAGjB,IAAA,SAAS;AAE9B;;;AAGG;AACuB,IAAA,cAAc;;AAG/B,IAAA,UAAU;;AAGV,IAAA,SAAS;AAElB;;;AAGG;IACM,EAAE,GAAkB,IAAI;;IAGzB,cAAc,GAA0B,IAAI;;AAGpD,IAAA,IAAI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,cAAc;;;AAInB,IAAA,aAAa,GAAG,IAAI,OAAO,EAAQ;AAE5C;;;AAGG;IACH,QAAQ,GAAkB,IAAI;;AAG9B;;;AAGG;IACH,MAAM,GAAkB,IAAI;AAE5B;;AAEG;IACH,QAAQ,GAAG,KAAK;AAGhB,IAAA,WAAA,GAAA;QACE,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC;;AAG9D,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,IAAI,OAAO,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE;AAC7E,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE;;;IAI7B,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;;IAG/B,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,CACtC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,EAC9C,IAAI,CAAC,iBAAiB,CACvB;;AAGH;;;;;AAKG;AACK,IAAA,sBAAsB,CAAC,KAA8B,EAAA;;;;;QAK3D,IAAI,KAAK,IAAI,KAAK,CAAC,WAAW,KAAK,IAAI,EAAE;AACvC,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK;;;uGAnHpB,MAAM,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAN,MAAM,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,SAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAKE,gBAAgB,CAfxB,EAAA,SAAA,EAAA,CAAA,OAAA,EAAA,WAAA,CAAA,EAAA,SAAA,EAAA,CAAA,YAAA,EAAA,WAAA,CAAA,EAAA,cAAA,EAAA,CAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,EAAA,UAAA,EAAA,EAAA,SAAA,EAAA,MAAA,EAAA,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,EAAC,CAAC,EAmBtC,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,WAAW,EAYX,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,aAAa,2BAAS,WAAW,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAKpC,WAAW,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECpFxB,+QAIA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FDsDa,MAAM,EAAA,UAAA,EAAA,CAAA;kBApBlB,SAAS;+BACE,SAAS,EAAA,eAAA,EAMF,uBAAuB,CAAC,OAAO,iBACjC,iBAAiB,CAAC,IAAI,EAC3B,QAAA,EAAA,QAAQ,aACP,CAAC,EAAC,OAAO,EAAE,OAAO,EAAE,WAAW,EAAA,MAAQ,EAAC,CAAC,EAC9C,IAAA,EAAA;;;AAGJ,wBAAA,QAAQ,EAAE,EAAE;;AAGZ,wBAAA,WAAW,EAAE,MAAM;AACpB,qBAAA,EAAA,QAAA,EAAA,+QAAA,EAAA;wDAQD,QAAQ,EAAA,CAAA;sBADP,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAKhC,aAAa,EAAA,CAAA;sBADhB,YAAY;uBAAC,WAAW;gBAcjB,gBAAgB,EAAA,CAAA;sBAFvB,YAAY;uBAAC,aAAa,EAAE,EAAC,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAC;gBAKtB,gBAAgB,EAAA,CAAA;sBAAvD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,WAAW,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC;gBAGtB,SAAS,EAAA,CAAA;sBAAxB,KAAK;uBAAC,OAAO;gBAGO,SAAS,EAAA,CAAA;sBAA7B,KAAK;uBAAC,YAAY;gBAMO,cAAc,EAAA,CAAA;sBAAvC,KAAK;uBAAC,iBAAiB;gBAGf,UAAU,EAAA,CAAA;sBAAlB;gBAGQ,SAAS,EAAA,CAAA;sBAAjB;gBAMQ,EAAE,EAAA,CAAA;sBAAV;;;AE7EH;AACA,MAAM,YAAY,GAAG,2BAA2B;AAEhD;AACA,MAAM,mBAAmB,GAAG,kCAAkC;AAE9D;;;AAGG;MACU,SAAS,CAAA;AAIA,IAAA,MAAA;;AAFZ,IAAA,YAAY;AAEpB,IAAA,WAAA,CAAoB,MAAgC,EAAA;QAAhC,IAAM,CAAA,MAAA,GAAN,MAAM;;;IAG1B,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACpD,QAAA,IAAI,CAAC,YAAY,GAAG,SAAS;;;AAI/B,IAAA,cAAc,CAAC,OAAoB,EAAA;QACjC,MAAM,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,KAAK,OAAO,CAAC;AAC7F,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY;AAErC,QAAA,IAAI,iBAAiB,KAAK,WAAW,EAAE;YACrC;;QAGF,WAAW,EAAE,gBAAgB,EAAE;QAE/B,IAAI,iBAAiB,EAAE;YACrB,MAAM,OAAO,GAAG,WAAW,EAAE,UAAU,CAAC,aAAa,CAAC,qBAAqB,IAAI;;AAG/E,YAAA,iBAAiB,CAAC,cAAc,CAAC,OAAO,CAAC;AACzC,YAAA,IAAI,CAAC,YAAY,GAAG,iBAAiB;;;AAG1C;MAGqB,UAAU,CAAA;AACtB,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AACzD,IAAA,cAAc;AACd,IAAA,qBAAqB;IACrB,aAAa,GAAG,KAAK;;AAG7B,IAAA,IACI,kBAAkB,GAAA;QACpB,OAAO,IAAI,CAAC,aAAa;;IAE3B,IAAI,kBAAkB,CAAC,QAAiB,EAAA;AACtC,QAAA,IAAI,IAAI,CAAC,aAAa,KAAK,QAAQ,EAAE;AACnC,YAAA,IAAI,CAAC,aAAa,GAAG,QAAQ;AAE7B,YAAA,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvB,IAAI,CAAC,oBAAoB,EAAE;;;;;AAMjC,IAAA,cAAc,CAAC,2BAAqC,EAAA;AAClD,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;;;AAI9C,QAAA,IACE,CAAC,2BAA2B;YAC5B,CAAC,OAAO,CAAC,qBAAqB;AAC9B,YAAA,CAAC,IAAI,CAAC,qBAAqB,EAC3B;AACA,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC;YACnC;;;;;AAOF,QAAA,MAAM,iBAAiB,GAAG,OAAO,CAAC,qBAAqB,EAAE;QACzD,MAAM,UAAU,GAAG,2BAA2B,CAAC,KAAK,GAAG,iBAAiB,CAAC,KAAK;QAC9E,MAAM,SAAS,GAAG,2BAA2B,CAAC,IAAI,GAAG,iBAAiB,CAAC,IAAI;AAC3E,QAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,mBAAmB,CAAC;AAC1C,QAAA,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,WAAW,CAC1C,WAAW,EACX,cAAc,SAAS,CAAA,WAAA,EAAc,UAAU,CAAA,CAAA,CAAG,CACnD;;QAGD,OAAO,CAAC,qBAAqB,EAAE;AAE/B,QAAA,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,mBAAmB,CAAC;AAC7C,QAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC;QACnC,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,CAAC;;;IAI/D,gBAAgB,GAAA;QACd,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC;;;IAI/D,QAAQ,GAAA;QACN,IAAI,CAAC,oBAAoB,EAAE;;;IAI7B,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,cAAc,EAAE,MAAM,EAAE;QAC7B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,qBAAqB,GAAG,IAAK;;;IAIlD,oBAAoB,GAAA;QAC1B,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,aAAa,IAAI,QAAQ;AAC7E,QAAA,MAAM,aAAa,IAAI,IAAI,CAAC,cAAc,GAAG,YAAY,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AAChF,QAAA,MAAM,oBAAoB,IAAI,IAAI,CAAC,qBAAqB,GAAG,YAAY,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AAE9F,QAAA,aAAa,CAAC,SAAS,GAAG,mBAAmB;AAC7C,QAAA,oBAAoB,CAAC,SAAS;AAC5B,YAAA,kEAAkE;AAEpE,QAAA,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC;QACrD,IAAI,CAAC,oBAAoB,EAAE;;AAG7B;;;AAGG;IACK,oBAAoB,GAAA;AAC1B,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAC3E,YAAA,MAAM,KAAK,CAAC,6DAA6D,CAAC;;AAG5E,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC;cACvB,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,aAAa,CAAC,mBAAmB;AAClE,cAAE,IAAI,CAAC,WAAW,CAAC,aAAa;AAElC,QAAA,IAAI,CAAC,aAAa,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AACrE,YAAA,MAAM,KAAK,CAAC,qCAAqC,CAAC;;AAGpD,QAAA,aAAc,CAAC,WAAW,CAAC,IAAI,CAAC,cAAe,CAAC;;uGAxG9B,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAV,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAU,iGAOX,gBAAgB,CAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAPf,UAAU,EAAA,UAAA,EAAA,CAAA;kBAD/B;8BASK,kBAAkB,EAAA,CAAA;sBADrB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;;AA6GtC;;;;;AAKG;SACa,+BAA+B,GAAA;AAC7C,IAAA,MAAM,MAAM,GAAG,CAAC,OAAoB,MAAM;AACxC,QAAA,IAAI,EAAE,OAAO,GAAG,CAAC,OAAO,CAAC,UAAU,IAAI,CAAC,IAAI,IAAI,GAAG,GAAG;AACtD,QAAA,KAAK,EAAE,OAAO,GAAG,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,GAAG,GAAG;AACzD,KAAA,CAAC;AAEF,IAAA,OAAO,MAAM;AACf;AAEA;MACa,uBAAuB,GAAG,IAAI,cAAc,CACvD,qBAAqB,EACrB;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,+BAA+B;AACzC,CAAA;;ACzMH;;;AAGG;AAQG,MAAO,kBAAmB,SAAQ,UAAU,CAAA;AAChD,IAAA,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;;IAI/B,QAAQ,GAAY,KAAK;;IAGzB,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,EAAE;;IAGvC,aAAa,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,UAAU;;IAGjD,cAAc,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,WAAW;;uGAjBvC,kBAAkB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAlB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,kBAAkB,qGAIV,gBAAgB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,4BAAA,EAAA,UAAA,EAAA,oBAAA,EAAA,YAAA,EAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAJxB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAP9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sBAAsB;AAChC,oBAAA,IAAI,EAAE;AACJ,wBAAA,8BAA8B,EAAE,UAAU;AAC1C,wBAAA,sBAAsB,EAAE,YAAY;AACrC,qBAAA;AACF,iBAAA;8BAMC,QAAQ,EAAA,CAAA;sBADP,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;;;ACYtC;AACA,MAAM,2BAA2B,GAAG;AAClC,IAAA,OAAO,EAAE,IAAI;CACd;AASD;;;AAGG;AACH,MAAM,mBAAmB,GAAG,GAAG;AAE/B;;;AAGG;AACH,MAAM,sBAAsB,GAAG,GAAG;AAKlC;;;AAGG;MAEmB,qBAAqB,CAAA;AAG/B,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AACzD,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAChD,IAAA,cAAc,GAAG,MAAM,CAAC,aAAa,CAAC;IACtC,IAAI,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAC/C,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,qBAAqB,GAAG,MAAM,CAAC,oBAAoB,CAAC;AACpD,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;IACrC,cAAc,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AACxD,IAAA,cAAc;;IAWd,eAAe,GAAG,CAAC;;IAGnB,qBAAqB,GAAG,KAAK;;AAGlB,IAAA,UAAU,GAAG,IAAI,OAAO,EAAQ;;IAGnD,uBAAuB,GAAG,KAAK;;IAG/B,mBAAmB,GAAG,IAAI;;IAG1B,oBAAoB,GAAG,IAAI;AAE3B;;;AAGG;AACK,IAAA,cAAc;;AAGd,IAAA,sBAAsB;;AAGpB,IAAA,WAAW;;AAGb,IAAA,mBAAmB;;AAGnB,IAAA,cAAc,GAAG,IAAI,OAAO,EAAQ;AAE5C;;;AAGG;IAEH,iBAAiB,GAAY,KAAK;;AAGlC,IAAA,IACI,aAAa,GAAA;QACf,OAAO,IAAI,CAAC,cAAc;;IAE5B,IAAI,aAAa,CAAC,CAAS,EAAA;AACzB,QAAA,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;AAE9B,QAAA,IAAI,IAAI,CAAC,cAAc,IAAI,KAAK,EAAE;AAChC,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI;AACjC,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK;AAE3B,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,gBAAA,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,KAAK,CAAC;;;;IAItC,cAAc,GAAW,CAAC;;AAGf,IAAA,kBAAkB,GAAyB,IAAI,YAAY,EAAU;;AAGrE,IAAA,YAAY,GAAyB,IAAI,YAAY,EAAU;AAIlF,IAAA,WAAA,GAAA;;QAEE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAM;YACzD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,YAAY,EAAE,MAClE,IAAI,CAAC,aAAa,EAAE,CACrB;AACF,SAAA,CAAC;;IAMJ,eAAe,GAAA;;QAGb,IAAI,CAAC,cAAc,CAAC,IAAI,CACtB,qBAAqB,CACnB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,kBAAkB,CAAC,aAAa,EACrC,YAAY,EACZ,MAAM,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAC1C,2BAA2B,CAC5B,EACD,qBAAqB,CACnB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,cAAc,CAAC,aAAa,EACjC,YAAY,EACZ,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,EACzC,2BAA2B,CAC5B,CACF;;IAGH,kBAAkB,GAAA;QAChB,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAGA,EAAY,CAAC,KAAK,CAAC;;;;;AAKpE,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC;AACjB,aAAA,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa;AACtC,aAAA,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;;;;QAIrD,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEvF,MAAM,OAAO,GAAG,MAAK;YACnB,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,yBAAyB,EAAE;AAClC,SAAC;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,eAAe,CAA4B,IAAI,CAAC,MAAM;AAC1E,aAAA,yBAAyB,CAAC,IAAI,CAAC,mBAAmB,EAAE;AACpD,aAAA,cAAc;AACd,aAAA,QAAQ;;AAER,aAAA,aAAa,CAAC,MAAM,KAAK,CAAC;;;AAI7B,QAAA,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;;;;QAKnE,eAAe,CAAC,OAAO,EAAE,EAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAC,CAAC;;;AAIpD,QAAA,KAAK,CAAC,SAAS,EAAE,cAAc,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE;AAC/E,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;aAC/B,SAAS,CAAC,MAAK;;;;AAId,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAK;AACpB,gBAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;;oBAE1B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,CAC7B,CAAC,EACD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAE,IAAI,CAAC,eAAe,CAAC,CAC7D;AACD,oBAAA,OAAO,EAAE;AACX,iBAAC,CAAC;AACJ,aAAC,CAAC;YACF,IAAI,CAAC,WAAW,EAAE,yBAAyB,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;AACzE,SAAC,CAAC;;;;QAKJ,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,aAAa,IAAG;AAChD,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC;AACrC,YAAA,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;AAClC,SAAC,CAAC;;;IAII,aAAa,GAAA;AACnB,QAAA,IAAI,OAAO,cAAc,KAAK,UAAU,EAAE;AACxC,YAAA,OAAO,KAAK;;AAGd,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAC7B,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,EACtB,SAAS,CACP,CAAC,QAA8C,KAC7C,IAAI,UAAU,CAAC,CAAC,QAAyC,KACvD,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,MAAM,cAAc,GAAG,IAAI,cAAc,CAAC,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC5E,YAAA,QAAQ,CAAC,OAAO,CAAC,IAAI,IAAI,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;AAC/E,YAAA,OAAO,MAAK;gBACV,cAAc,CAAC,UAAU,EAAE;AAC7B,aAAC;SACF,CAAC,CACH,CACJ;;;QAGD,IAAI,CAAC,CAAC,CAAC;;;AAGP,QAAA,MAAM,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAC1F;;IAGH,qBAAqB,GAAA;;QAEnB,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAC7C,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM;AACxC,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;;AAKxC,QAAA,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAC9B,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC;YACxC,IAAI,CAAC,uBAAuB,EAAE;YAC9B,IAAI,CAAC,yBAAyB,EAAE;AAChC,YAAA,IAAI,CAAC,qBAAqB,GAAG,KAAK;AAClC,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;;AAKxC,QAAA,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC/B,IAAI,CAAC,wBAAwB,EAAE;AAC/B,YAAA,IAAI,CAAC,sBAAsB,GAAG,KAAK;AACnC,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;IAI1C,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,IAAI,OAAO,EAAE,CAAC;AACjD,QAAA,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE;AAC3B,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;AACtB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;AAC1B,QAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE;;;AAIhC,IAAA,cAAc,CAAC,KAAoB,EAAA;;AAEjC,QAAA,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE;YACzB;;AAGF,QAAA,QAAQ,KAAK,CAAC,OAAO;AACnB,YAAA,KAAK,KAAK;AACV,YAAA,KAAK,KAAK;gBACR,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,aAAa,EAAE;AAC1C,oBAAA,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAE7C,oBAAA,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;wBAC1B,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;AAC7C,wBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;;;gBAG7B;AACF,YAAA;AACE,gBAAA,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC,KAAK,CAAC;;;AAIxC;;AAEG;IACH,iBAAiB,GAAA;QACf,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,WAAW;;;;AAK9D,QAAA,IAAI,WAAW,KAAK,IAAI,CAAC,mBAAmB,EAAE;AAC5C,YAAA,IAAI,CAAC,mBAAmB,GAAG,WAAW,IAAI,EAAE;;;AAI5C,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAK;gBACpB,IAAI,CAAC,gBAAgB,EAAE;gBACvB,IAAI,CAAC,yBAAyB,EAAE;AAChC,gBAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;AACxC,aAAC,CAAC;;;AAIN;;;;;;AAMG;IACH,gBAAgB,GAAA;QACd,IAAI,CAAC,uBAAuB,EAAE;QAC9B,IAAI,CAAC,uBAAuB,EAAE;QAC9B,IAAI,CAAC,wBAAwB,EAAE;;;AAIjC,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,eAAgB,GAAG,CAAC;;;IAIjE,IAAI,UAAU,CAAC,KAAa,EAAA;AAC1B,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YAChF;;AAGF,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC;;AAGvC;;;AAGG;AACH,IAAA,aAAa,CAAC,KAAa,EAAA;QACzB,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI;;AAG5D;;;AAGG;AACH,IAAA,YAAY,CAAC,QAAgB,EAAA;AAC3B,QAAA,IAAI,IAAI,CAAC,uBAAuB,EAAE;AAChC,YAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;;QAG/B,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YACrC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE;;;;AAKvC,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa;AACxD,YAAA,MAAM,GAAG,GAAG,IAAI,CAAC,mBAAmB,EAAE;AAEtC,YAAA,IAAI,GAAG,IAAI,KAAK,EAAE;AAChB,gBAAA,WAAW,CAAC,UAAU,GAAG,CAAC;;iBACrB;gBACL,WAAW,CAAC,UAAU,GAAG,WAAW,CAAC,WAAW,GAAG,WAAW,CAAC,WAAW;;;;;IAMhF,mBAAmB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;;;IAI/D,wBAAwB,GAAA;AACtB,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B;;AAGF,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc;AAC1C,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,EAAE,KAAK,KAAK,GAAG,CAAC,cAAc,GAAG,cAAc;;;;;;;AAQ1F,QAAA,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG,CAAA,WAAA,EAAc,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK;;;;;AAMvF,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;YACjD,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,UAAU,GAAG,CAAC;;;;AAKvD,IAAA,IAAI,cAAc,GAAA;QAChB,OAAO,IAAI,CAAC,eAAe;;IAE7B,IAAI,cAAc,CAAC,KAAa,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;;AAGvB;;;;;;;AAOG;AACH,IAAA,aAAa,CAAC,SAA0B,EAAA;QACtC,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,WAAW;;QAGnE,MAAM,YAAY,GAAG,CAAC,CAAC,SAAS,IAAI,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,UAAU,IAAI,CAAC;QAExE,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,GAAG,YAAY,CAAC;;;AAI5D,IAAA,qBAAqB,CAAC,SAA0B,EAAA;QAC9C,IAAI,CAAC,aAAa,EAAE;AACpB,QAAA,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;;AAG/B;;;;;AAKG;AACH,IAAA,cAAc,CAAC,UAAkB,EAAA;AAC/B,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B;;QAGF,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC,GAAG,IAAI;QAE5E,IAAI,CAAC,aAAa,EAAE;YAClB;;;QAIF,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,WAAW;QACnE,MAAM,EAAC,UAAU,EAAE,WAAW,EAAC,GAAG,aAAa,CAAC,UAAU,CAAC,aAAa;QAExE,IAAI,cAAsB,EAAE,aAAqB;AACjD,QAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE,IAAI,KAAK,EAAE;YACvC,cAAc,GAAG,UAAU;AAC3B,YAAA,aAAa,GAAG,cAAc,GAAG,WAAW;;aACvC;YACL,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,WAAW,GAAG,UAAU;AACzE,YAAA,cAAc,GAAG,aAAa,GAAG,WAAW;;AAG9C,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,cAAc;AAC5C,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,GAAG,UAAU;AAExD,QAAA,IAAI,cAAc,GAAG,gBAAgB,EAAE;;AAErC,YAAA,IAAI,CAAC,cAAc,IAAI,gBAAgB,GAAG,cAAc;;AACnD,aAAA,IAAI,aAAa,GAAG,eAAe,EAAE;;AAE1C,YAAA,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,GAAG,CAC7B,aAAa,GAAG,eAAe,EAC/B,cAAc,GAAG,gBAAgB,CAClC;;;AAIL;;;;;;;AAOG;IACH,uBAAuB,GAAA;AACrB,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAC1B,YAAA,IAAI,CAAC,uBAAuB,GAAG,KAAK;;aAC/B;YACL,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,WAAW;YAChE,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,WAAW;;;;;;;;;AAUjE,YAAA,MAAM,SAAS,GAAG,WAAW,GAAG,cAAc,IAAI,CAAC;YAEnD,IAAI,CAAC,SAAS,EAAE;AACd,gBAAA,IAAI,CAAC,cAAc,GAAG,CAAC;;AAGzB,YAAA,IAAI,SAAS,KAAK,IAAI,CAAC,uBAAuB,EAAE;AAC9C,gBAAA,IAAI,CAAC,uBAAuB,GAAG,SAAS;AACxC,gBAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;;AAK5C;;;;;;;;AAQG;IACH,uBAAuB,GAAA;AACrB,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,GAAG,IAAI;;aACtD;;YAEL,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,cAAc,IAAI,CAAC;YACpD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAC9E,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;AAI1C;;;;;;AAMG;IACH,qBAAqB,GAAA;QACnB,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,WAAW;QACpE,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,WAAW;AACnE,QAAA,OAAO,eAAe,GAAG,UAAU,IAAI,CAAC;;;IAI1C,yBAAyB,GAAA;AACvB,QAAA,MAAM,YAAY,GAChB,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,IAAI;AACtF,QAAA,MAAM,oBAAoB,GAAG,YAAY,GAAG,YAAY,CAAC,UAAU,CAAC,aAAa,GAAG,IAAI;QAExF,IAAI,oBAAoB,EAAE;AACxB,YAAA,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,oBAAoB,CAAC;;aAC5C;AACL,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;;;;IAKvB,aAAa,GAAA;AACX,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE;;AAG5B;;;;AAIG;IACH,qBAAqB,CAAC,SAA0B,EAAE,UAAuB,EAAA;;;AAGvE,QAAA,IAAI,UAAU,IAAI,UAAU,CAAC,MAAM,IAAI,IAAI,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YACtE;;;QAIF,IAAI,CAAC,aAAa,EAAE;;AAGpB,QAAA,KAAK,CAAC,mBAAmB,EAAE,sBAAsB;;AAE9C,aAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;aAC3D,SAAS,CAAC,MAAK;AACd,YAAA,MAAM,EAAC,iBAAiB,EAAE,QAAQ,EAAC,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;;YAGnE,IAAI,QAAQ,KAAK,CAAC,IAAI,QAAQ,IAAI,iBAAiB,EAAE;gBACnD,IAAI,CAAC,aAAa,EAAE;;AAExB,SAAC,CAAC;;AAGN;;;;AAIG;AACK,IAAA,SAAS,CAAC,QAAgB,EAAA;AAChC,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,OAAO,EAAC,iBAAiB,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAC;;AAG5C,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,EAAE;AACtD,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;;;AAIzE,QAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI;QAClC,IAAI,CAAC,uBAAuB,EAAE;QAE9B,OAAO,EAAC,iBAAiB,EAAE,QAAQ,EAAE,IAAI,CAAC,eAAe,EAAC;;uGA3lBxC,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAArB,qBAAqB,EAAA,YAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,iBAAA,EAAA,CAAA,mBAAA,EAAA,mBAAA,EA+DtB,gBAAgB,CAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAIhB,eAAe,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAnEd,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAD1C;wDAiEC,iBAAiB,EAAA,CAAA;sBADhB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAKhC,aAAa,EAAA,CAAA;sBADhB,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC;gBAmBhB,kBAAkB,EAAA,CAAA;sBAApC;gBAGkB,YAAY,EAAA,CAAA;sBAA9B;;;AClIH;;;;;;AAMG;AAeG,MAAO,YACX,SAAQ,qBAAqB,CAAA;AAG8B,IAAA,MAAM;AAClB,IAAA,iBAAiB;AAC1B,IAAA,QAAQ;AACH,IAAA,aAAa;AAC5B,IAAA,cAAc;AACV,IAAA,kBAAkB;AAClD,IAAA,OAAO;;AAGc,IAAA,SAAS;;AAGJ,IAAA,cAAc;;IAIxC,aAAa,GAAY,KAAK;IAErB,kBAAkB,GAAA;QACzB,IAAI,CAAC,OAAO,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC;QACzC,KAAK,CAAC,kBAAkB,EAAE;;AAGlB,IAAA,aAAa,CAAC,KAAoB,EAAA;QAC1C,KAAK,CAAC,cAAc,EAAE;;uGA5Bb,YAAY,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAZ,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAAA,CAAA,YAAA,EAAA,WAAA,CAAA,EAAA,cAAA,EAAA,CAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAmBJ,gBAAgB,CAflB,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,sDAAA,EAAA,yBAAA,EAAA,8BAAA,EAAA,gCAAA,EAAA,EAAA,cAAA,EAAA,oBAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,QAAA,EAAA,SAAA,EAAA,kBAAkB,ulBCtDrC,6qDA2CA,EAAA,MAAA,EAAA,CAAA,w2FAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EDKY,SAAS,EAAA,QAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,iBAAiB,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,CAAA,2BAAA,EAAA,UAAA,CAAA,EAAA,OAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAE3B,YAAY,EAAA,UAAA,EAAA,CAAA;kBAdxB,SAAS;+BACE,gBAAgB,EAAA,aAAA,EAGX,iBAAiB,CAAC,IAAI,mBAEpB,uBAAuB,CAAC,OAAO,EAC1C,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,oBAAoB;AAC7B,wBAAA,wDAAwD,EAAE,yBAAyB;AACnF,wBAAA,gCAAgC,EAAE,gCAAgC;AACnE,qBAAA,EAAA,OAAA,EACQ,CAAC,SAAS,EAAE,iBAAiB,CAAC,EAAA,QAAA,EAAA,6qDAAA,EAAA,MAAA,EAAA,CAAA,w2FAAA,CAAA,EAAA;8BAMoB,MAAM,EAAA,CAAA;sBAAhE,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,kBAAkB,EAAE,EAAC,WAAW,EAAE,KAAK,EAAC;gBACV,iBAAiB,EAAA,CAAA;sBAA/D,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,kBAAkB,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC;gBACP,QAAQ,EAAA,CAAA;sBAA7C,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,SAAS,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC;gBACO,aAAa,EAAA,CAAA;sBAAvD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,cAAc,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC;gBACb,cAAc,EAAA,CAAA;sBAAzC,SAAS;uBAAC,eAAe;gBACM,kBAAkB,EAAA,CAAA;sBAAjD,SAAS;uBAAC,mBAAmB;gBAIT,SAAS,EAAA,CAAA;sBAA7B,KAAK;uBAAC,YAAY;gBAGO,cAAc,EAAA,CAAA;sBAAvC,KAAK;uBAAC,iBAAiB;gBAIxB,aAAa,EAAA,CAAA;sBADZ,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;;;AEvBtC;MACa,eAAe,GAAG,IAAI,cAAc,CAAgB,iBAAiB;;ACblF;;;AAGG;AAEG,MAAO,gBAAiB,SAAQ,eAAe,CAAA;AAC3C,IAAA,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC;;AAG1B,IAAA,aAAa,GAAG,YAAY,CAAC,KAAK;;AAElC,IAAA,WAAW,GAAG,YAAY,CAAC,KAAK;AAIxC,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;;;IAIA,QAAQ,GAAA;QACf,KAAK,CAAC,QAAQ,EAAE;AAEhB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC;aAC7B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;AAC9C,aAAA,SAAS,CAAC,CAAC,WAAoB,KAAI;AAClC,YAAA,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;gBAC7D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;;AAEpC,SAAC,CAAC;AAEJ,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,SAAS,CAAC,MAAK;AAC/D,YAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE;gBAC/B,IAAI,CAAC,MAAM,EAAE;;AAEjB,SAAC,CAAC;;;IAIK,WAAW,GAAA;QAClB,KAAK,CAAC,WAAW,EAAE;AACnB,QAAA,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE;AAChC,QAAA,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE;;uGArCrB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAhB,gBAAgB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAhB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAD5B,SAAS;mBAAC,EAAC,QAAQ,EAAE,kBAAkB,EAAC;;AAoEzC;;;AAGG;MAkBU,UAAU,CAAA;AACb,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;IACzD,IAAI,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAC/C,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;IAC7B,iBAAiB,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AACnE,IAAA,cAAc;AACd,IAAA,YAAY;AACZ,IAAA,cAAc;;AAGd,IAAA,cAAc;;AAGd,IAAA,sBAAsB,GAAG,YAAY,CAAC,KAAK;;AAGnD,IAAA,SAAS;;AAGC,IAAA,iBAAiB;;AAGR,IAAA,YAAY,GAAyB,IAAI,YAAY,EAAU;;AAG/D,IAAA,gBAAgB,GAA0B,IAAI,YAAY,EAAW;;AAG/E,IAAA,mBAAmB,GAAuB,IAAI,YAAY,EAAQ;;AAGxD,IAAA,WAAW,GAAuB,IAAI,YAAY,CAAO,IAAI,CAAC;;AAGpD,IAAA,WAAW;;AAGlB,IAAA,eAAe;;AAGnB,IAAA,QAAQ;;;;IAKjB,iBAAiB,GAAW,OAAO;;IAGnC,eAAe,GAAY,KAAK;;IAGzC,IACI,QAAQ,CAAC,QAAgB,EAAA;AAC3B,QAAA,IAAI,CAAC,cAAc,GAAG,QAAQ;QAC9B,IAAI,CAAC,8BAA8B,EAAE;;AAKvC,IAAA,WAAA,GAAA;AACE,QAAA,IAAI,IAAI,CAAC,IAAI,EAAE;AACb,YAAA,MAAM,iBAAiB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AACnD,YAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,GAAc,KAAI;AAC1E,gBAAA,IAAI,CAAC,8BAA8B,CAAC,GAAG,CAAC;gBACxC,iBAAiB,CAAC,YAAY,EAAE;AAClC,aAAC,CAAC;;;IAIN,QAAQ,GAAA;QACN,IAAI,CAAC,qBAAqB,EAAE;AAE5B,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,EAAE;AAC/B,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;;AAG1B,YAAA,eAAe,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,EAAE;gBACzF,QAAQ,EAAE,IAAI,CAAC,SAAS;AACzB,aAAA,CAAC;;AAGJ,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI;;IAG1B,WAAW,GAAA;AACT,QAAA,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC;AACjC,QAAA,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,OAAO,IAAI,OAAO,EAAE,CAAC;AAClD,QAAA,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE;;;IAInC,qBAAqB,GAAA;AAC3B,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;AAC9C,YAAA,MAAM,cAAc,GAAG,CAAC,KAAsB,KAAI;gBAChD,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,eAAe,EAAE,aAAa,EAAE;oBACxD,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,wBAAwB,CAAC;;;AAIzE,oBAAA,IAAI,KAAK,CAAC,IAAI,KAAK,eAAe,EAAE;wBAClC,IAAI,CAAC,eAAe,EAAE;;;AAG5B,aAAC;YAED,IAAI,CAAC,cAAc,GAAG;AACpB,gBAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,EAAE,iBAAiB,EAAE,CAAC,KAAsB,KAAI;oBAC3E,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,eAAe,EAAE,aAAa,EAAE;wBACxD,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,wBAAwB,CAAC;wBACtE,IAAI,CAAC,kBAAkB,EAAE;;AAE7B,iBAAC,CAAC;gBACF,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,EAAE,eAAe,EAAE,cAAc,CAAC;gBAC/D,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,EAAE,kBAAkB,EAAE,cAAc,CAAC;aACnE;AACH,SAAC,CAAC;;;IAII,kBAAkB,GAAA;AACxB,QAAA,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC;AACjC,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,KAAK,QAAQ;AAC/C,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC;QACvC,IAAI,WAAW,EAAE;AACf,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC;;;;IAK/D,eAAe,GAAA;AACrB,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,EAAE;AAC/B,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;;AAClB,aAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,QAAQ,EAAE;AAC9C,YAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE;;;;AAKnC,IAAA,eAAe,CAAC,QAAiB,EAAA;AAC/B,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,yBAAyB,EAAE,QAAQ,CAAC;;;IAItF,mBAAmB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;;;IAI/D,iBAAiB,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,cAAc,KAAK,CAAC;;;AAI1B,IAAA,8BAA8B,CAAC,GAAA,GAAiB,IAAI,CAAC,mBAAmB,EAAE,EAAA;AAChF,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,SAAS;AAEvC,QAAA,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE;AAC3B,YAAA,IAAI,CAAC,SAAS,GAAG,GAAG,IAAI,KAAK,GAAG,MAAM,GAAG,OAAO;;AAC3C,aAAA,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE;AAClC,YAAA,IAAI,CAAC,SAAS,GAAG,GAAG,IAAI,KAAK,GAAG,OAAO,GAAG,MAAM;;aAC3C;AACL,YAAA,IAAI,CAAC,SAAS,GAAG,QAAQ;;AAG3B,QAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE;YAC9B,IAAI,CAAC,yBAAyB,EAAE;;aAC3B,IACL,IAAI,CAAC,YAAY;AACjB,aAAC,IAAI,CAAC,SAAS,KAAK,QAAQ,IAAI,IAAI,CAAC,iBAAiB,KAAK,QAAQ,CAAC,EACpE;;;;AAIA,YAAA,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC;YACjC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MACnD,UAAU,CAAC,MAAM,IAAI,CAAC,yBAAyB,EAAE,EAAE,GAAG,CAAC,CACxD;;;;IAKG,yBAAyB,GAAA;QAC/B,IAAI,CAAC,kBAAkB,EAAE;AACzB,QAAA,eAAe,CAAC,MAAM,IAAI,CAAC,eAAe,EAAE,EAAE,EAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAC,CAAC;;;IAInE,mBAAmB,GAAA;AACzB,QAAA,QACE,IAAI,CAAC,iBAAiB,KAAK,gBAAgB;YAC3C,IAAI,CAAC,iBAAiB,KAAK,KAAK;AAChC,YAAA,IAAI,CAAC,iBAAiB,KAAK,IAAI;;uGAlMxB,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAV,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAU,yeAoCV,gBAAgB,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECnK7B,+WASA,ED8Ba,MAAA,EAAA,CAAA,u9BAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,gBAAgB,6DAsFC,aAAa,EAAA,QAAA,EAAA,mCAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAE9B,UAAU,EAAA,UAAA,EAAA,CAAA;kBAjBtB,SAAS;+BACE,cAAc,EAAA,aAAA,EAGT,iBAAiB,CAAC,IAAI,mBAEpB,uBAAuB,CAAC,OAAO,EAC1C,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,kBAAkB;;;;;AAK3B,wBAAA,cAAc,EAAE,oCAAoC;AACrD,qBAAA,EAAA,OAAA,EACQ,CAAC,gBAAgB,EAAE,aAAa,CAAC,EAAA,QAAA,EAAA,+WAAA,EAAA,MAAA,EAAA,CAAA,u9BAAA,CAAA,EAAA;wDA0BvB,YAAY,EAAA,CAAA;sBAA9B;gBAGkB,gBAAgB,EAAA,CAAA;sBAAlC;gBAMkB,WAAW,EAAA,CAAA;sBAA7B;gBAG4B,WAAW,EAAA,CAAA;sBAAvC,SAAS;uBAAC,gBAAgB;gBAGL,eAAe,EAAA,CAAA;sBAApC,SAAS;uBAAC,SAAS;gBAGF,QAAQ,EAAA,CAAA;sBAAzB,KAAK;uBAAC,SAAS;gBAKP,iBAAiB,EAAA,CAAA;sBAAzB;gBAGQ,eAAe,EAAA,CAAA;sBAAvB;gBAIG,QAAQ,EAAA,CAAA;sBADX;;;AE5HH;;;;AAIG;MAiCU,WAAW,CAAA;AAGb,IAAA,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC;AACjC,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAC9C,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,IAAA,iBAAiB,GAAG,YAAY,CAAC,KAAK;AACtC,IAAA,qBAAqB,GAAG,YAAY,CAAC,KAAK;AAC1C,IAAA,oBAAoB,GAAG,YAAY,CAAC,KAAK;AACzC,IAAA,qBAAqB,GAC3B,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,KAAK,gBAAgB;AAEtE;;;AAGG;AAC2C,IAAA,QAAQ;AAC5B,IAAA,UAAU;AACP,IAAA,eAAe;AACpB,IAAA,UAAU;;AAGlC,IAAA,KAAK,GAAsB,IAAI,SAAS,EAAU;;IAG1C,cAAc,GAAkB,CAAC;;IAGjC,oBAAoB,GAAkB,IAAI;;IAG1C,qBAAqB,GAAW,CAAC;AAEzC;;;;;;AAMG;AAEH,IAAA,KAAK;;AAGL,IAAA,IACI,kBAAkB,GAAA;QACpB,OAAO,IAAI,CAAC,mBAAmB;;IAEjC,IAAI,kBAAkB,CAAC,KAAc,EAAA;AACnC,QAAA,IAAI,CAAC,mBAAmB,GAAG,KAAK;AAChC,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;IAEhC,mBAAmB,GAAG,KAAK;;IAInC,WAAW,GAAY,IAAI;;IAI3B,SAAS,GAAkB,IAAI;;IAI/B,aAAa,GAAY,KAAK;;AAG9B,IAAA,IACI,aAAa,GAAA;QACf,OAAO,IAAI,CAAC,cAAc;;IAE5B,IAAI,aAAa,CAAC,KAAa,EAAA;AAC7B,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,KAAK;;IAE3C,cAAc,GAAkB,IAAI;;IAGnC,cAAc,GAAyB,OAAO;;AAGvD,IAAA,IACI,iBAAiB,GAAA;QACnB,OAAO,IAAI,CAAC,kBAAkB;;IAEhC,IAAI,iBAAiB,CAAC,KAAsB,EAAA;AAC1C,QAAA,MAAM,WAAW,GAAG,KAAK,GAAG,EAAE;AAC9B,QAAA,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,KAAK,GAAG,IAAI,GAAG,WAAW;;AAE1E,IAAA,kBAAkB;AAE1B;;;;;AAKG;AACH,IAAA,IACI,eAAe,GAAA;QACjB,OAAO,IAAI,CAAC,gBAAgB;;IAG9B,IAAI,eAAe,CAAC,KAAa,EAAA;AAC/B,QAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,KAAK;;AAG7C,IAAA,gBAAgB;AAExB;;;AAGG;IAEH,iBAAiB,GAAY,KAAK;;IAIlC,aAAa,GAAY,KAAK;AAE9B;;;;AAIG;IAEH,eAAe,GAAY,KAAK;AAEhC;;;;;;;;;AASG;AACH,IAAA,IACI,eAAe,GAAA;QACjB,OAAO,IAAI,CAAC,gBAAgB;;IAG9B,IAAI,eAAe,CAAC,KAAmB,EAAA;QAKrC,MAAM,SAAS,GAAiB,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS;QAExE,SAAS,CAAC,MAAM,CAAC,0BAA0B,EAAE,CAAkB,eAAA,EAAA,IAAI,CAAC,eAAe,CAAE,CAAA,CAAC;QAEtF,IAAI,KAAK,EAAE;YACT,SAAS,CAAC,GAAG,CAAC,0BAA0B,EAAE,CAAkB,eAAA,EAAA,KAAK,CAAE,CAAA,CAAC;;AAGtE,QAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK;;AAGvB,IAAA,gBAAgB;;AAGH,IAAA,SAAS;;AAGJ,IAAA,cAAc;;AAGrB,IAAA,mBAAmB,GAAyB,IAAI,YAAY,EAAU;;AAGtE,IAAA,WAAW,GAC5B,IAAI,YAAY,EAAqB;;AAGpB,IAAA,aAAa,GAAuB,IAAI,YAAY,EAAQ;;AAG5D,IAAA,iBAAiB,GAClC,IAAI,YAAY,CAAoB,IAAI,CAAC;AAEnC,IAAA,QAAQ;;IAGN,SAAS,GAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS;AAI1D,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,aAAa,GAAG,MAAM,CAAgB,eAAe,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAE9E,QAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC;AAC5D,QAAA,IAAI,CAAC,iBAAiB;AACpB,YAAA,aAAa,IAAI,aAAa,CAAC,iBAAiB,GAAG,aAAa,CAAC,iBAAiB,GAAG,OAAO;AAC9F,QAAA,IAAI,CAAC,iBAAiB;AACpB,YAAA,aAAa,IAAI,aAAa,CAAC,iBAAiB,IAAI;kBAChD,aAAa,CAAC;kBACd,KAAK;AACX,QAAA,IAAI,CAAC,aAAa;AAChB,YAAA,aAAa,IAAI,aAAa,CAAC,aAAa,IAAI,IAAI,GAAG,aAAa,CAAC,aAAa,GAAG,KAAK;AAC5F,QAAA,IAAI,aAAa,EAAE,eAAe,IAAI,IAAI,EAAE;AAC1C,YAAA,IAAI,CAAC,eAAe,GAAG,aAAa,CAAC,eAAe;;QAEtD,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,aAAa,EAAE,eAAe;AACvD,QAAA,IAAI,CAAC,kBAAkB;AACrB,YAAA,aAAa,IAAI,aAAa,CAAC,kBAAkB,IAAI;kBACjD,aAAa,CAAC;kBACd,KAAK;AACX,QAAA,IAAI,CAAC,WAAW;AACd,YAAA,aAAa,IAAI,aAAa,CAAC,WAAW,IAAI,IAAI,GAAG,aAAa,CAAC,WAAW,GAAG,IAAI;AACvF,QAAA,IAAI,CAAC,SAAS;AACZ,YAAA,aAAa,IAAI,aAAa,CAAC,SAAS,IAAI,IAAI,GAAG,aAAa,CAAC,SAAS,GAAG,IAAI;;AAGrF;;;;;AAKG;IACH,qBAAqB,GAAA;;;AAGnB,QAAA,MAAM,aAAa,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;;;AAItF,QAAA,IAAI,IAAI,CAAC,cAAc,IAAI,aAAa,EAAE;AACxC,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,IAAI,IAAI;YAE9C,IAAI,CAAC,UAAU,EAAE;AACf,gBAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;;;AAGnE,gBAAA,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa;gBAClD,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC,YAAY,GAAG,IAAI;;;;AAKvD,YAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;gBAC1B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,MAAM,GAAG,CAAC,QAAQ,GAAG,KAAK,KAAK,aAAa,CAAC,CAAC;gBAE5E,IAAI,CAAC,UAAU,EAAE;AACf,oBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,aAAa,CAAC;;;oBAG5C,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE;;AAE3D,aAAC,CAAC;;;QAIJ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAW,EAAE,KAAa,KAAI;AAChD,YAAA,GAAG,CAAC,QAAQ,GAAG,KAAK,GAAG,aAAa;;;AAIpC,YAAA,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,IAAI,GAAG,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;gBACnE,GAAG,CAAC,MAAM,GAAG,aAAa,GAAG,IAAI,CAAC,cAAc;;AAEpD,SAAC,CAAC;AAEF,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,aAAa,EAAE;AACzC,YAAA,IAAI,CAAC,cAAc,GAAG,aAAa;AACnC,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI;AAChC,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;IAI1C,kBAAkB,GAAA;QAChB,IAAI,CAAC,yBAAyB,EAAE;QAChC,IAAI,CAAC,qBAAqB,EAAE;;;AAI5B,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,MAAK;YACzD,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC;;;AAI9D,YAAA,IAAI,aAAa,KAAK,IAAI,CAAC,cAAc,EAAE;gBACzC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;AACjC,gBAAA,IAAI,WAA+B;AAEnC,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACpC,oBAAA,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;;;;wBAIpB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,GAAG,CAAC;AAC7C,wBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI;AAChC,wBAAA,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC;wBACrB;;;;;;gBAOJ,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,aAAa,CAAC,EAAE;AACvC,oBAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;AAC1B,wBAAA,IAAI,CAAC,aAAa,CAAC,CAAC,QAAQ,GAAG,IAAI;AACnC,wBAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;AACrE,qBAAC,CAAC;;;AAIN,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;AACxC,SAAC,CAAC;;IAGJ,eAAe,GAAA;QACb,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,UAAW,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;;;IAIxF,yBAAyB,GAAA;;;;QAI/B,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAuB,KAAI;YACzF,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,IAAI,CAAC,MAAM,CAAC,GAAG,IAAG;gBAChB,OAAO,GAAG,CAAC,gBAAgB,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,gBAAgB;aAC9D,CAAC,CACH;AACD,YAAA,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE;AAC9B,SAAC,CAAC;;IAGJ,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;AACpB,QAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE;AACpC,QAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE;AACxC,QAAA,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE;;;IAIzC,aAAa,GAAA;AACX,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,IAAI,CAAC,UAAU,CAAC,yBAAyB,EAAE;;;AAI/C;;;;;;AAMG;IACH,gBAAgB,GAAA;AACd,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE;;;AAItC;;;AAGG;AACH,IAAA,QAAQ,CAAC,KAAa,EAAA;AACpB,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU;QAE9B,IAAI,MAAM,EAAE;AACV,YAAA,MAAM,CAAC,UAAU,GAAG,KAAK;;;AAI7B,IAAA,aAAa,CAAC,KAAa,EAAA;AACzB,QAAA,IAAI,CAAC,oBAAoB,GAAG,KAAK;AACjC,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;;AAG/C,IAAA,kBAAkB,CAAC,KAAa,EAAA;AACtC,QAAA,MAAM,KAAK,GAAG,IAAI,iBAAiB,EAAE;AACrC,QAAA,KAAK,CAAC,KAAK,GAAG,KAAK;QACnB,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;AACnC,YAAA,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC;;AAEzC,QAAA,OAAO,KAAK;;AAGd;;;;;AAKG;IACK,qBAAqB,GAAA;AAC3B,QAAA,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAC9B,YAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE;;AAG1C,QAAA,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,MACxF,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CACvC;;;AAIK,IAAA,cAAc,CAAC,KAAoB,EAAA;;;;QAIzC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;;;IAIjE,cAAc,CAAC,GAAW,EAAE,KAAa,EAAA;QACvC,OAAO,GAAG,CAAC,EAAE,IAAI,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAA,OAAA,EAAU,KAAK,CAAA,CAAE;;;AAIpD,IAAA,gBAAgB,CAAC,KAAa,EAAA;AAC5B,QAAA,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAY,SAAA,EAAA,KAAK,EAAE;;AAG5C;;;AAGG;AACH,IAAA,wBAAwB,CAAC,SAAiB,EAAA;QACxC,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;AACtD,YAAA,IAAI,CAAC,qBAAqB,GAAG,SAAS;YACtC;;AAGF,QAAA,MAAM,OAAO,GAAgB,IAAI,CAAC,eAAe,CAAC,aAAa;QAE/D,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,qBAAqB,GAAG,IAAI;;;QAIxD,IAAI,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,YAAY,EAAE;YACnD,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,GAAG,IAAI;;;;IAK3C,2BAA2B,GAAA;AACzB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa;AAClD,QAAA,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,YAAY;AACjD,QAAA,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;AACzB,QAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;;;AAInD,IAAA,YAAY,CAAC,GAAW,EAAE,SAAgC,EAAE,KAAa,EAAA;AACvE,QAAA,SAAS,CAAC,UAAU,GAAG,KAAK;AAE5B,QAAA,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE;AACjB,YAAA,IAAI,CAAC,aAAa,GAAG,KAAK;;;;AAK9B,IAAA,YAAY,CAAC,KAAa,EAAA;QACxB,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,aAAa;AACnE,QAAA,OAAO,KAAK,KAAK,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;;;IAIvC,gBAAgB,CAAC,WAAwB,EAAE,KAAa,EAAA;;;;;QAKtD,IAAI,WAAW,IAAI,WAAW,KAAK,OAAO,IAAI,WAAW,KAAK,OAAO,EAAE;AACrE,YAAA,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,KAAK;;;AAItC;;;AAGG;AACO,IAAA,aAAa,CAAC,QAAiB,EAAA;;;;;;;;QAQvC,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,IAAI,CAAC,eAAe,CAAC,CAAC,KAAK,IAAI,CAAC,cAAc,CAAC,CAAC;;;IAIhF,mBAAmB,GAAA;QAC3B,QACE,IAAI,CAAC,qBAAqB;YAC1B,IAAI,CAAC,iBAAiB,KAAK,GAAG;AAC9B,YAAA,IAAI,CAAC,iBAAiB,KAAK,KAAK;;uGA5ezB,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAX,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,WAAW,4IA4CH,gBAAgB,CAAA,EAAA,WAAA,EAAA,CAAA,kBAAA,EAAA,aAAA,EAWW,gBAAgB,CAAA,EAAA,SAAA,EAAA,CAAA,gBAAA,EAAA,WAAA,CAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAQ3C,gBAAgB,CAIhB,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAAA,eAAe,CA6Bf,EAAA,cAAA,EAAA,gBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAAA,eAAe,iEAef,gBAAgB,CAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAIhB,gBAAgB,CAAA,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAQhB,gBAAgB,CAnJxB,EAAA,eAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,CAAA,YAAA,EAAA,WAAA,CAAA,EAAA,cAAA,EAAA,CAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,WAAA,EAAA,aAAA,EAAA,aAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,mCAAA,EAAA,wCAAA,EAAA,eAAA,EAAA,yCAAA,EAAA,8BAAA,EAAA,sCAAA,EAAA,aAAA,EAAA,qBAAA,EAAA,WAAA,EAAA,oCAAA,EAAA,mBAAA,EAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,aAAa;AACtB,gBAAA,WAAW,EAAE,WAAW;AACzB,aAAA;AACF,SAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,UAAA,EAAA,SAAA,EAmCgB,MAAM,EACT,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,YAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,YAAA,EAAA,SAAA,EAAA,UAAU,EC9G1B,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,aAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,2kHAqFA,6oODAI,YAAY,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,CAAA,YAAA,EAAA,iBAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EACZ,kBAAkB,EAAA,QAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,CAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAClB,eAAe,EACf,QAAA,EAAA,oDAAA,EAAA,OAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,SAAS,EACT,QAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,eAAe,iJACf,UAAU,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,UAAA,CAAA,EAAA,OAAA,EAAA,CAAA,cAAA,EAAA,kBAAA,EAAA,aAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAGD,WAAW,EAAA,UAAA,EAAA,CAAA;kBAhCvB,SAAS;+BACE,eAAe,EAAA,QAAA,EACf,aAAa,EAAA,aAAA,EAGR,iBAAiB,CAAC,IAAI,EAEpB,eAAA,EAAA,uBAAuB,CAAC,OAAO,EACrC,SAAA,EAAA;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,aAAa;AACtB,4BAAA,WAAW,EAAa,WAAA;AACzB,yBAAA;qBACF,EACK,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,mBAAmB;AAC5B,wBAAA,SAAS,EAAE,+BAA+B;AAC1C,wBAAA,0CAA0C,EAAE,eAAe;AAC3D,wBAAA,2CAA2C,EAAE,4BAA4B;AACzE,wBAAA,wCAAwC,EAAE,aAAa;AACvD,wBAAA,uBAAuB,EAAE,WAAW;AACpC,wBAAA,sCAAsC,EAAE,mBAAmB;qBAC5D,EACQ,OAAA,EAAA;wBACP,YAAY;wBACZ,kBAAkB;wBAClB,eAAe;wBACf,SAAS;wBACT,eAAe;wBACf,UAAU;AACX,qBAAA,EAAA,QAAA,EAAA,2kHAAA,EAAA,MAAA,EAAA,CAAA,qlOAAA,CAAA,EAAA;wDAkB6C,QAAQ,EAAA,CAAA;sBAArD,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,MAAM,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;gBAClB,UAAU,EAAA,CAAA;sBAAnC,YAAY;uBAAC,UAAU;gBACK,eAAe,EAAA,CAAA;sBAA3C,SAAS;uBAAC,gBAAgB;gBACH,UAAU,EAAA,CAAA;sBAAjC,SAAS;uBAAC,WAAW;gBAsBtB,KAAK,EAAA,CAAA;sBADJ;gBAKG,kBAAkB,EAAA,CAAA;sBADrB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAYpC,WAAW,EAAA,CAAA;sBADV,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,kBAAkB,EAAE,SAAS,EAAE,gBAAgB,EAAC;gBAK/D,SAAS,EAAA,CAAA;sBADR,KAAK;uBAAC,EAAC,KAAK,EAAE,gBAAgB,EAAC;gBAKhC,aAAa,EAAA,CAAA;sBADZ,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAKhC,aAAa,EAAA,CAAA;sBADhB,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC;gBAU1B,cAAc,EAAA,CAAA;sBAAtB;gBAIG,iBAAiB,EAAA,CAAA;sBADpB;gBAiBG,eAAe,EAAA,CAAA;sBADlB,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC;gBAgBnC,iBAAiB,EAAA,CAAA;sBADhB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAKpC,aAAa,EAAA,CAAA;sBADZ,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBASpC,eAAe,EAAA,CAAA;sBADd,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAchC,eAAe,EAAA,CAAA;sBADlB;gBAwBoB,SAAS,EAAA,CAAA;sBAA7B,KAAK;uBAAC,YAAY;gBAGO,cAAc,EAAA,CAAA;sBAAvC,KAAK;uBAAC,iBAAiB;gBAGL,mBAAmB,EAAA,CAAA;sBAArC;gBAGkB,WAAW,EAAA,CAAA;sBAA7B;gBAIkB,aAAa,EAAA,CAAA;sBAA/B;gBAGkB,iBAAiB,EAAA,CAAA;sBAAnC;;AAiUH;MACa,iBAAiB,CAAA;;AAE5B,IAAA,KAAK;;AAEL,IAAA,GAAG;AACJ;;AEhiBD;;;AAGG;AAuBG,MAAO,SAAU,SAAQ,qBAAqB,CAAA;AAClD,IAAA,YAAY,GAAG,MAAM,CAAmC,IAAI,CAAC;;AAG7D,IAAA,IACI,kBAAkB,GAAA;AACpB,QAAA,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK;;IAEvC,IAAI,kBAAkB,CAAC,KAAc,EAAA;AACnC,QAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC;AACpC,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;AAExC,IAAA,mBAAmB,GAAG,IAAI,eAAe,CAAC,KAAK,CAAC;;IAIhD,WAAW,GAAY,IAAI;AAE3B,IAAA,IACI,iBAAiB,GAAA;QACnB,OAAO,IAAI,CAAC,kBAAkB;;IAGhC,IAAI,iBAAiB,CAAC,KAAsB,EAAA;AAC1C,QAAA,MAAM,WAAW,GAAG,KAAK,GAAG,EAAE;AAC9B,QAAA,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,KAAK,GAAG,IAAI,GAAG,WAAW;;AAG1E,IAAA,kBAAkB;;AAG0C,IAAA,MAAM;AAE1E;;;;;;AAMG;AACH,IAAA,IACI,eAAe,GAAA;QACjB,OAAO,IAAI,CAAC,gBAAgB;;IAG9B,IAAI,eAAe,CAAC,KAAmB,EAAA;QACrC,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS;QAC1D,SAAS,CAAC,MAAM,CAAC,0BAA0B,EAAE,CAAkB,eAAA,EAAA,IAAI,CAAC,eAAe,CAAE,CAAA,CAAC;QAEtF,IAAI,KAAK,EAAE;YACT,SAAS,CAAC,GAAG,CAAC,0BAA0B,EAAE,CAAkB,eAAA,EAAA,KAAK,CAAE,CAAA,CAAC;;AAGtE,QAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK;;AAGvB,IAAA,gBAAgB;;IAIxB,aAAa,GAAY,KAAK;AAE9B;;;;;;AAMG;IACM,KAAK,GAAiB,SAAS;AAExC;;;;AAIG;AACM,IAAA,QAAQ;AAE8B,IAAA,iBAAiB;AAC1B,IAAA,QAAQ;AACH,IAAA,aAAa;AAC5B,IAAA,cAAc;AACV,IAAA,kBAAkB;AAClD,IAAA,OAAO;AAIP,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;AACrC,QAAA,MAAM,GAAG,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AACpD,QAAA,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AAC7B,QAAA,MAAM,iBAAiB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AACnD,QAAA,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;AAC3C,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;AACjC,QAAA,MAAM,aAAa,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AACrE,QAAA,MAAM,aAAa,GAAG,MAAM,CAAgB,eAAe,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAE9E,QAAA,KAAK,CAAC,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,CAAC;AACzF,QAAA,IAAI,CAAC,iBAAiB;AACpB,YAAA,aAAa,IAAI,aAAa,CAAC,iBAAiB,IAAI;kBAChD,aAAa,CAAC;kBACd,KAAK;AACX,QAAA,IAAI,CAAC,kBAAkB;AACrB,YAAA,aAAa,IAAI,aAAa,CAAC,kBAAkB,IAAI;kBACjD,aAAa,CAAC;kBACd,KAAK;AACX,QAAA,IAAI,CAAC,WAAW;AACd,YAAA,aAAa,IAAI,aAAa,CAAC,WAAW,IAAI,IAAI,GAAG,aAAa,CAAC,WAAW,GAAG,IAAI;;IAG/E,aAAa,GAAA;;;IAId,kBAAkB,GAAA;QACzB,IAAI,CAAC,OAAO,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC;;;QAGzC,IAAI,CAAC,MAAM,CAAC;AACT,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;aAChD,SAAS,CAAC,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE3C,KAAK,CAAC,kBAAkB,EAAE;;AAG1B,QAAA,IAAI,CAAC,WAAY,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,MACnF,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,IAAI,IAAI,CAAC,CAC5D;;IAGM,eAAe,GAAA;AACtB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AACrE,YAAA,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC;;QAE1E,KAAK,CAAC,eAAe,EAAE;;;IAIzB,gBAAgB,GAAA;AACd,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB;;QAGF,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;AAEnC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACrC,YAAA,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;AACnB,gBAAA,IAAI,CAAC,aAAa,GAAG,CAAC;AACtB,gBAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;oBACjB,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;;;;gBAI1C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC/B,gBAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;gBACtC;;;AAIJ,QAAA,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;;IAGzB,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,QAAQ,GAAG,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,MAAM,CAAC;;AAGxF,IAAA,SAAS,CAAC,IAAgB,EAAA;AACxB,QAAA,OAAO,IAAI,CAAC,WAAW,EAAE,UAAU,KAAK,IAAI;;uGAvKnC,SAAS,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAT,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAS,EAID,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,EAAA,kBAAA,EAAA,CAAA,oBAAA,EAAA,oBAAA,EAAA,gBAAgB,CAWW,EAAA,WAAA,EAAA,CAAA,kBAAA,EAAA,aAAA,EAAA,gBAAgB,CA4C3C,EAAA,iBAAA,EAAA,mBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAAA,gBAAgB,CA5BD,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,WAAA,EAAA,YAAA,EAAA,sDAAA,EAAA,yBAAA,EAAA,8BAAA,EAAA,gCAAA,EAAA,wCAAA,EAAA,aAAA,EAAA,mBAAA,EAAA,4CAAA,EAAA,kBAAA,EAAA,sBAAA,EAAA,gBAAA,EAAA,oBAAA,EAAA,+BAAA,EAAA,uCAAA,EAAA,oCAAA,EAAA,mBAAA,EAAA,EAAA,cAAA,EAAA,wCAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,UAAU,CC7G9C,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,mBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,cAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,eAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,oBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,cAAA,EAAA,WAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,88CAiCA,ED2CY,MAAA,EAAA,CAAA,q+LAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,SAAS,wPAAE,iBAAiB,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,CAAA,2BAAA,EAAA,UAAA,CAAA,EAAA,OAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAE3B,SAAS,EAAA,UAAA,EAAA,CAAA;kBAtBrB,SAAS;+BACE,mBAAmB,EAAA,QAAA,EACnB,yBAAyB,EAG7B,IAAA,EAAA;AACJ,wBAAA,aAAa,EAAE,YAAY;AAC3B,wBAAA,OAAO,EAAE,wCAAwC;AACjD,wBAAA,wDAAwD,EAAE,yBAAyB;AACnF,wBAAA,gCAAgC,EAAE,gCAAgC;AAClE,wBAAA,0CAA0C,EAAE,aAAa;AACzD,wBAAA,qBAAqB,EAAE,wCAAwC;AAC/D,wBAAA,oBAAoB,EAAE,oBAAoB;AAC1C,wBAAA,kBAAkB,EAAE,kBAAkB;AACtC,wBAAA,iCAAiC,EAAE,qCAAqC;AACxE,wBAAA,sCAAsC,EAAE,mBAAmB;AAC5D,qBAAA,EAAA,aAAA,EACc,iBAAiB,CAAC,IAAI,EAAA,eAAA,EAEpB,uBAAuB,CAAC,OAAO,EAAA,OAAA,EACvC,CAAC,SAAS,EAAE,iBAAiB,CAAC,EAAA,QAAA,EAAA,88CAAA,EAAA,MAAA,EAAA,CAAA,q+LAAA,CAAA,EAAA;wDAOnC,kBAAkB,EAAA,CAAA;sBADrB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAYpC,WAAW,EAAA,CAAA;sBADV,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,kBAAkB,EAAE,SAAS,EAAE,gBAAgB,EAAC;gBAI3D,iBAAiB,EAAA,CAAA;sBADpB;gBAamE,MAAM,EAAA,CAAA;sBAAzE,eAAe;uBAAC,UAAU,CAAC,MAAM,UAAU,CAAC,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;gBAU9D,eAAe,EAAA,CAAA;sBADlB;gBAoBD,aAAa,EAAA,CAAA;sBADZ,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAU3B,KAAK,EAAA,CAAA;sBAAb;gBAOQ,QAAQ,EAAA,CAAA;sBAAhB;gBAE8C,iBAAiB,EAAA,CAAA;sBAA/D,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,kBAAkB,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC;gBACP,QAAQ,EAAA,CAAA;sBAA7C,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,SAAS,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC;gBACO,aAAa,EAAA,CAAA;sBAAvD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,cAAc,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC;gBACb,cAAc,EAAA,CAAA;sBAAzC,SAAS;uBAAC,eAAe;gBACM,kBAAkB,EAAA,CAAA;sBAAjD,SAAS;uBAAC,mBAAmB;;AAyFhC;;AAEG;AAwBG,MAAO,UACX,SAAQ,UAAU,CAAA;AAGV,IAAA,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC;AACtC,IAAA,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;AACvB,IAAA,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC;AAE3B,IAAA,UAAU,GAAG,IAAI,OAAO,EAAQ;;IAGvC,SAAS,GAAY,KAAK;IAE1B,SAAS,GAAG,QAAQ,CAAC,MAC7B,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,KAAK,IAAI,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAC7D;;AAGD,IAAA,IACI,MAAM,GAAA;QACR,OAAO,IAAI,CAAC,SAAS;;IAGvB,IAAI,MAAM,CAAC,KAAc,EAAA;AACvB,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,EAAE;AAC5B,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,YAAA,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE;;;;IAMtC,QAAQ,GAAY,KAAK;;IAIzB,aAAa,GAAY,KAAK;IAK9B,QAAQ,GAAW,CAAC;AAEpB;;;;;AAKG;AACH,IAAA,YAAY;AAEZ;;;AAGG;AACH,IAAA,IAAI,cAAc,GAAA;QAChB,QACE,IAAI,CAAC,QAAQ;AACb,YAAA,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,UAAU,CAAC,aAAa;AAC7B,YAAA,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ;;;IAKvB,EAAE,GAAW,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC;AAIjE,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;QAEP,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC;AAC5D,QAAA,MAAM,mBAAmB,GAAG,MAAM,CAA6B,yBAAyB,EAAE;AACxF,YAAA,QAAQ,EAAE,IAAI;AACf,SAAA,CAAC;AACF,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,kBAAkB,CAAC,UAAU,CAAC,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAC7E,QAAA,MAAM,aAAa,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAErE,QAAA,IAAI,CAAC,YAAY,GAAG,mBAAmB,IAAI,EAAE;AAC7C,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,IAAI,IAAI,GAAG,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC;AAE9D,QAAA,IAAI,aAAa,KAAK,gBAAgB,EAAE;AACtC,YAAA,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,EAAC,aAAa,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAC;;QAGnE,IAAI,CAAC,UAAU,CAAC;AACb,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;aAC/B,SAAS,CAAC,kBAAkB,IAAG;AAC9B,YAAA,IAAI,CAAC,kBAAkB,GAAG,kBAAkB;AAC9C,SAAC,CAAC;;;IAIN,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,EAAE;;IAGvC,eAAe,GAAA;QACb,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC;;IAGpC,WAAW,GAAA;AAClB,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;AACtB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;QAC1B,KAAK,CAAC,WAAW,EAAE;QACnB,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC;;IAGpD,YAAY,GAAA;;;AAGV,QAAA,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;;AAG7E,IAAA,cAAc,CAAC,KAAoB,EAAA;AACjC,QAAA,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,EAAE;AACtD,YAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,KAAK,CAAC,cAAc,EAAE;;AACjB,iBAAA,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;;;AAGnC,gBAAA,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,EAAE;oBAC3B,KAAK,CAAC,cAAc,EAAE;;AAGxB,gBAAA,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,EAAE;;;;IAK3C,gBAAgB,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC;AACrB,cAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;cAC1B,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,YAAY,CAAC,eAAe,CAAC;;IAGjE,gBAAgB,GAAA;AACd,QAAA,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;YAC5B,OAAO,IAAI,CAAC,MAAM,GAAG,MAAM,GAAG,OAAO;;aAChC;YACL,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,YAAY,CAAC,eAAe,CAAC;;;IAItE,eAAe,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,MAAM,GAAG,IAAI;;IAGjE,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,YAAY,CAAC,MAAM,CAAC;;uGAtJnF,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAV,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAU,EAkBF,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,8BAAA,EAAA,MAAA,EAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAAA,gBAAgB,CAahB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAIhB,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAAA,gBAAgB,CAItB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,CE1T/E,EAAA,EAAA,EAAA,IAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,wBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,oBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,mBAAA,EAAA,oBAAA,EAAA,UAAA,EAAA,oBAAA,EAAA,oBAAA,EAAA,SAAA,EAAA,IAAA,EAAA,eAAA,EAAA,aAAA,EAAA,WAAA,EAAA,YAAA,EAAA,4BAAA,EAAA,UAAA,EAAA,uBAAA,EAAA,QAAA,EAAA,EAAA,cAAA,EAAA,8CAAA,EAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,uUAcA,sgHFmQY,SAAS,EAAA,QAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAER,UAAU,EAAA,UAAA,EAAA,CAAA;kBAvBtB,SAAS;+BACE,8BAA8B,EAAA,QAAA,EAC9B,YAAY,EAAA,eAAA,EACL,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAG/B,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,8CAA8C;AACvD,wBAAA,sBAAsB,EAAE,oBAAoB;AAC5C,wBAAA,qBAAqB,EAAE,mBAAmB;AAC1C,wBAAA,sBAAsB,EAAE,UAAU;AAClC,wBAAA,sBAAsB,EAAE,oBAAoB;AAC5C,wBAAA,WAAW,EAAE,IAAI;AACjB,wBAAA,iBAAiB,EAAE,aAAa;AAChC,wBAAA,aAAa,EAAE,YAAY;AAC3B,wBAAA,8BAA8B,EAAE,UAAU;AAC1C,wBAAA,yBAAyB,EAAE,QAAQ;AACnC,wBAAA,SAAS,EAAE,gBAAgB;AAC3B,wBAAA,WAAW,EAAE,wBAAwB;qBACtC,EACQ,OAAA,EAAA,CAAC,SAAS,CAAC,EAAA,QAAA,EAAA,uUAAA,EAAA,MAAA,EAAA,CAAA,88GAAA,CAAA,EAAA;wDAqBhB,MAAM,EAAA,CAAA;sBADT,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAcpC,QAAQ,EAAA,CAAA;sBADP,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAKpC,aAAa,EAAA,CAAA;sBADZ,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAMpC,QAAQ,EAAA,CAAA;sBAHP,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA;wBACL,SAAS,EAAE,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;AAC5E,qBAAA;gBAyBQ,EAAE,EAAA,CAAA;sBAAV;;AAyFH;;AAEG;MAcU,cAAc,CAAA;;IAEhB,EAAE,GAAW,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,oBAAoB,CAAC;;AAGtE,IAAA,YAAY;uGALD,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,cAAc,iSAVf,2BAA2B,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAU1B,cAAc,EAAA,UAAA,EAAA,CAAA;kBAb1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,mBAAmB;AAC7B,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,QAAQ,EAAE,2BAA2B;AACrC,oBAAA,IAAI,EAAE;AACJ,wBAAA,wBAAwB,EAAE,cAAc;AACxC,wBAAA,WAAW,EAAE,IAAI;AACjB,wBAAA,OAAO,EAAE,uBAAuB;AAChC,wBAAA,MAAM,EAAE,UAAU;AACnB,qBAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAChD,iBAAA;8BAGU,EAAE,EAAA,CAAA;sBAAV;;;MGzZU,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,YApBtB,eAAe;YACf,aAAa;YACb,WAAW;YACX,MAAM;YACN,WAAW;YACX,SAAS;YACT,cAAc;AACd,YAAA,UAAU,aAGV,eAAe;YACf,aAAa;YACb,WAAW;YACX,MAAM;YACN,WAAW;YACX,SAAS;YACT,cAAc;YACd,UAAU,CAAA,EAAA,CAAA;wGAGD,aAAa,EAAA,OAAA,EAAA,CApBtB,eAAe,EAUf,eAAe,CAAA,EAAA,CAAA;;2FAUN,aAAa,EAAA,UAAA,EAAA,CAAA;kBAtBzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE;wBACP,eAAe;wBACf,aAAa;wBACb,WAAW;wBACX,MAAM;wBACN,WAAW;wBACX,SAAS;wBACT,cAAc;wBACd,UAAU;AACX,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,eAAe;wBACf,aAAa;wBACb,WAAW;wBACX,MAAM;wBACN,WAAW;wBACX,SAAS;wBACT,cAAc;wBACd,UAAU;AACX,qBAAA;AACF,iBAAA;;;AC7BD;;;;;AAKG;AACU,MAAA,iBAAiB,GAE1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDF,IAAA,YAAY,EAAE;AACZ,QAAA,IAAI,EAAE,CAAC;AACP,QAAA,IAAI,EAAE,cAAc;AACpB,QAAA,WAAW,EAAE;AACX,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,uDAAuD;AAC7D,gBAAA,MAAM,EAAE;AACN,oBAAA,IAAI,EAAE,CAAC;oBACP,MAAM,EAAE,EAAC,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAC;AAClD,oBAAA,MAAM,EAAE,IAAI;AACb,iBAAA;AACF,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,MAAM;AACZ,gBAAA,MAAM,EAAE;AACN,oBAAA,IAAI,EAAE,CAAC;AACP,oBAAA,MAAM,EAAE;AACN,wBAAA,SAAS,EAAE,0BAA0B;AACrC,wBAAA,SAAS,EAAE,KAAK;AAChB,wBAAA,UAAU,EAAE,QAAQ;AACrB,qBAAA;AACD,oBAAA,MAAM,EAAE,IAAI;AACb,iBAAA;AACF,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,OAAO;AACb,gBAAA,MAAM,EAAE;AACN,oBAAA,IAAI,EAAE,CAAC;AACP,oBAAA,MAAM,EAAE;AACN,wBAAA,SAAS,EAAE,yBAAyB;AACpC,wBAAA,SAAS,EAAE,KAAK;AAChB,wBAAA,UAAU,EAAE,QAAQ;AACrB,qBAAA;AACD,oBAAA,MAAM,EAAE,IAAI;AACb,iBAAA;AACF,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,wDAAwD;AAC9D,gBAAA,SAAS,EAAE;AACT,oBAAA,IAAI,EAAE,CAAC;AACP,oBAAA,MAAM,EAAE,IAAI;AACZ,oBAAA,OAAO,EAAE,sDAAsD;AAChE,iBAAA;AACD,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,4BAA4B;AAClC,gBAAA,SAAS,EAAE;AACT,oBAAA;AACE,wBAAA,IAAI,EAAE,CAAC;wBACP,MAAM,EAAE,EAAC,SAAS,EAAE,0BAA0B,EAAE,UAAU,EAAE,QAAQ,EAAC;AACrE,wBAAA,MAAM,EAAE,IAAI;AACb,qBAAA;AACD,oBAAA;AACE,wBAAA,IAAI,EAAE,CAAC;AACP,wBAAA,MAAM,EAAE,IAAI;AACZ,wBAAA,OAAO,EAAE,sDAAsD;AAChE,qBAAA;AACF,iBAAA;AACD,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,6BAA6B;AACnC,gBAAA,SAAS,EAAE;AACT,oBAAA;AACE,wBAAA,IAAI,EAAE,CAAC;wBACP,MAAM,EAAE,EAAC,SAAS,EAAE,yBAAyB,EAAE,UAAU,EAAE,QAAQ,EAAC;AACpE,wBAAA,MAAM,EAAE,IAAI;AACb,qBAAA;AACD,oBAAA;AACE,wBAAA,IAAI,EAAE,CAAC;AACP,wBAAA,MAAM,EAAE,IAAI;AACZ,wBAAA,OAAO,EAAE,sDAAsD;AAChE,qBAAA;AACF,iBAAA;AACD,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACF,SAAA;AACD,QAAA,OAAO,EAAE,EAAE;AACZ,KAAA;;;;;"}