{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/core/testing/optgroup-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {OptgroupHarnessFilters} from './optgroup-harness-filters';\nimport {MatOptionHarness} from './option-harness';\nimport {OptionHarnessFilters} from './option-harness-filters';\n\n/** Harness for interacting with a `mat-optgroup` in tests. */\nexport class MatOptgroupHarness extends ComponentHarness {\n  /** Selector used to locate option group instances. */\n  static hostSelector = '.mat-mdc-optgroup';\n  private _label = this.locatorFor('.mat-mdc-optgroup-label');\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a option group with specific\n   * attributes.\n   * @param options Options for filtering which option instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatOptgroupHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: OptgroupHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options).addOption(\n      'labelText',\n      options.labelText,\n      async (harness, title) => HarnessPredicate.stringMatches(await harness.getLabelText(), title),\n    );\n  }\n\n  /** Gets the option group's label text. */\n  async getLabelText(): Promise<string> {\n    return (await this._label()).text();\n  }\n\n  /** Gets whether the option group is disabled. */\n  async isDisabled(): Promise<boolean> {\n    return (await (await this.host()).getAttribute('aria-disabled')) === 'true';\n  }\n\n  /**\n   * Gets the options that are inside the group.\n   * @param filter Optionally filters which options are included.\n   */\n  async getOptions(filter: OptionHarnessFilters = {}): Promise<MatOptionHarness[]> {\n    return this.locatorForAll(MatOptionHarness.with(filter))();\n  }\n}\n"], "names": [], "mappings": ";;;AAiBA;AACM,MAAO,kBAAmB,SAAQ,gBAAgB,CAAA;;AAEtD,IAAA,OAAO,YAAY,GAAG,mBAAmB;AACjC,IAAA,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,yBAAyB,CAAC;AAE3D;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAkC,EAAE,EAAA;AAEpC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,SAAS,CAClD,WAAW,EACX,OAAO,CAAC,SAAS,EACjB,OAAO,OAAO,EAAE,KAAK,KAAK,gBAAgB,CAAC,aAAa,CAAC,MAAM,OAAO,CAAC,YAAY,EAAE,EAAE,KAAK,CAAC,CAC9F;;;AAIH,IAAA,MAAM,YAAY,GAAA;QAChB,OAAO,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE;;;AAIrC,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,eAAe,CAAC,MAAM,MAAM;;AAG7E;;;AAGG;AACH,IAAA,MAAM,UAAU,CAAC,MAAA,GAA+B,EAAE,EAAA;AAChD,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE;;;;;;"}