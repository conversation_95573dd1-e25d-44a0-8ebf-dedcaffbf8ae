{"version": 3, "file": "line-Bm3zUbBF.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/core/line/line.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule, Directive, ElementRef, QueryList} from '@angular/core';\nimport {startWith} from 'rxjs/operators';\nimport {MatCommonModule} from '../common-behaviors/common-module';\n\n/**\n * Shared directive to count lines inside a text area, such as a list item.\n * Line elements can be extracted with a @ContentChildren(MatLine) query, then\n * counted by checking the query list's length.\n */\n@Directive({\n  selector: '[mat-line], [matLine]',\n  host: {'class': 'mat-line'},\n})\nexport class MatLine {}\n\n/**\n * Helper that takes a query list of lines and sets the correct class on the host.\n * @docs-private\n */\nexport function setLines(\n  lines: QueryList<unknown>,\n  element: ElementRef<HTMLElement>,\n  prefix = 'mat',\n) {\n  // Note: doesn't need to unsubscribe, because `changes`\n  // gets completed by <PERSON><PERSON> when the view is destroyed.\n  lines.changes.pipe(startWith(lines)).subscribe(({length}) => {\n    setClass(element, `${prefix}-2-line`, false);\n    setClass(element, `${prefix}-3-line`, false);\n    setClass(element, `${prefix}-multi-line`, false);\n\n    if (length === 2 || length === 3) {\n      setClass(element, `${prefix}-${length}-line`, true);\n    } else if (length > 3) {\n      setClass(element, `${prefix}-multi-line`, true);\n    }\n  });\n}\n\n/** Adds or removes a class from an element. */\nfunction setClass(element: ElementRef<HTMLElement>, className: string, isAdd: boolean): void {\n  element.nativeElement.classList.toggle(className, isAdd);\n}\n\n@NgModule({\n  imports: [MatCommonModule, MatLine],\n  exports: [MatLine, MatCommonModule],\n})\nexport class MatLineModule {}\n"], "names": [], "mappings": ";;;;;AAYA;;;;AAIG;MAKU,OAAO,CAAA;uGAAP,OAAO,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAP,OAAO,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,uBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,UAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAP,OAAO,EAAA,UAAA,EAAA,CAAA;kBAJnB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,uBAAuB;AACjC,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,UAAU,EAAC;AAC5B,iBAAA;;AAGD;;;AAGG;AACG,SAAU,QAAQ,CACtB,KAAyB,EACzB,OAAgC,EAChC,MAAM,GAAG,KAAK,EAAA;;;AAId,IAAA,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAC,MAAM,EAAC,KAAI;QAC1D,QAAQ,CAAC,OAAO,EAAE,CAAA,EAAG,MAAM,CAAS,OAAA,CAAA,EAAE,KAAK,CAAC;QAC5C,QAAQ,CAAC,OAAO,EAAE,CAAA,EAAG,MAAM,CAAS,OAAA,CAAA,EAAE,KAAK,CAAC;QAC5C,QAAQ,CAAC,OAAO,EAAE,CAAA,EAAG,MAAM,CAAa,WAAA,CAAA,EAAE,KAAK,CAAC;QAEhD,IAAI,MAAM,KAAK,CAAC,IAAI,MAAM,KAAK,CAAC,EAAE;YAChC,QAAQ,CAAC,OAAO,EAAE,CAAG,EAAA,MAAM,CAAI,CAAA,EAAA,MAAM,CAAO,KAAA,CAAA,EAAE,IAAI,CAAC;;AAC9C,aAAA,IAAI,MAAM,GAAG,CAAC,EAAE;YACrB,QAAQ,CAAC,OAAO,EAAE,CAAA,EAAG,MAAM,CAAa,WAAA,CAAA,EAAE,IAAI,CAAC;;AAEnD,KAAC,CAAC;AACJ;AAEA;AACA,SAAS,QAAQ,CAAC,OAAgC,EAAE,SAAiB,EAAE,KAAc,EAAA;IACnF,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC;AAC1D;MAMa,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,YAHd,eAAe,EAhCd,OAAO,CAAP,EAAA,OAAA,EAAA,CAAA,OAAO,EAiCC,eAAe,CAAA,EAAA,CAAA;wGAEvB,aAAa,EAAA,OAAA,EAAA,CAHd,eAAe,EACN,eAAe,CAAA,EAAA,CAAA;;2FAEvB,aAAa,EAAA,UAAA,EAAA,CAAA;kBAJzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,OAAO,CAAC;AACnC,oBAAA,OAAO,EAAE,CAAC,OAAO,EAAE,eAAe,CAAC;AACpC,iBAAA;;;;;"}