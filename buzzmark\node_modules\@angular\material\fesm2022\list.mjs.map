{"version": 3, "file": "list.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/list/list-option-types.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/list/list-item-sections.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/list/tokens.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/list/list-base.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/list/action-list.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/list/list.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/list/list-item.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/list/list-option.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/list/list-option.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/list/subheader.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/list/nav-list.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/list/selection-list.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/list/list-module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken} from '@angular/core';\n\n/**\n * Type describing possible positions of a checkbox or radio in a list option\n * with respect to the list item's text.\n */\nexport type MatListOptionTogglePosition = 'before' | 'after';\n\n/**\n * Interface describing a list option. This is used to avoid circular\n * dependencies between the list-option and the styler directives.\n * @docs-private\n */\nexport interface ListOption {\n  _getTogglePosition(): MatListOptionTogglePosition;\n}\n\n/**\n * Injection token that can be used to reference instances of an `ListOption`. It serves\n * as alternative token to an actual implementation which could result in undesired\n * retention of the class or circular references breaking runtime execution.\n * @docs-private\n */\nexport const LIST_OPTION = new InjectionToken<ListOption>('ListOption');\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, ElementRef, inject} from '@angular/core';\nimport {LIST_OPTION, ListOption} from './list-option-types';\n\n/**\n * Directive capturing the title of a list item. A list item usually consists of a\n * title and optional secondary or tertiary lines.\n *\n * Text content for the title never wraps. There can only be a single title per list item.\n */\n@Directive({\n  selector: '[matListItemTitle]',\n  host: {'class': 'mat-mdc-list-item-title mdc-list-item__primary-text'},\n})\nexport class MatListItemTitle {\n  _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n\n  constructor(...args: unknown[]);\n  constructor() {}\n}\n\n/**\n * Directive capturing a line in a list item. A list item usually consists of a\n * title and optional secondary or tertiary lines.\n *\n * Text content inside a line never wraps. There can be at maximum two lines per list item.\n */\n@Directive({\n  selector: '[matListItemLine]',\n  host: {'class': 'mat-mdc-list-item-line mdc-list-item__secondary-text'},\n})\nexport class MatListItemLine {\n  _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n\n  constructor(...args: unknown[]);\n  constructor() {}\n}\n\n/**\n * Directive matching an optional meta section for list items.\n *\n * List items can reserve space at the end of an item to display a control,\n * button or additional text content.\n */\n@Directive({\n  selector: '[matListItemMeta]',\n  host: {'class': 'mat-mdc-list-item-meta mdc-list-item__end'},\n})\nexport class MatListItemMeta {}\n\n/**\n * @docs-private\n *\n * MDC uses the very intuitively named classes `.mdc-list-item__start` and `.mat-list-item__end` to\n * position content such as icons or checkboxes/radios that comes either before or after the text\n * content respectively. This directive detects the placement of the checkbox/radio and applies the\n * correct MDC class to position the icon/avatar on the opposite side.\n */\n@Directive({\n  host: {\n    // MDC uses intuitively named classes `.mdc-list-item__start` and `.mat-list-item__end` to\n    // position content such as icons or checkboxes/radios that comes either before or after the\n    // text content respectively. This directive detects the placement of the checkbox/radio and\n    // applies the correct MDC class to position the icon/avatar on the opposite side.\n    '[class.mdc-list-item__start]': '_isAlignedAtStart()',\n    '[class.mdc-list-item__end]': '!_isAlignedAtStart()',\n  },\n})\nexport class _MatListItemGraphicBase {\n  _listOption = inject<ListOption>(LIST_OPTION, {optional: true});\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  _isAlignedAtStart() {\n    // By default, in all list items the graphic is aligned at start. In list options,\n    // the graphic is only aligned at start if the checkbox/radio is at the end.\n    return !this._listOption || this._listOption?._getTogglePosition() === 'after';\n  }\n}\n\n/**\n * Directive matching an optional avatar within a list item.\n *\n * List items can reserve space at the beginning of an item to display an avatar.\n */\n@Directive({\n  selector: '[matListItemAvatar]',\n  host: {'class': 'mat-mdc-list-item-avatar'},\n})\nexport class MatListItemAvatar extends _MatListItemGraphicBase {}\n\n/**\n * Directive matching an optional icon within a list item.\n *\n * List items can reserve space at the beginning of an item to display an icon.\n */\n@Directive({\n  selector: '[matListItemIcon]',\n  host: {'class': 'mat-mdc-list-item-icon'},\n})\nexport class MatListItemIcon extends _MatListItemGraphicBase {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken} from '@angular/core';\n\n/** Object that can be used to configure the default options for the list module. */\nexport interface MatListConfig {\n  /** Whether icon indicators should be hidden for single-selection. */\n  hideSingleSelectionIndicator?: boolean;\n}\n\n/** Injection token that can be used to provide the default options for the list module. */\nexport const MAT_LIST_CONFIG = new InjectionToken<MatListConfig>('MAT_LIST_CONFIG');\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {BooleanInput, coerceBooleanProperty, coerceNumberProperty} from '@angular/cdk/coercion';\nimport {Platform} from '@angular/cdk/platform';\nimport {\n  AfterViewInit,\n  ContentChildren,\n  Directive,\n  ElementRef,\n  inject,\n  Input,\n  NgZone,\n  OnDestroy,\n  QueryList,\n  ANIMATION_MODULE_TYPE,\n  Injector,\n} from '@angular/core';\nimport {\n  _StructuralStylesLoader,\n  MAT_RIPPLE_GLOBAL_OPTIONS,\n  RippleConfig,\n  RippleGlobalOptions,\n  RippleRenderer,\n  RippleTarget,\n} from '../core';\nimport {_CdkPrivateStyleLoader} from '@angular/cdk/private';\nimport {Subscription, merge} from 'rxjs';\nimport {\n  MatListItemLine,\n  MatListItemTitle,\n  MatListItemIcon,\n  MatListItemAvatar,\n} from './list-item-sections';\nimport {MAT_LIST_CONFIG} from './tokens';\n\n@Directive({\n  host: {\n    '[attr.aria-disabled]': 'disabled',\n  },\n})\n/** @docs-private */\nexport abstract class MatListBase {\n  _isNonInteractive: boolean = true;\n\n  /** Whether ripples for all list items is disabled. */\n  @Input()\n  get disableRipple(): boolean {\n    return this._disableRipple;\n  }\n  set disableRipple(value: BooleanInput) {\n    this._disableRipple = coerceBooleanProperty(value);\n  }\n  private _disableRipple: boolean = false;\n\n  /**\n   * Whether the entire list is disabled. When disabled, the list itself and each of its list items\n   * are disabled.\n   */\n  @Input()\n  get disabled(): boolean {\n    return this._disabled;\n  }\n  set disabled(value: BooleanInput) {\n    this._disabled = coerceBooleanProperty(value);\n  }\n  private _disabled = false;\n\n  protected _defaultOptions = inject(MAT_LIST_CONFIG, {optional: true});\n}\n\n@Directive({\n  host: {\n    '[class.mdc-list-item--disabled]': 'disabled',\n    '[attr.aria-disabled]': 'disabled',\n    '[attr.disabled]': '(_isButtonElement && disabled) || null',\n  },\n})\n/** @docs-private */\nexport abstract class MatListItemBase implements AfterViewInit, OnDestroy, RippleTarget {\n  _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  protected _ngZone = inject(NgZone);\n  private _listBase = inject(MatListBase, {optional: true});\n  private _platform = inject(Platform);\n\n  /** Query list matching list-item line elements. */\n  abstract _lines: QueryList<MatListItemLine> | undefined;\n\n  /** Query list matching list-item title elements. */\n  abstract _titles: QueryList<MatListItemTitle> | undefined;\n\n  /**\n   * Element reference to the unscoped content in a list item.\n   *\n   * Unscoped content is user-projected text content in a list item that is\n   * not part of an explicit line or title.\n   */\n  abstract _unscopedContent: ElementRef<HTMLSpanElement> | undefined;\n\n  /** Host element for the list item. */\n  _hostElement: HTMLElement;\n\n  /** indicate whether the host element is a button or not */\n  _isButtonElement: boolean;\n\n  /** Whether animations are disabled. */\n  _noopAnimations: boolean;\n\n  @ContentChildren(MatListItemAvatar, {descendants: false}) _avatars: QueryList<never>;\n  @ContentChildren(MatListItemIcon, {descendants: false}) _icons: QueryList<never>;\n\n  /**\n   * The number of lines this list item should reserve space for. If not specified,\n   * lines are inferred based on the projected content.\n   *\n   * Explicitly specifying the number of lines is useful if you want to acquire additional\n   * space and enable the wrapping of text. The unscoped text content of a list item will\n   * always be able to take up the remaining space of the item, unless it represents the title.\n   *\n   * A maximum of three lines is supported as per the Material Design specification.\n   */\n  @Input()\n  set lines(lines: number | string | null) {\n    this._explicitLines = coerceNumberProperty(lines, null);\n    this._updateItemLines(false);\n  }\n  _explicitLines: number | null = null;\n\n  /** Whether ripples for list items are disabled. */\n  @Input()\n  get disableRipple(): boolean {\n    return (\n      this.disabled ||\n      this._disableRipple ||\n      this._noopAnimations ||\n      !!this._listBase?.disableRipple\n    );\n  }\n  set disableRipple(value: BooleanInput) {\n    this._disableRipple = coerceBooleanProperty(value);\n  }\n  private _disableRipple: boolean = false;\n\n  /** Whether the list-item is disabled. */\n  @Input()\n  get disabled(): boolean {\n    return this._disabled || !!this._listBase?.disabled;\n  }\n  set disabled(value: BooleanInput) {\n    this._disabled = coerceBooleanProperty(value);\n  }\n  private _disabled = false;\n\n  private _subscriptions = new Subscription();\n  private _rippleRenderer: RippleRenderer | null = null;\n\n  /** Whether the list item has unscoped text content. */\n  _hasUnscopedTextContent: boolean = false;\n\n  /**\n   * Implemented as part of `RippleTarget`.\n   * @docs-private\n   */\n  rippleConfig: RippleConfig & RippleGlobalOptions;\n\n  /**\n   * Implemented as part of `RippleTarget`.\n   * @docs-private\n   */\n  get rippleDisabled(): boolean {\n    return this.disableRipple || !!this.rippleConfig.disabled;\n  }\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    const globalRippleOptions = inject<RippleGlobalOptions>(MAT_RIPPLE_GLOBAL_OPTIONS, {\n      optional: true,\n    });\n    const animationMode = inject(ANIMATION_MODULE_TYPE, {optional: true});\n\n    this.rippleConfig = globalRippleOptions || {};\n    this._hostElement = this._elementRef.nativeElement;\n    this._isButtonElement = this._hostElement.nodeName.toLowerCase() === 'button';\n    this._noopAnimations = animationMode === 'NoopAnimations';\n\n    if (this._listBase && !this._listBase._isNonInteractive) {\n      this._initInteractiveListItem();\n    }\n\n    // If no type attribute is specified for a host `<button>` element, set it to `button`. If a\n    // type attribute is already specified, we do nothing. We do this for backwards compatibility.\n    // TODO: Determine if we intend to continue doing this for the MDC-based list.\n    if (this._isButtonElement && !this._hostElement.hasAttribute('type')) {\n      this._hostElement.setAttribute('type', 'button');\n    }\n  }\n\n  ngAfterViewInit() {\n    this._monitorProjectedLinesAndTitle();\n    this._updateItemLines(true);\n  }\n\n  ngOnDestroy() {\n    this._subscriptions.unsubscribe();\n    if (this._rippleRenderer !== null) {\n      this._rippleRenderer._removeTriggerEvents();\n    }\n  }\n\n  /** Whether the list item has icons or avatars. */\n  _hasIconOrAvatar() {\n    return !!(this._avatars.length || this._icons.length);\n  }\n\n  private _initInteractiveListItem() {\n    this._hostElement.classList.add('mat-mdc-list-item-interactive');\n    this._rippleRenderer = new RippleRenderer(\n      this,\n      this._ngZone,\n      this._hostElement,\n      this._platform,\n      inject(Injector),\n    );\n    this._rippleRenderer.setupTriggerEvents(this._hostElement);\n  }\n\n  /**\n   * Subscribes to changes in the projected title and lines. Triggers a\n   * item lines update whenever a change occurs.\n   */\n  private _monitorProjectedLinesAndTitle() {\n    this._ngZone.runOutsideAngular(() => {\n      this._subscriptions.add(\n        merge(this._lines!.changes, this._titles!.changes).subscribe(() =>\n          this._updateItemLines(false),\n        ),\n      );\n    });\n  }\n\n  /**\n   * Updates the lines of the list item. Based on the projected user content and optional\n   * explicit lines setting, the visual appearance of the list item is determined.\n   *\n   * This method should be invoked whenever the projected user content changes, or\n   * when the explicit lines have been updated.\n   *\n   * @param recheckUnscopedContent Whether the projected unscoped content should be re-checked.\n   *   The unscoped content is not re-checked for every update as it is a rather expensive check\n   *   for content that is expected to not change very often.\n   */\n  _updateItemLines(recheckUnscopedContent: boolean) {\n    // If the updated is triggered too early before the view and content is initialized,\n    // we just skip the update. After view initialization the update is triggered again.\n    if (!this._lines || !this._titles || !this._unscopedContent) {\n      return;\n    }\n\n    // Re-check the DOM for unscoped text content if requested. This needs to\n    // happen before any computation or sanity checks run as these rely on the\n    // result of whether there is unscoped text content or not.\n    if (recheckUnscopedContent) {\n      this._checkDomForUnscopedTextContent();\n    }\n\n    // Sanity check the list item lines and title in the content. This is a dev-mode only\n    // check that can be dead-code eliminated by Terser in production.\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      sanityCheckListItemContent(this);\n    }\n\n    const numberOfLines = this._explicitLines ?? this._inferLinesFromContent();\n    const unscopedContentEl = this._unscopedContent.nativeElement;\n\n    // Update the list item element to reflect the number of lines.\n    this._hostElement.classList.toggle('mat-mdc-list-item-single-line', numberOfLines <= 1);\n    this._hostElement.classList.toggle('mdc-list-item--with-one-line', numberOfLines <= 1);\n    this._hostElement.classList.toggle('mdc-list-item--with-two-lines', numberOfLines === 2);\n    this._hostElement.classList.toggle('mdc-list-item--with-three-lines', numberOfLines === 3);\n\n    // If there is no title and the unscoped content is the is the only line, the\n    // unscoped text content will be treated as the title of the list-item.\n    if (this._hasUnscopedTextContent) {\n      const treatAsTitle = this._titles.length === 0 && numberOfLines === 1;\n      unscopedContentEl.classList.toggle('mdc-list-item__primary-text', treatAsTitle);\n      unscopedContentEl.classList.toggle('mdc-list-item__secondary-text', !treatAsTitle);\n    } else {\n      unscopedContentEl.classList.remove('mdc-list-item__primary-text');\n      unscopedContentEl.classList.remove('mdc-list-item__secondary-text');\n    }\n  }\n\n  /**\n   * Infers the number of lines based on the projected user content. This is useful\n   * if no explicit number of lines has been specified on the list item.\n   *\n   * The number of lines is inferred based on whether there is a title, the number of\n   * additional lines (secondary/tertiary). An additional line is acquired if there is\n   * unscoped text content.\n   */\n  private _inferLinesFromContent() {\n    let numOfLines = this._titles!.length + this._lines!.length;\n    if (this._hasUnscopedTextContent) {\n      numOfLines += 1;\n    }\n    return numOfLines;\n  }\n\n  /** Checks whether the list item has unscoped text content. */\n  private _checkDomForUnscopedTextContent() {\n    this._hasUnscopedTextContent = Array.from<ChildNode>(\n      this._unscopedContent!.nativeElement.childNodes,\n    )\n      .filter(node => node.nodeType !== node.COMMENT_NODE)\n      .some(node => !!(node.textContent && node.textContent.trim()));\n  }\n}\n\n/**\n * Sanity checks the configuration of the list item with respect to the amount\n * of lines, whether there is a title, or if there is unscoped text content.\n *\n * The checks are extracted into a top-level function that can be dead-code\n * eliminated by Terser or other optimizers in production mode.\n */\nfunction sanityCheckListItemContent(item: MatListItemBase) {\n  const numTitles = item._titles!.length;\n  const numLines = item._lines!.length;\n\n  if (numTitles > 1) {\n    console.warn('A list item cannot have multiple titles.');\n  }\n  if (numTitles === 0 && numLines > 0) {\n    console.warn('A list item line can only be used if there is a list item title.');\n  }\n  if (\n    numTitles === 0 &&\n    item._hasUnscopedTextContent &&\n    item._explicitLines !== null &&\n    item._explicitLines > 1\n  ) {\n    console.warn('A list item cannot have wrapping content without a title.');\n  }\n  if (numLines > 2 || (numLines === 2 && item._hasUnscopedTextContent)) {\n    console.warn('A list item can have at maximum three lines.');\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ChangeDetectionStrategy, Component, ViewEncapsulation} from '@angular/core';\nimport {MatListBase} from './list-base';\n\n@Component({\n  selector: 'mat-action-list',\n  exportAs: 'matActionList',\n  template: '<ng-content></ng-content>',\n  host: {\n    'class': 'mat-mdc-action-list mat-mdc-list-base mdc-list',\n    'role': 'group',\n  },\n  styleUrl: 'list.css',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [{provide: MatListBase, useExisting: MatActionList}],\n})\nexport class MatActionList extends MatListBase {\n  // An navigation list is considered interactive, but does not extend the interactive list\n  // base class. We do this because as per MDC, items of interactive lists are only reachable\n  // through keyboard shortcuts. We want all items for the navigation list to be reachable\n  // through tab key as we do not intend to provide any special accessibility treatment. The\n  // accessibility treatment depends on how the end-user will interact with it.\n  override _isNonInteractive = false;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  Input,\n  ContentChildren,\n  ElementRef,\n  QueryList,\n  ViewChild,\n  ViewEncapsulation,\n  InjectionToken,\n} from '@angular/core';\nimport {MatListBase, MatListItemBase} from './list-base';\nimport {MatListItemLine, MatListItemMeta, MatListItemTitle} from './list-item-sections';\nimport {coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {CdkObserveContent} from '@angular/cdk/observers';\n\n/**\n * Injection token that can be used to inject instances of `MatList`. It serves as\n * alternative token to the actual `MatList` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nexport const MAT_LIST = new InjectionToken<MatList>('MatList');\n\n@Component({\n  selector: 'mat-list',\n  exportAs: 'matList',\n  template: '<ng-content></ng-content>',\n  host: {\n    'class': 'mat-mdc-list mat-mdc-list-base mdc-list',\n  },\n  styleUrl: 'list.css',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [{provide: MatListBase, useExisting: MatList}],\n})\nexport class MatList extends MatListBase {}\n\n@Component({\n  selector: 'mat-list-item, a[mat-list-item], button[mat-list-item]',\n  exportAs: 'matListItem',\n  host: {\n    'class': 'mat-mdc-list-item mdc-list-item',\n    '[class.mdc-list-item--activated]': 'activated',\n    '[class.mdc-list-item--with-leading-avatar]': '_avatars.length !== 0',\n    '[class.mdc-list-item--with-leading-icon]': '_icons.length !== 0',\n    '[class.mdc-list-item--with-trailing-meta]': '_meta.length !== 0',\n    // Utility class that makes it easier to target the case where there's both a leading\n    // and a trailing icon. Avoids having to write out all the combinations.\n    '[class.mat-mdc-list-item-both-leading-and-trailing]': '_hasBothLeadingAndTrailing()',\n    '[class._mat-animation-noopable]': '_noopAnimations',\n    '[attr.aria-current]': '_getAriaCurrent()',\n  },\n  templateUrl: 'list-item.html',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [CdkObserveContent],\n})\nexport class MatListItem extends MatListItemBase {\n  @ContentChildren(MatListItemLine, {descendants: true}) _lines: QueryList<MatListItemLine>;\n  @ContentChildren(MatListItemTitle, {descendants: true}) _titles: QueryList<MatListItemTitle>;\n  @ContentChildren(MatListItemMeta, {descendants: true}) _meta: QueryList<MatListItemMeta>;\n  @ViewChild('unscopedContent') _unscopedContent: ElementRef<HTMLSpanElement>;\n  @ViewChild('text') _itemText: ElementRef<HTMLElement>;\n\n  /** Indicates whether an item in a `<mat-nav-list>` is the currently active page. */\n  @Input()\n  get activated(): boolean {\n    return this._activated;\n  }\n  set activated(activated) {\n    this._activated = coerceBooleanProperty(activated);\n  }\n  _activated = false;\n\n  /**\n   * Determine the value of `aria-current`. Return 'page' if this item is an activated anchor tag.\n   * Otherwise, return `null`. This method is safe to use with server-side rendering.\n   */\n  _getAriaCurrent(): string | null {\n    return this._hostElement.nodeName === 'A' && this._activated ? 'page' : null;\n  }\n\n  protected _hasBothLeadingAndTrailing(): boolean {\n    return this._meta.length !== 0 && (this._avatars.length !== 0 || this._icons.length !== 0);\n  }\n}\n", "<ng-content select=\"[matListItemAvatar],[matListItemIcon]\"></ng-content>\n\n<span class=\"mdc-list-item__content\">\n  <ng-content select=\"[matListItemTitle]\"></ng-content>\n  <ng-content select=\"[matListItemLine]\"></ng-content>\n  <span #unscopedContent class=\"mat-mdc-list-item-unscoped-content\"\n        (cdkObserveContent)=\"_updateItemLines(true)\">\n    <ng-content></ng-content>\n  </span>\n</span>\n\n<ng-content select=\"[matListItemMeta]\"></ng-content>\n\n<ng-content select=\"mat-divider\"></ng-content>\n\n<!--\n  Strong focus indicator element. MDC uses the `::before` pseudo element for the default\n  focus/hover/selected state, so we need a separate element.\n-->\n<div class=\"mat-focus-indicator\"></div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {BooleanInput, coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {SelectionModel} from '@angular/cdk/collections';\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChildren,\n  ElementRef,\n  EventEmitter,\n  InjectionToken,\n  Input,\n  OnDestroy,\n  OnInit,\n  Output,\n  QueryList,\n  ViewChild,\n  ViewEncapsulation,\n  inject,\n} from '@angular/core';\nimport {ThemePalette} from '../core';\nimport {MatListBase, MatListItemBase} from './list-base';\nimport {LIST_OPTION, ListOption, MatListOptionTogglePosition} from './list-option-types';\nimport {MatListItemLine, MatListItemTitle} from './list-item-sections';\nimport {NgTemplateOutlet} from '@angular/common';\nimport {CdkObserveContent} from '@angular/cdk/observers';\n\n/**\n * Injection token that can be used to reference instances of an `SelectionList`. It serves\n * as alternative token to an actual implementation which would result in circular references.\n * @docs-private\n */\nexport const SELECTION_LIST = new InjectionToken<SelectionList>('SelectionList');\n\n/**\n * Interface describing the containing list of a list option. This is used to avoid\n * circular dependencies between the list-option and the selection list.\n * @docs-private\n */\nexport interface SelectionList extends MatListBase {\n  multiple: boolean;\n  color: ThemePalette;\n  selectedOptions: SelectionModel<MatListOption>;\n  hideSingleSelectionIndicator: boolean;\n  compareWith: (o1: any, o2: any) => boolean;\n  _value: string[] | null;\n  _reportValueChange(): void;\n  _emitChangeEvent(options: MatListOption[]): void;\n  _onTouched(): void;\n}\n\n@Component({\n  selector: 'mat-list-option',\n  exportAs: 'matListOption',\n  styleUrl: 'list-option.css',\n  host: {\n    'class': 'mat-mdc-list-item mat-mdc-list-option mdc-list-item',\n    'role': 'option',\n    // As per MDC, only list items without checkbox or radio indicator should receive the\n    // `--selected` class.\n    '[class.mdc-list-item--selected]':\n      'selected && !_selectionList.multiple && _selectionList.hideSingleSelectionIndicator',\n    // Based on the checkbox/radio position and whether there are icons or avatars, we apply MDC's\n    // list-item `--leading` and `--trailing` classes.\n    '[class.mdc-list-item--with-leading-avatar]': '_hasProjected(\"avatars\", \"before\")',\n    '[class.mdc-list-item--with-leading-icon]': '_hasProjected(\"icons\", \"before\")',\n    '[class.mdc-list-item--with-trailing-icon]': '_hasProjected(\"icons\", \"after\")',\n    '[class.mat-mdc-list-option-with-trailing-avatar]': '_hasProjected(\"avatars\", \"after\")',\n    // Based on the checkbox/radio position, we apply the `--leading` or `--trailing` MDC classes\n    // which ensure that the checkbox/radio is positioned correctly within the list item.\n    '[class.mdc-list-item--with-leading-checkbox]': '_hasCheckboxAt(\"before\")',\n    '[class.mdc-list-item--with-trailing-checkbox]': '_hasCheckboxAt(\"after\")',\n    '[class.mdc-list-item--with-leading-radio]': '_hasRadioAt(\"before\")',\n    '[class.mdc-list-item--with-trailing-radio]': '_hasRadioAt(\"after\")',\n\n    // Utility class that makes it easier to target the case where there's both a leading\n    // and a trailing icon. Avoids having to write out all the combinations.\n    '[class.mat-mdc-list-item-both-leading-and-trailing]': '_hasBothLeadingAndTrailing()',\n    '[class.mat-accent]': 'color !== \"primary\" && color !== \"warn\"',\n    '[class.mat-warn]': 'color === \"warn\"',\n    '[class._mat-animation-noopable]': '_noopAnimations',\n    '[attr.aria-selected]': 'selected',\n    '(blur)': '_handleBlur()',\n    '(click)': '_toggleOnInteraction()',\n  },\n  templateUrl: 'list-option.html',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [\n    {provide: MatListItemBase, useExisting: MatListOption},\n    {provide: LIST_OPTION, useExisting: MatListOption},\n  ],\n  imports: [NgTemplateOutlet, CdkObserveContent],\n})\nexport class MatListOption extends MatListItemBase implements ListOption, OnInit, OnDestroy {\n  protected _selectionList = inject<SelectionList>(SELECTION_LIST);\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n\n  @ContentChildren(MatListItemLine, {descendants: true}) _lines: QueryList<MatListItemLine>;\n  @ContentChildren(MatListItemTitle, {descendants: true}) _titles: QueryList<MatListItemTitle>;\n  @ViewChild('unscopedContent') _unscopedContent: ElementRef<HTMLSpanElement>;\n\n  /**\n   * Emits when the selected state of the option has changed.\n   * Use to facilitate two-data binding to the `selected` property.\n   * @docs-private\n   */\n  @Output()\n  readonly selectedChange: EventEmitter<boolean> = new EventEmitter<boolean>();\n\n  /** Whether the label should appear before or after the checkbox/radio. Defaults to 'after' */\n  @Input() togglePosition: MatListOptionTogglePosition = 'after';\n\n  /**\n   * Whether the label should appear before or after the checkbox/radio. Defaults to 'after'\n   *\n   * @deprecated Use `togglePosition` instead.\n   * @breaking-change 17.0.0\n   */\n  @Input() get checkboxPosition(): MatListOptionTogglePosition {\n    return this.togglePosition;\n  }\n  set checkboxPosition(value: MatListOptionTogglePosition) {\n    this.togglePosition = value;\n  }\n\n  /**\n   * Theme color of the list option. This sets the color of the checkbox/radio.\n   * This API is supported in M2 themes only, it has no effect in M3 themes. For color customization\n   * in M3, see https://material.angular.dev/components/list/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  @Input()\n  get color(): ThemePalette {\n    return this._color || this._selectionList.color;\n  }\n  set color(newValue: ThemePalette) {\n    this._color = newValue;\n  }\n  private _color: ThemePalette;\n\n  /** Value of the option */\n  @Input()\n  get value(): any {\n    return this._value;\n  }\n  set value(newValue: any) {\n    if (this.selected && newValue !== this.value && this._inputsInitialized) {\n      this.selected = false;\n    }\n\n    this._value = newValue;\n  }\n  private _value: any;\n\n  /** Whether the option is selected. */\n  @Input()\n  get selected(): boolean {\n    return this._selectionList.selectedOptions.isSelected(this);\n  }\n  set selected(value: BooleanInput) {\n    const isSelected = coerceBooleanProperty(value);\n\n    if (isSelected !== this._selected) {\n      this._setSelected(isSelected);\n\n      if (isSelected || this._selectionList.multiple) {\n        this._selectionList._reportValueChange();\n      }\n    }\n  }\n  private _selected = false;\n\n  /**\n   * This is set to true after the first OnChanges cycle so we don't\n   * clear the value of `selected` in the first cycle.\n   */\n  private _inputsInitialized = false;\n\n  ngOnInit() {\n    const list = this._selectionList;\n\n    if (list._value && list._value.some(value => list.compareWith(this._value, value))) {\n      this._setSelected(true);\n    }\n\n    const wasSelected = this._selected;\n\n    // List options that are selected at initialization can't be reported properly to the form\n    // control. This is because it takes some time until the selection-list knows about all\n    // available options. Also it can happen that the ControlValueAccessor has an initial value\n    // that should be used instead. Deferring the value change report to the next tick ensures\n    // that the form control value is not being overwritten.\n    Promise.resolve().then(() => {\n      if (this._selected || wasSelected) {\n        this.selected = true;\n        this._changeDetectorRef.markForCheck();\n      }\n    });\n    this._inputsInitialized = true;\n  }\n\n  override ngOnDestroy(): void {\n    super.ngOnDestroy();\n\n    if (this.selected) {\n      // We have to delay this until the next tick in order\n      // to avoid changed after checked errors.\n      Promise.resolve().then(() => {\n        this.selected = false;\n      });\n    }\n  }\n\n  /** Toggles the selection state of the option. */\n  toggle(): void {\n    this.selected = !this.selected;\n  }\n\n  /** Allows for programmatic focusing of the option. */\n  focus(): void {\n    this._hostElement.focus();\n  }\n\n  /** Gets the text label of the list option. Used for the typeahead functionality in the list. */\n  getLabel() {\n    const titleElement = this._titles?.get(0)?._elementRef.nativeElement;\n    // If there is no explicit title element, the unscoped text content\n    // is treated as the list item title.\n    const labelEl = titleElement || this._unscopedContent?.nativeElement;\n    return labelEl?.textContent || '';\n  }\n\n  /** Whether a checkbox is shown at the given position. */\n  _hasCheckboxAt(position: MatListOptionTogglePosition): boolean {\n    return this._selectionList.multiple && this._getTogglePosition() === position;\n  }\n\n  /** Where a radio indicator is shown at the given position. */\n  _hasRadioAt(position: MatListOptionTogglePosition): boolean {\n    return (\n      !this._selectionList.multiple &&\n      this._getTogglePosition() === position &&\n      !this._selectionList.hideSingleSelectionIndicator\n    );\n  }\n\n  /** Whether icons or avatars are shown at the given position. */\n  _hasIconsOrAvatarsAt(position: 'before' | 'after'): boolean {\n    return this._hasProjected('icons', position) || this._hasProjected('avatars', position);\n  }\n\n  /** Gets whether the given type of element is projected at the specified position. */\n  _hasProjected(type: 'icons' | 'avatars', position: 'before' | 'after'): boolean {\n    // If the checkbox/radio is shown at the specified position, neither icons or\n    // avatars can be shown at the position.\n    return (\n      this._getTogglePosition() !== position &&\n      (type === 'avatars' ? this._avatars.length !== 0 : this._icons.length !== 0)\n    );\n  }\n\n  _handleBlur() {\n    this._selectionList._onTouched();\n  }\n\n  /** Gets the current position of the checkbox/radio. */\n  _getTogglePosition() {\n    return this.togglePosition || 'after';\n  }\n\n  /**\n   * Sets the selected state of the option.\n   * @returns Whether the value has changed.\n   */\n  _setSelected(selected: boolean): boolean {\n    if (selected === this._selected) {\n      return false;\n    }\n\n    this._selected = selected;\n\n    if (selected) {\n      this._selectionList.selectedOptions.select(this);\n    } else {\n      this._selectionList.selectedOptions.deselect(this);\n    }\n\n    this.selectedChange.emit(selected);\n    this._changeDetectorRef.markForCheck();\n    return true;\n  }\n\n  /**\n   * Notifies Angular that the option needs to be checked in the next change detection run.\n   * Mainly used to trigger an update of the list option if the disabled state of the selection\n   * list changed.\n   */\n  _markForCheck() {\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /** Toggles the option's value based on a user interaction. */\n  _toggleOnInteraction() {\n    if (!this.disabled) {\n      if (this._selectionList.multiple) {\n        this.selected = !this.selected;\n        this._selectionList._emitChangeEvent([this]);\n      } else if (!this.selected) {\n        this.selected = true;\n        this._selectionList._emitChangeEvent([this]);\n      }\n    }\n  }\n\n  /** Sets the tabindex of the list option. */\n  _setTabindex(value: number) {\n    this._hostElement.setAttribute('tabindex', value + '');\n  }\n\n  protected _hasBothLeadingAndTrailing(): boolean {\n    const hasLeading =\n      this._hasProjected('avatars', 'before') ||\n      this._hasProjected('icons', 'before') ||\n      this._hasCheckboxAt('before') ||\n      this._hasRadioAt('before');\n    const hasTrailing =\n      this._hasProjected('icons', 'after') ||\n      this._hasProjected('avatars', 'after') ||\n      this._hasCheckboxAt('after') ||\n      this._hasRadioAt('after');\n    return hasLeading && hasTrailing;\n  }\n}\n", "<!--\n  Save icons and the pseudo checkbox/radio so that they can be re-used in the template without\n  duplication. Also content can only be injected once so we need to extract icons/avatars\n  into a template since we use it in multiple places.\n-->\n<ng-template #icons>\n  <ng-content select=\"[matListItemAvatar],[matListItemIcon]\">\n  </ng-content>\n</ng-template>\n\n<ng-template #checkbox>\n  <div class=\"mdc-checkbox\" [class.mdc-checkbox--disabled]=\"disabled\">\n    <input type=\"checkbox\" class=\"mdc-checkbox__native-control\"\n           [checked]=\"selected\" [disabled]=\"disabled\"/>\n    <div class=\"mdc-checkbox__background\">\n      <svg class=\"mdc-checkbox__checkmark\"\n           viewBox=\"0 0 24 24\"\n           aria-hidden=\"true\">\n        <path class=\"mdc-checkbox__checkmark-path\"\n              fill=\"none\"\n              d=\"M1.73,12.91 8.1,19.28 22.79,4.59\"/>\n      </svg>\n      <div class=\"mdc-checkbox__mixedmark\"></div>\n    </div>\n  </div>\n</ng-template>\n\n<ng-template #radio>\n  <div class=\"mdc-radio\" [class.mdc-radio--disabled]=\"disabled\">\n    <input type=\"radio\" class=\"mdc-radio__native-control\"\n           [checked]=\"selected\" [disabled]=\"disabled\"/>\n    <div class=\"mdc-radio__background\">\n      <div class=\"mdc-radio__outer-circle\"></div>\n      <div class=\"mdc-radio__inner-circle\"></div>\n    </div>\n  </div>\n</ng-template>\n\n@if (_hasCheckboxAt('before')) {\n  <!-- Container for the checkbox at start. -->\n  <span class=\"mdc-list-item__start mat-mdc-list-option-checkbox-before\">\n    <ng-template [ngTemplateOutlet]=\"checkbox\"></ng-template>\n  </span>\n} @else if (_hasRadioAt('before')) {\n  <!-- Container for the radio at the start. -->\n  <span class=\"mdc-list-item__start mat-mdc-list-option-radio-before\">\n    <ng-template [ngTemplateOutlet]=\"radio\"></ng-template>\n  </span>\n}\n<!-- Conditionally renders icons/avatars before the list item text. -->\n@if (_hasIconsOrAvatarsAt('before')) {\n  <ng-template [ngTemplateOutlet]=\"icons\"></ng-template>\n}\n\n<!-- Text -->\n<span class=\"mdc-list-item__content\">\n  <ng-content select=\"[matListItemTitle]\"></ng-content>\n  <ng-content select=\"[matListItemLine]\"></ng-content>\n  <span #unscopedContent class=\"mat-mdc-list-item-unscoped-content\"\n        (cdkObserveContent)=\"_updateItemLines(true)\">\n    <ng-content></ng-content>\n  </span>\n</span>\n\n@if (_hasCheckboxAt('after')) {\n  <!-- Container for the checkbox at the end. -->\n  <span class=\"mdc-list-item__end\">\n    <ng-template [ngTemplateOutlet]=\"checkbox\"></ng-template>\n  </span>\n} @else if (_hasRadioAt('after')) {\n  <!-- Container for the radio at the end. -->\n  <span class=\"mdc-list-item__end\">\n    <ng-template [ngTemplateOutlet]=\"radio\"></ng-template>\n  </span>\n}\n\n<!-- Conditionally renders icons/avatars after the list item text. -->\n@if (_hasIconsOrAvatarsAt('after')) {\n  <ng-template [ngTemplateOutlet]=\"icons\"></ng-template>\n}\n\n<!-- Divider -->\n<ng-content select=\"mat-divider\"></ng-content>\n\n<!--\n  Strong focus indicator element. MDC uses the `::before` pseudo element for the default\n  focus/hover/selected state, so we need a separate element.\n-->\n<div class=\"mat-focus-indicator\"></div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive} from '@angular/core';\n\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\n@Directive({\n  selector: '[mat-subheader], [matSubheader]',\n  // TODO(mmalerba): MDC's subheader font looks identical to the list item font, figure out why and\n  //  make a change in one of the repos to visually distinguish.\n  host: {'class': 'mat-mdc-subheader mdc-list-group__subheader'},\n})\nexport class MatListSubheaderCssMatStyler {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ChangeDetectionStrategy, Component, InjectionToken, ViewEncapsulation} from '@angular/core';\nimport {MatListBase} from './list-base';\n\n/**\n * Injection token that can be used to inject instances of `MatNavList`. It serves as\n * alternative token to the actual `MatNavList` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nexport const MAT_NAV_LIST = new InjectionToken<MatNavList>('MatNavList');\n\n@Component({\n  selector: 'mat-nav-list',\n  exportAs: 'matNavList',\n  template: '<ng-content></ng-content>',\n  host: {\n    'class': 'mat-mdc-nav-list mat-mdc-list-base mdc-list',\n    'role': 'navigation',\n  },\n  styleUrl: 'list.css',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [{provide: MatListBase, useExisting: MatNavList}],\n})\nexport class <PERSON><PERSON><PERSON><PERSON><PERSON> extends MatListBase {\n  // An navigation list is considered interactive, but does not extend the interactive list\n  // base class. We do this because as per MDC, items of interactive lists are only reachable\n  // through keyboard shortcuts. We want all items for the navigation list to be reachable\n  // through tab key as we do not intend to provide any special accessibility treatment. The\n  // accessibility treatment depends on how the end-user will interact with it.\n  override _isNonInteractive = false;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {FocusKeyManager} from '@angular/cdk/a11y';\nimport {BooleanInput, coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {SelectionModel} from '@angular/cdk/collections';\nimport {A, ENTER, SPACE, hasModifierKey} from '@angular/cdk/keycodes';\nimport {_getFocusedElementPierceShadowDom} from '@angular/cdk/platform';\nimport {\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChildren,\n  ElementRef,\n  EventEmitter,\n  Input,\n  NgZone,\n  OnChanges,\n  OnDestroy,\n  Output,\n  QueryList,\n  Renderer2,\n  SimpleChanges,\n  ViewEncapsulation,\n  forwardRef,\n  inject,\n} from '@angular/core';\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from '@angular/forms';\nimport {ThemePalette} from '../core';\nimport {Subject} from 'rxjs';\nimport {takeUntil} from 'rxjs/operators';\nimport {MatListBase} from './list-base';\nimport {MatListOption, SELECTION_LIST, SelectionList} from './list-option';\n\nexport const MAT_SELECTION_LIST_VALUE_ACCESSOR: any = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatSelectionList),\n  multi: true,\n};\n\n/** Change event that is being fired whenever the selected state of an option changes. */\nexport class MatSelectionListChange {\n  constructor(\n    /** Reference to the selection list that emitted the event. */\n    public source: MatSelectionList,\n    /** Reference to the options that have been changed. */\n    public options: MatListOption[],\n  ) {}\n}\n\n@Component({\n  selector: 'mat-selection-list',\n  exportAs: 'matSelectionList',\n  host: {\n    'class': 'mat-mdc-selection-list mat-mdc-list-base mdc-list',\n    'role': 'listbox',\n    '[attr.aria-multiselectable]': 'multiple',\n    '(keydown)': '_handleKeydown($event)',\n  },\n  template: '<ng-content></ng-content>',\n  styleUrl: 'list.css',\n  encapsulation: ViewEncapsulation.None,\n  providers: [\n    MAT_SELECTION_LIST_VALUE_ACCESSOR,\n    {provide: MatListBase, useExisting: MatSelectionList},\n    {provide: SELECTION_LIST, useExisting: MatSelectionList},\n  ],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class MatSelectionList\n  extends MatListBase\n  implements SelectionList, ControlValueAccessor, AfterViewInit, OnChanges, OnDestroy\n{\n  _element = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _ngZone = inject(NgZone);\n  private _renderer = inject(Renderer2);\n\n  private _initialized = false;\n  private _keyManager: FocusKeyManager<MatListOption>;\n  private _listenerCleanups: (() => void)[] | undefined;\n\n  /** Emits when the list has been destroyed. */\n  private _destroyed = new Subject<void>();\n\n  /** Whether the list has been destroyed. */\n  private _isDestroyed: boolean;\n\n  /** View to model callback that should be called whenever the selected options change. */\n  private _onChange: (value: any) => void = (_: any) => {};\n\n  @ContentChildren(MatListOption, {descendants: true}) _items: QueryList<MatListOption>;\n\n  /** Emits a change event whenever the selected state of an option changes. */\n  @Output() readonly selectionChange: EventEmitter<MatSelectionListChange> =\n    new EventEmitter<MatSelectionListChange>();\n\n  /**\n   * Theme color of the selection list. This sets the checkbox color for all\n   * list options. This API is supported in M2 themes only, it has no effect in\n   * M3 themes. For color customization in M3, see https://material.angular.dev/components/list/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  @Input() color: ThemePalette = 'accent';\n\n  /**\n   * Function used for comparing an option against the selected value when determining which\n   * options should appear as selected. The first argument is the value of an options. The second\n   * one is a value from the selected value. A boolean must be returned.\n   */\n  @Input() compareWith: (o1: any, o2: any) => boolean = (a1, a2) => a1 === a2;\n\n  /** Whether selection is limited to one or multiple items (default multiple). */\n  @Input()\n  get multiple(): boolean {\n    return this._multiple;\n  }\n  set multiple(value: BooleanInput) {\n    const newValue = coerceBooleanProperty(value);\n\n    if (newValue !== this._multiple) {\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && this._initialized) {\n        throw new Error(\n          'Cannot change `multiple` mode of mat-selection-list after initialization.',\n        );\n      }\n\n      this._multiple = newValue;\n      this.selectedOptions = new SelectionModel(this._multiple, this.selectedOptions.selected);\n    }\n  }\n  private _multiple = true;\n\n  /** Whether radio indicator for all list items is hidden. */\n  @Input()\n  get hideSingleSelectionIndicator(): boolean {\n    return this._hideSingleSelectionIndicator;\n  }\n  set hideSingleSelectionIndicator(value: BooleanInput) {\n    this._hideSingleSelectionIndicator = coerceBooleanProperty(value);\n  }\n  private _hideSingleSelectionIndicator: boolean =\n    this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n\n  /** The currently selected options. */\n  selectedOptions = new SelectionModel<MatListOption>(this._multiple);\n\n  /** Keeps track of the currently-selected value. */\n  _value: string[] | null;\n\n  /** View to model callback that should be called if the list or its options lost focus. */\n  _onTouched: () => void = () => {};\n\n  private readonly _changeDetectorRef = inject(ChangeDetectorRef);\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n    this._isNonInteractive = false;\n  }\n\n  ngAfterViewInit() {\n    // Mark the selection list as initialized so that the `multiple`\n    // binding can no longer be changed.\n    this._initialized = true;\n    this._setupRovingTabindex();\n\n    // These events are bound outside the zone, because they don't change\n    // any change-detected properties and they can trigger timeouts.\n    this._ngZone.runOutsideAngular(() => {\n      this._listenerCleanups = [\n        this._renderer.listen(this._element.nativeElement, 'focusin', this._handleFocusin),\n        this._renderer.listen(this._element.nativeElement, 'focusout', this._handleFocusout),\n      ];\n    });\n\n    if (this._value) {\n      this._setOptionsFromValues(this._value);\n    }\n\n    this._watchForSelectionChange();\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    const disabledChanges = changes['disabled'];\n    const disableRippleChanges = changes['disableRipple'];\n    const hideSingleSelectionIndicatorChanges = changes['hideSingleSelectionIndicator'];\n\n    if (\n      (disableRippleChanges && !disableRippleChanges.firstChange) ||\n      (disabledChanges && !disabledChanges.firstChange) ||\n      (hideSingleSelectionIndicatorChanges && !hideSingleSelectionIndicatorChanges.firstChange)\n    ) {\n      this._markOptionsForCheck();\n    }\n  }\n\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this._listenerCleanups?.forEach(current => current());\n    this._destroyed.next();\n    this._destroyed.complete();\n    this._isDestroyed = true;\n  }\n\n  /** Focuses the selection list. */\n  focus(options?: FocusOptions) {\n    this._element.nativeElement.focus(options);\n  }\n\n  /** Selects all of the options. Returns the options that changed as a result. */\n  selectAll(): MatListOption[] {\n    return this._setAllOptionsSelected(true);\n  }\n\n  /** Deselects all of the options. Returns the options that changed as a result. */\n  deselectAll(): MatListOption[] {\n    return this._setAllOptionsSelected(false);\n  }\n\n  /** Reports a value change to the ControlValueAccessor */\n  _reportValueChange() {\n    // Stop reporting value changes after the list has been destroyed. This avoids\n    // cases where the list might wrongly reset its value once it is removed, but\n    // the form control is still live.\n    if (this.options && !this._isDestroyed) {\n      const value = this._getSelectedOptionValues();\n      this._onChange(value);\n      this._value = value;\n    }\n  }\n\n  /** Emits a change event if the selected state of an option changed. */\n  _emitChangeEvent(options: MatListOption[]) {\n    this.selectionChange.emit(new MatSelectionListChange(this, options));\n  }\n\n  /** Implemented as part of ControlValueAccessor. */\n  writeValue(values: string[]): void {\n    this._value = values;\n\n    if (this.options) {\n      this._setOptionsFromValues(values || []);\n    }\n  }\n\n  /** Implemented as a part of ControlValueAccessor. */\n  setDisabledState(isDisabled: boolean): void {\n    this.disabled = isDisabled;\n    this._changeDetectorRef.markForCheck();\n    this._markOptionsForCheck();\n  }\n\n  /**\n   * Whether the *entire* selection list is disabled. When true, each list item is also disabled\n   * and each list item is removed from the tab order (has tabindex=\"-1\").\n   */\n  @Input()\n  override get disabled(): boolean {\n    return this._selectionListDisabled;\n  }\n  override set disabled(value: BooleanInput) {\n    // Update the disabled state of this list. Write to `this._selectionListDisabled` instead of\n    // `super.disabled`. That is to avoid closure compiler compatibility issues with assigning to\n    // a super property.\n    this._selectionListDisabled = coerceBooleanProperty(value);\n    if (this._selectionListDisabled) {\n      this._keyManager?.setActiveItem(-1);\n    }\n  }\n  private _selectionListDisabled = false;\n\n  /** Implemented as part of ControlValueAccessor. */\n  registerOnChange(fn: (value: any) => void): void {\n    this._onChange = fn;\n  }\n\n  /** Implemented as part of ControlValueAccessor. */\n  registerOnTouched(fn: () => void): void {\n    this._onTouched = fn;\n  }\n\n  /** Watches for changes in the selected state of the options and updates the list accordingly. */\n  private _watchForSelectionChange() {\n    this.selectedOptions.changed.pipe(takeUntil(this._destroyed)).subscribe(event => {\n      // Sync external changes to the model back to the options.\n      for (let item of event.added) {\n        item.selected = true;\n      }\n\n      for (let item of event.removed) {\n        item.selected = false;\n      }\n\n      if (!this._containsFocus()) {\n        this._resetActiveOption();\n      }\n    });\n  }\n\n  /** Sets the selected options based on the specified values. */\n  private _setOptionsFromValues(values: string[]) {\n    this.options.forEach(option => option._setSelected(false));\n\n    values.forEach(value => {\n      const correspondingOption = this.options.find(option => {\n        // Skip options that are already in the model. This allows us to handle cases\n        // where the same primitive value is selected multiple times.\n        return option.selected ? false : this.compareWith(option.value, value);\n      });\n\n      if (correspondingOption) {\n        correspondingOption._setSelected(true);\n      }\n    });\n  }\n\n  /** Returns the values of the selected options. */\n  private _getSelectedOptionValues(): string[] {\n    return this.options.filter(option => option.selected).map(option => option.value);\n  }\n\n  /** Marks all the options to be checked in the next change detection run. */\n  private _markOptionsForCheck() {\n    if (this.options) {\n      this.options.forEach(option => option._markForCheck());\n    }\n  }\n\n  /**\n   * Sets the selected state on all of the options\n   * and emits an event if anything changed.\n   */\n  private _setAllOptionsSelected(isSelected: boolean, skipDisabled?: boolean): MatListOption[] {\n    // Keep track of whether anything changed, because we only want to\n    // emit the changed event when something actually changed.\n    const changedOptions: MatListOption[] = [];\n\n    this.options.forEach(option => {\n      if ((!skipDisabled || !option.disabled) && option._setSelected(isSelected)) {\n        changedOptions.push(option);\n      }\n    });\n\n    if (changedOptions.length) {\n      this._reportValueChange();\n    }\n\n    return changedOptions;\n  }\n\n  // Note: This getter exists for backwards compatibility. The `_items` query list\n  // cannot be named `options` as it will be picked up by the interactive list base.\n  /** The option components contained within this selection-list. */\n  get options(): QueryList<MatListOption> {\n    return this._items;\n  }\n\n  /** Handles keydown events within the list. */\n  _handleKeydown(event: KeyboardEvent) {\n    const activeItem = this._keyManager.activeItem;\n\n    if (\n      (event.keyCode === ENTER || event.keyCode === SPACE) &&\n      !this._keyManager.isTyping() &&\n      activeItem &&\n      !activeItem.disabled\n    ) {\n      event.preventDefault();\n      activeItem._toggleOnInteraction();\n    } else if (\n      event.keyCode === A &&\n      this.multiple &&\n      !this._keyManager.isTyping() &&\n      hasModifierKey(event, 'ctrlKey', 'metaKey')\n    ) {\n      const shouldSelect = this.options.some(option => !option.disabled && !option.selected);\n      event.preventDefault();\n      this._emitChangeEvent(this._setAllOptionsSelected(shouldSelect, true));\n    } else {\n      this._keyManager.onKeydown(event);\n    }\n  }\n\n  /** Handles focusout events within the list. */\n  private _handleFocusout = () => {\n    // Focus takes a while to update so we have to wrap our call in a timeout.\n    setTimeout(() => {\n      if (!this._containsFocus()) {\n        this._resetActiveOption();\n      }\n    });\n  };\n\n  /** Handles focusin events within the list. */\n  private _handleFocusin = (event: FocusEvent) => {\n    if (this.disabled) {\n      return;\n    }\n\n    const activeIndex = this._items\n      .toArray()\n      .findIndex(item => item._elementRef.nativeElement.contains(event.target as HTMLElement));\n\n    if (activeIndex > -1) {\n      this._setActiveOption(activeIndex);\n    } else {\n      this._resetActiveOption();\n    }\n  };\n\n  /**\n   * Sets up the logic for maintaining the roving tabindex.\n   *\n   * `skipPredicate` determines if key manager should avoid putting a given list item in the tab\n   * index. Allow disabled list items to receive focus to align with WAI ARIA recommendation.\n   * Normally WAI ARIA's instructions are to exclude disabled items from the tab order, but it\n   * makes a few exceptions for compound widgets.\n   *\n   * From [Developing a Keyboard Interface](\n   * https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n   *   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n   *   Listbox...\"\n   */\n  private _setupRovingTabindex() {\n    this._keyManager = new FocusKeyManager(this._items)\n      .withHomeAndEnd()\n      .withTypeAhead()\n      .withWrap()\n      .skipPredicate(() => this.disabled);\n\n    // Set the initial focus.\n    this._resetActiveOption();\n\n    // Move the tabindex to the currently-focused list item.\n    this._keyManager.change.subscribe(activeItemIndex => this._setActiveOption(activeItemIndex));\n\n    // If the active item is removed from the list, reset back to the first one.\n    this._items.changes.pipe(takeUntil(this._destroyed)).subscribe(() => {\n      const activeItem = this._keyManager.activeItem;\n\n      if (!activeItem || this._items.toArray().indexOf(activeItem) === -1) {\n        this._resetActiveOption();\n      }\n    });\n  }\n\n  /**\n   * Sets an option as active.\n   * @param index Index of the active option. If set to -1, no option will be active.\n   */\n  private _setActiveOption(index: number) {\n    this._items.forEach((item, itemIndex) => item._setTabindex(itemIndex === index ? 0 : -1));\n    this._keyManager.updateActiveItem(index);\n  }\n\n  /**\n   * Resets the active option. When the list is disabled, remove all options from to the tab order.\n   * Otherwise, focus the first selected option.\n   */\n  private _resetActiveOption() {\n    if (this.disabled) {\n      this._setActiveOption(-1);\n      return;\n    }\n\n    const activeItem =\n      this._items.find(item => item.selected && !item.disabled) || this._items.first;\n    this._setActiveOption(activeItem ? this._items.toArray().indexOf(activeItem) : -1);\n  }\n\n  /** Returns whether the focus is currently within the list. */\n  private _containsFocus() {\n    const activeElement = _getFocusedElementPierceShadowDom();\n    return activeElement && this._element.nativeElement.contains(activeElement);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatPseudoCheckboxModule, MatRippleModule, MatCommonModule} from '../core';\nimport {MatDividerModule, MatDivider} from '../divider';\nimport {MatActionList} from './action-list';\nimport {MatList, MatListItem} from './list';\nimport {MatListOption} from './list-option';\nimport {MatListSubheaderCssMatStyler} from './subheader';\nimport {\n  MatListItemLine,\n  MatListItemTitle,\n  MatListItemMeta,\n  MatListItemAvatar,\n  MatListItemIcon,\n} from './list-item-sections';\nimport {MatNavList} from './nav-list';\nimport {MatSelectionList} from './selection-list';\nimport {ObserversModule} from '@angular/cdk/observers';\n\n// Export required to fix compiler confusion about import module paths\nexport {MatDivider};\n\n@NgModule({\n  imports: [\n    ObserversModule,\n    MatCommonModule,\n    MatRippleModule,\n    MatPseudoCheckboxModule,\n    MatList,\n    MatActionList,\n    MatNavList,\n    MatSelectionList,\n    MatListItem,\n    MatListOption,\n    MatListSubheaderCssMatStyler,\n    MatListItemAvatar,\n    MatListItemIcon,\n    MatListItemLine,\n    MatListItemTitle,\n    MatListItemMeta,\n  ],\n  exports: [\n    MatList,\n    MatActionList,\n    MatNavList,\n    MatSelectionList,\n    MatListItem,\n    MatListOption,\n    MatListItemAvatar,\n    MatListItemIcon,\n    MatListSubheaderCssMatStyler,\n    MatDividerModule,\n    MatListItemLine,\n    MatListItemTitle,\n    MatListItemMeta,\n  ],\n})\nexport class MatListModule {}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAyBA;;;;;AAKG;AACI,MAAM,WAAW,GAAG,IAAI,cAAc,CAAa,YAAY,CAAC;;ACpBvE;;;;;AAKG;MAKU,gBAAgB,CAAA;AAC3B,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AAGzD,IAAA,WAAA,GAAA;uGAJW,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAhB,gBAAgB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,qDAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAhB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAJ5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oBAAoB;AAC9B,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,qDAAqD,EAAC;AACvE,iBAAA;;AAQD;;;;;AAKG;MAKU,eAAe,CAAA;AAC1B,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AAGzD,IAAA,WAAA,GAAA;uGAJW,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAf,eAAe,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,sDAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAJ3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,mBAAmB;AAC7B,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,sDAAsD,EAAC;AACxE,iBAAA;;AAQD;;;;;AAKG;MAKU,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAf,eAAe,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,2CAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAJ3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,mBAAmB;AAC7B,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,2CAA2C,EAAC;AAC7D,iBAAA;;AAGD;;;;;;;AAOG;MAWU,uBAAuB,CAAA;IAClC,WAAW,GAAG,MAAM,CAAa,WAAW,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAG/D,IAAA,WAAA,GAAA;IAEA,iBAAiB,GAAA;;;AAGf,QAAA,OAAO,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,EAAE,kBAAkB,EAAE,KAAK,OAAO;;uGATrE,uBAAuB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAvB,uBAAuB,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,4BAAA,EAAA,qBAAA,EAAA,0BAAA,EAAA,sBAAA,EAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAvB,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBAVnC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,IAAI,EAAE;;;;;AAKJ,wBAAA,8BAA8B,EAAE,qBAAqB;AACrD,wBAAA,4BAA4B,EAAE,sBAAsB;AACrD,qBAAA;AACF,iBAAA;;AAcD;;;;AAIG;AAKG,MAAO,iBAAkB,SAAQ,uBAAuB,CAAA;uGAAjD,iBAAiB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAjB,iBAAiB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,0BAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAjB,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAJ7B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,0BAA0B,EAAC;AAC5C,iBAAA;;AAGD;;;;AAIG;AAKG,MAAO,eAAgB,SAAQ,uBAAuB,CAAA;uGAA/C,eAAe,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAf,eAAe,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,wBAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAJ3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,mBAAmB;AAC7B,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,wBAAwB,EAAC;AAC1C,iBAAA;;;AC3FD;MACa,eAAe,GAAG,IAAI,cAAc,CAAgB,iBAAiB;;AC6BlF;MACsB,WAAW,CAAA;IAC/B,iBAAiB,GAAY,IAAI;;AAGjC,IAAA,IACI,aAAa,GAAA;QACf,OAAO,IAAI,CAAC,cAAc;;IAE5B,IAAI,aAAa,CAAC,KAAmB,EAAA;AACnC,QAAA,IAAI,CAAC,cAAc,GAAG,qBAAqB,CAAC,KAAK,CAAC;;IAE5C,cAAc,GAAY,KAAK;AAEvC;;;AAGG;AACH,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,KAAmB,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,KAAK,CAAC;;IAEvC,SAAS,GAAG,KAAK;IAEf,eAAe,GAAG,MAAM,CAAC,eAAe,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;uGA1BjD,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAX,WAAW,EAAA,YAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,aAAA,EAAA,eAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,oBAAA,EAAA,UAAA,EAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAX,WAAW,EAAA,UAAA,EAAA,CAAA;kBANhC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,IAAI,EAAE;AACJ,wBAAA,sBAAsB,EAAE,UAAU;AACnC,qBAAA;AACF,iBAAA;8BAOK,aAAa,EAAA,CAAA;sBADhB;gBAcG,QAAQ,EAAA,CAAA;sBADX;;AAmBH;MACsB,eAAe,CAAA;AACnC,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AAC/C,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;IAC1B,SAAS,GAAG,MAAM,CAAC,WAAW,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AACjD,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;;AAiBpC,IAAA,YAAY;;AAGZ,IAAA,gBAAgB;;AAGhB,IAAA,eAAe;AAE2C,IAAA,QAAQ;AACV,IAAA,MAAM;AAE9D;;;;;;;;;AASG;IACH,IACI,KAAK,CAAC,KAA6B,EAAA;QACrC,IAAI,CAAC,cAAc,GAAG,oBAAoB,CAAC,KAAK,EAAE,IAAI,CAAC;AACvD,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;;IAE9B,cAAc,GAAkB,IAAI;;AAGpC,IAAA,IACI,aAAa,GAAA;QACf,QACE,IAAI,CAAC,QAAQ;AACb,YAAA,IAAI,CAAC,cAAc;AACnB,YAAA,IAAI,CAAC,eAAe;AACpB,YAAA,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa;;IAGnC,IAAI,aAAa,CAAC,KAAmB,EAAA;AACnC,QAAA,IAAI,CAAC,cAAc,GAAG,qBAAqB,CAAC,KAAK,CAAC;;IAE5C,cAAc,GAAY,KAAK;;AAGvC,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ;;IAErD,IAAI,QAAQ,CAAC,KAAmB,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,KAAK,CAAC;;IAEvC,SAAS,GAAG,KAAK;AAEjB,IAAA,cAAc,GAAG,IAAI,YAAY,EAAE;IACnC,eAAe,GAA0B,IAAI;;IAGrD,uBAAuB,GAAY,KAAK;AAExC;;;AAGG;AACH,IAAA,YAAY;AAEZ;;;AAGG;AACH,IAAA,IAAI,cAAc,GAAA;QAChB,OAAO,IAAI,CAAC,aAAa,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ;;AAK3D,IAAA,WAAA,GAAA;QACE,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC;AAC5D,QAAA,MAAM,mBAAmB,GAAG,MAAM,CAAsB,yBAAyB,EAAE;AACjF,YAAA,QAAQ,EAAE,IAAI;AACf,SAAA,CAAC;AACF,QAAA,MAAM,aAAa,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAErE,QAAA,IAAI,CAAC,YAAY,GAAG,mBAAmB,IAAI,EAAE;QAC7C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;AAClD,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,QAAQ;AAC7E,QAAA,IAAI,CAAC,eAAe,GAAG,aAAa,KAAK,gBAAgB;QAEzD,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE;YACvD,IAAI,CAAC,wBAAwB,EAAE;;;;;AAMjC,QAAA,IAAI,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE;YACpE,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;;;IAIpD,eAAe,GAAA;QACb,IAAI,CAAC,8BAA8B,EAAE;AACrC,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;;IAG7B,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE;AACjC,QAAA,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,EAAE;AACjC,YAAA,IAAI,CAAC,eAAe,CAAC,oBAAoB,EAAE;;;;IAK/C,gBAAgB,GAAA;AACd,QAAA,OAAO,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;;IAG/C,wBAAwB,GAAA;QAC9B,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,+BAA+B,CAAC;QAChE,IAAI,CAAC,eAAe,GAAG,IAAI,cAAc,CACvC,IAAI,EACJ,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,SAAS,EACd,MAAM,CAAC,QAAQ,CAAC,CACjB;QACD,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC;;AAG5D;;;AAGG;IACK,8BAA8B,GAAA;AACpC,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CACrB,KAAK,CAAC,IAAI,CAAC,MAAO,CAAC,OAAO,EAAE,IAAI,CAAC,OAAQ,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,MAC3D,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAC7B,CACF;AACH,SAAC,CAAC;;AAGJ;;;;;;;;;;AAUG;AACH,IAAA,gBAAgB,CAAC,sBAA+B,EAAA;;;AAG9C,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC3D;;;;;QAMF,IAAI,sBAAsB,EAAE;YAC1B,IAAI,CAAC,+BAA+B,EAAE;;;;AAKxC,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;YACjD,0BAA0B,CAAC,IAAI,CAAC;;QAGlC,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,sBAAsB,EAAE;AAC1E,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa;;AAG7D,QAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,+BAA+B,EAAE,aAAa,IAAI,CAAC,CAAC;AACvF,QAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,8BAA8B,EAAE,aAAa,IAAI,CAAC,CAAC;AACtF,QAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,+BAA+B,EAAE,aAAa,KAAK,CAAC,CAAC;AACxF,QAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,iCAAiC,EAAE,aAAa,KAAK,CAAC,CAAC;;;AAI1F,QAAA,IAAI,IAAI,CAAC,uBAAuB,EAAE;AAChC,YAAA,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,IAAI,aAAa,KAAK,CAAC;YACrE,iBAAiB,CAAC,SAAS,CAAC,MAAM,CAAC,6BAA6B,EAAE,YAAY,CAAC;YAC/E,iBAAiB,CAAC,SAAS,CAAC,MAAM,CAAC,+BAA+B,EAAE,CAAC,YAAY,CAAC;;aAC7E;AACL,YAAA,iBAAiB,CAAC,SAAS,CAAC,MAAM,CAAC,6BAA6B,CAAC;AACjE,YAAA,iBAAiB,CAAC,SAAS,CAAC,MAAM,CAAC,+BAA+B,CAAC;;;AAIvE;;;;;;;AAOG;IACK,sBAAsB,GAAA;AAC5B,QAAA,IAAI,UAAU,GAAG,IAAI,CAAC,OAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,MAAO,CAAC,MAAM;AAC3D,QAAA,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAChC,UAAU,IAAI,CAAC;;AAEjB,QAAA,OAAO,UAAU;;;IAIX,+BAA+B,GAAA;AACrC,QAAA,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC,IAAI,CACvC,IAAI,CAAC,gBAAiB,CAAC,aAAa,CAAC,UAAU;AAE9C,aAAA,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY;aAClD,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;;uGA7O9C,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAf,eAAe,EAAA,YAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,aAAA,EAAA,eAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,+BAAA,EAAA,UAAA,EAAA,oBAAA,EAAA,UAAA,EAAA,eAAA,EAAA,wCAAA,EAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,UAAA,EAAA,SAAA,EA6BlB,iBAAiB,EAAA,EAAA,EAAA,YAAA,EAAA,QAAA,EAAA,SAAA,EACjB,eAAe,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FA9BZ,eAAe,EAAA,UAAA,EAAA,CAAA;kBARpC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,IAAI,EAAE;AACJ,wBAAA,iCAAiC,EAAE,UAAU;AAC7C,wBAAA,sBAAsB,EAAE,UAAU;AAClC,wBAAA,iBAAiB,EAAE,wCAAwC;AAC5D,qBAAA;AACF,iBAAA;wDA+B2D,QAAQ,EAAA,CAAA;sBAAjE,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,iBAAiB,EAAE,EAAC,WAAW,EAAE,KAAK,EAAC;gBACA,MAAM,EAAA,CAAA;sBAA7D,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,eAAe,EAAE,EAAC,WAAW,EAAE,KAAK,EAAC;gBAalD,KAAK,EAAA,CAAA;sBADR;gBASG,aAAa,EAAA,CAAA;sBADhB;gBAgBG,QAAQ,EAAA,CAAA;sBADX;;AAgLH;;;;;;AAMG;AACH,SAAS,0BAA0B,CAAC,IAAqB,EAAA;AACvD,IAAA,MAAM,SAAS,GAAG,IAAI,CAAC,OAAQ,CAAC,MAAM;AACtC,IAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAO,CAAC,MAAM;AAEpC,IAAA,IAAI,SAAS,GAAG,CAAC,EAAE;AACjB,QAAA,OAAO,CAAC,IAAI,CAAC,0CAA0C,CAAC;;IAE1D,IAAI,SAAS,KAAK,CAAC,IAAI,QAAQ,GAAG,CAAC,EAAE;AACnC,QAAA,OAAO,CAAC,IAAI,CAAC,kEAAkE,CAAC;;IAElF,IACE,SAAS,KAAK,CAAC;AACf,QAAA,IAAI,CAAC,uBAAuB;QAC5B,IAAI,CAAC,cAAc,KAAK,IAAI;AAC5B,QAAA,IAAI,CAAC,cAAc,GAAG,CAAC,EACvB;AACA,QAAA,OAAO,CAAC,IAAI,CAAC,2DAA2D,CAAC;;AAE3E,IAAA,IAAI,QAAQ,GAAG,CAAC,KAAK,QAAQ,KAAK,CAAC,IAAI,IAAI,CAAC,uBAAuB,CAAC,EAAE;AACpE,QAAA,OAAO,CAAC,IAAI,CAAC,8CAA8C,CAAC;;AAEhE;;ACzUM,MAAO,aAAc,SAAQ,WAAW,CAAA;;;;;;IAMnC,iBAAiB,GAAG,KAAK;uGANvB,aAAa,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,EAFb,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,OAAA,EAAA,EAAA,cAAA,EAAA,gDAAA,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,aAAa,EAAC,CAAC,8EARrD,2BAA2B,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,omjBAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAU1B,aAAa,EAAA,UAAA,EAAA,CAAA;kBAbzB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,iBAAiB,EACjB,QAAA,EAAA,eAAe,EACf,QAAA,EAAA,2BAA2B,EAC/B,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,gDAAgD;AACzD,wBAAA,MAAM,EAAE,OAAO;AAChB,qBAAA,EAAA,aAAA,EAEc,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,SAAA,EACpC,CAAC,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAA,aAAe,EAAC,CAAC,EAAA,MAAA,EAAA,CAAA,omjBAAA,CAAA,EAAA;;;ACEjE;;;;AAIG;MACU,QAAQ,GAAG,IAAI,cAAc,CAAU,SAAS;AAcvD,MAAO,OAAQ,SAAQ,WAAW,CAAA;uGAA3B,OAAO,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAP,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,OAAO,EAFP,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,UAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,yCAAA,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAC,CAAC,wEAP/C,2BAA2B,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,omjBAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAS1B,OAAO,EAAA,UAAA,EAAA,CAAA;kBAZnB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,UAAU,EACV,QAAA,EAAA,SAAS,EACT,QAAA,EAAA,2BAA2B,EAC/B,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,yCAAyC;AACnD,qBAAA,EAAA,aAAA,EAEc,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,SAAA,EACpC,CAAC,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAA,OAAS,EAAC,CAAC,EAAA,MAAA,EAAA,CAAA,omjBAAA,CAAA,EAAA;;AAwBrD,MAAO,WAAY,SAAQ,eAAe,CAAA;AACS,IAAA,MAAM;AACL,IAAA,OAAO;AACR,IAAA,KAAK;AAC9B,IAAA,gBAAgB;AAC3B,IAAA,SAAS;;AAG5B,IAAA,IACI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU;;IAExB,IAAI,SAAS,CAAC,SAAS,EAAA;AACrB,QAAA,IAAI,CAAC,UAAU,GAAG,qBAAqB,CAAC,SAAS,CAAC;;IAEpD,UAAU,GAAG,KAAK;AAElB;;;AAGG;IACH,eAAe,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,KAAK,GAAG,IAAI,IAAI,CAAC,UAAU,GAAG,MAAM,GAAG,IAAI;;IAGpE,0BAA0B,GAAA;QAClC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC;;uGA1BjF,WAAW,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAX,WAAW,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,wDAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,gCAAA,EAAA,WAAA,EAAA,0CAAA,EAAA,uBAAA,EAAA,wCAAA,EAAA,qBAAA,EAAA,yCAAA,EAAA,oBAAA,EAAA,mDAAA,EAAA,8BAAA,EAAA,+BAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,mBAAA,EAAA,EAAA,cAAA,EAAA,iCAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,QAAA,EAAA,SAAA,EACL,eAAe,EACf,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,SAAA,EAAA,SAAA,EAAA,gBAAgB,2DAChB,eAAe,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,WAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,MAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,aAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECpElC,+tBAoBA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,ED2CY,iBAAiB,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,CAAA,2BAAA,EAAA,UAAA,CAAA,EAAA,OAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEhB,WAAW,EAAA,UAAA,EAAA,CAAA;kBApBvB,SAAS;+BACE,wDAAwD,EAAA,QAAA,EACxD,aAAa,EACjB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,iCAAiC;AAC1C,wBAAA,kCAAkC,EAAE,WAAW;AAC/C,wBAAA,4CAA4C,EAAE,uBAAuB;AACrE,wBAAA,0CAA0C,EAAE,qBAAqB;AACjE,wBAAA,2CAA2C,EAAE,oBAAoB;;;AAGjE,wBAAA,qDAAqD,EAAE,8BAA8B;AACrF,wBAAA,iCAAiC,EAAE,iBAAiB;AACpD,wBAAA,qBAAqB,EAAE,mBAAmB;qBAC3C,EAEc,aAAA,EAAA,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,OAAA,EACtC,CAAC,iBAAiB,CAAC,EAAA,QAAA,EAAA,+tBAAA,EAAA;8BAG2B,MAAM,EAAA,CAAA;sBAA5D,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,eAAe,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;gBACG,OAAO,EAAA,CAAA;sBAA9D,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,gBAAgB,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;gBACC,KAAK,EAAA,CAAA;sBAA3D,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,eAAe,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;gBACvB,gBAAgB,EAAA,CAAA;sBAA7C,SAAS;uBAAC,iBAAiB;gBACT,SAAS,EAAA,CAAA;sBAA3B,SAAS;uBAAC,MAAM;gBAIb,SAAS,EAAA,CAAA;sBADZ;;;AEvCH;;;;AAIG;MACU,cAAc,GAAG,IAAI,cAAc,CAAgB,eAAe;AA8DzE,MAAO,aAAc,SAAQ,eAAe,CAAA;AACtC,IAAA,cAAc,GAAG,MAAM,CAAgB,cAAc,CAAC;AACxD,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAEC,IAAA,MAAM;AACL,IAAA,OAAO;AACjC,IAAA,gBAAgB;AAE9C;;;;AAIG;AAEM,IAAA,cAAc,GAA0B,IAAI,YAAY,EAAW;;IAGnE,cAAc,GAAgC,OAAO;AAE9D;;;;;AAKG;AACH,IAAA,IAAa,gBAAgB,GAAA;QAC3B,OAAO,IAAI,CAAC,cAAc;;IAE5B,IAAI,gBAAgB,CAAC,KAAkC,EAAA;AACrD,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK;;AAG7B;;;;;;;AAOG;AACH,IAAA,IACI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK;;IAEjD,IAAI,KAAK,CAAC,QAAsB,EAAA;AAC9B,QAAA,IAAI,CAAC,MAAM,GAAG,QAAQ;;AAEhB,IAAA,MAAM;;AAGd,IAAA,IACI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,MAAM;;IAEpB,IAAI,KAAK,CAAC,QAAa,EAAA;AACrB,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,QAAQ,KAAK,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,kBAAkB,EAAE;AACvE,YAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;;AAGvB,QAAA,IAAI,CAAC,MAAM,GAAG,QAAQ;;AAEhB,IAAA,MAAM;;AAGd,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC;;IAE7D,IAAI,QAAQ,CAAC,KAAmB,EAAA;AAC9B,QAAA,MAAM,UAAU,GAAG,qBAAqB,CAAC,KAAK,CAAC;AAE/C,QAAA,IAAI,UAAU,KAAK,IAAI,CAAC,SAAS,EAAE;AACjC,YAAA,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;YAE7B,IAAI,UAAU,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE;AAC9C,gBAAA,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE;;;;IAItC,SAAS,GAAG,KAAK;AAEzB;;;AAGG;IACK,kBAAkB,GAAG,KAAK;IAElC,QAAQ,GAAA;AACN,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc;QAEhC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,EAAE;AAClF,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;;AAGzB,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS;;;;;;AAOlC,QAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;AAC1B,YAAA,IAAI,IAAI,CAAC,SAAS,IAAI,WAAW,EAAE;AACjC,gBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;AACpB,gBAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;AAE1C,SAAC,CAAC;AACF,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI;;IAGvB,WAAW,GAAA;QAClB,KAAK,CAAC,WAAW,EAAE;AAEnB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;;;AAGjB,YAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;AAC1B,gBAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;AACvB,aAAC,CAAC;;;;IAKN,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ;;;IAIhC,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;;;IAI3B,QAAQ,GAAA;AACN,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,aAAa;;;QAGpE,MAAM,OAAO,GAAG,YAAY,IAAI,IAAI,CAAC,gBAAgB,EAAE,aAAa;AACpE,QAAA,OAAO,OAAO,EAAE,WAAW,IAAI,EAAE;;;AAInC,IAAA,cAAc,CAAC,QAAqC,EAAA;AAClD,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,QAAQ,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,QAAQ;;;AAI/E,IAAA,WAAW,CAAC,QAAqC,EAAA;AAC/C,QAAA,QACE,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ;AAC7B,YAAA,IAAI,CAAC,kBAAkB,EAAE,KAAK,QAAQ;AACtC,YAAA,CAAC,IAAI,CAAC,cAAc,CAAC,4BAA4B;;;AAKrD,IAAA,oBAAoB,CAAC,QAA4B,EAAA;AAC/C,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC;;;IAIzF,aAAa,CAAC,IAAyB,EAAE,QAA4B,EAAA;;;AAGnE,QAAA,QACE,IAAI,CAAC,kBAAkB,EAAE,KAAK,QAAQ;aACrC,IAAI,KAAK,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC;;IAIhF,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE;;;IAIlC,kBAAkB,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,cAAc,IAAI,OAAO;;AAGvC;;;AAGG;AACH,IAAA,YAAY,CAAC,QAAiB,EAAA;AAC5B,QAAA,IAAI,QAAQ,KAAK,IAAI,CAAC,SAAS,EAAE;AAC/B,YAAA,OAAO,KAAK;;AAGd,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ;QAEzB,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC;;aAC3C;YACL,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC;;AAGpD,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC;AAClC,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;AACtC,QAAA,OAAO,IAAI;;AAGb;;;;AAIG;IACH,aAAa,GAAA;AACX,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;IAIxC,oBAAoB,GAAA;AAClB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE;AAChC,gBAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ;gBAC9B,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,CAAC;;AACvC,iBAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AACzB,gBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;gBACpB,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,CAAC;;;;;AAMlD,IAAA,YAAY,CAAC,KAAa,EAAA;QACxB,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,UAAU,EAAE,KAAK,GAAG,EAAE,CAAC;;IAG9C,0BAA0B,GAAA;QAClC,MAAM,UAAU,GACd,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC;AACvC,YAAA,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,QAAQ,CAAC;AACrC,YAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;AAC7B,YAAA,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;QAC5B,MAAM,WAAW,GACf,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC;AACpC,YAAA,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,OAAO,CAAC;AACtC,YAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;AAC5B,YAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;QAC3B,OAAO,UAAU,IAAI,WAAW;;uGA/OvB,aAAa,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,EANb,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,SAAA,EAAA,EAAA,MAAA,EAAA,eAAA,EAAA,OAAA,EAAA,wBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,+BAAA,EAAA,qFAAA,EAAA,0CAAA,EAAA,wCAAA,EAAA,wCAAA,EAAA,sCAAA,EAAA,yCAAA,EAAA,qCAAA,EAAA,gDAAA,EAAA,uCAAA,EAAA,4CAAA,EAAA,4BAAA,EAAA,6CAAA,EAAA,2BAAA,EAAA,yCAAA,EAAA,yBAAA,EAAA,0CAAA,EAAA,wBAAA,EAAA,mDAAA,EAAA,8BAAA,EAAA,kBAAA,EAAA,6CAAA,EAAA,gBAAA,EAAA,oBAAA,EAAA,+BAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,UAAA,EAAA,EAAA,cAAA,EAAA,qDAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA,EAAC,OAAO,EAAE,eAAe,EAAE,WAAW,EAAE,aAAa,EAAC;AACtD,YAAA,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,aAAa,EAAC;SACnD,EAOgB,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,QAAA,EAAA,SAAA,EAAA,eAAe,6DACf,gBAAgB,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,eAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EC1GnC,8sGAyFA,EDUY,MAAA,EAAA,CAAA,q6kBAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,gBAAgB,oJAAE,iBAAiB,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,CAAA,2BAAA,EAAA,UAAA,CAAA,EAAA,OAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAElC,aAAa,EAAA,UAAA,EAAA,CAAA;kBA3CzB,SAAS;+BACE,iBAAiB,EAAA,QAAA,EACjB,eAAe,EAEnB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,qDAAqD;AAC9D,wBAAA,MAAM,EAAE,QAAQ;;;AAGhB,wBAAA,iCAAiC,EAC/B,qFAAqF;;;AAGvF,wBAAA,4CAA4C,EAAE,oCAAoC;AAClF,wBAAA,0CAA0C,EAAE,kCAAkC;AAC9E,wBAAA,2CAA2C,EAAE,iCAAiC;AAC9E,wBAAA,kDAAkD,EAAE,mCAAmC;;;AAGvF,wBAAA,8CAA8C,EAAE,0BAA0B;AAC1E,wBAAA,+CAA+C,EAAE,yBAAyB;AAC1E,wBAAA,2CAA2C,EAAE,uBAAuB;AACpE,wBAAA,4CAA4C,EAAE,sBAAsB;;;AAIpE,wBAAA,qDAAqD,EAAE,8BAA8B;AACrF,wBAAA,oBAAoB,EAAE,yCAAyC;AAC/D,wBAAA,kBAAkB,EAAE,kBAAkB;AACtC,wBAAA,iCAAiC,EAAE,iBAAiB;AACpD,wBAAA,sBAAsB,EAAE,UAAU;AAClC,wBAAA,QAAQ,EAAE,eAAe;AACzB,wBAAA,SAAS,EAAE,wBAAwB;AACpC,qBAAA,EAAA,aAAA,EAEc,iBAAiB,CAAC,IAAI,mBACpB,uBAAuB,CAAC,MAAM,EACpC,SAAA,EAAA;AACT,wBAAA,EAAC,OAAO,EAAE,eAAe,EAAE,WAAW,eAAe,EAAC;AACtD,wBAAA,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,eAAe,EAAC;AACnD,qBAAA,EAAA,OAAA,EACQ,CAAC,gBAAgB,EAAE,iBAAiB,CAAC,EAAA,QAAA,EAAA,8sGAAA,EAAA,MAAA,EAAA,CAAA,q6kBAAA,CAAA,EAAA;8BAMS,MAAM,EAAA,CAAA;sBAA5D,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,eAAe,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;gBACG,OAAO,EAAA,CAAA;sBAA9D,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,gBAAgB,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;gBACxB,gBAAgB,EAAA,CAAA;sBAA7C,SAAS;uBAAC,iBAAiB;gBAQnB,cAAc,EAAA,CAAA;sBADtB;gBAIQ,cAAc,EAAA,CAAA;sBAAtB;gBAQY,gBAAgB,EAAA,CAAA;sBAA5B;gBAgBG,KAAK,EAAA,CAAA;sBADR;gBAWG,KAAK,EAAA,CAAA;sBADR;gBAeG,QAAQ,EAAA,CAAA;sBADX;;;AE3JH;;;AAGG;MAOU,4BAA4B,CAAA;uGAA5B,4BAA4B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAA5B,4BAA4B,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iCAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,6CAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAA5B,4BAA4B,EAAA,UAAA,EAAA,CAAA;kBANxC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iCAAiC;;;AAG3C,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,6CAA6C,EAAC;AAC/D,iBAAA;;;ACRD;;;;AAIG;MACU,YAAY,GAAG,IAAI,cAAc,CAAa,YAAY;AAejE,MAAO,UAAW,SAAQ,WAAW,CAAA;;;;;;IAMhC,iBAAiB,GAAG,KAAK;uGANvB,UAAU,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAV,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAU,EAFV,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,YAAA,EAAA,EAAA,cAAA,EAAA,6CAAA,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAC,CAAC,2EARlD,2BAA2B,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,omjBAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAU1B,UAAU,EAAA,UAAA,EAAA,CAAA;kBAbtB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,cAAc,EACd,QAAA,EAAA,YAAY,EACZ,QAAA,EAAA,2BAA2B,EAC/B,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,6CAA6C;AACtD,wBAAA,MAAM,EAAE,YAAY;AACrB,qBAAA,EAAA,aAAA,EAEc,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,SAAA,EACpC,CAAC,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAA,UAAY,EAAC,CAAC,EAAA,MAAA,EAAA,CAAA,omjBAAA,CAAA,EAAA;;;ACWjD,MAAA,iCAAiC,GAAQ;AACpD,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,gBAAgB,CAAC;AAC/C,IAAA,KAAK,EAAE,IAAI;;AAGb;MACa,sBAAsB,CAAA;AAGxB,IAAA,MAAA;AAEA,IAAA,OAAA;AAJT,IAAA,WAAA;;IAES,MAAwB;;IAExB,OAAwB,EAAA;QAFxB,IAAM,CAAA,MAAA,GAAN,MAAM;QAEN,IAAO,CAAA,OAAA,GAAP,OAAO;;AAEjB;AAqBK,MAAO,gBACX,SAAQ,WAAW,CAAA;AAGnB,IAAA,QAAQ,GAAG,MAAM,CAA0B,UAAU,CAAC;AAC9C,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,IAAA,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;IAE7B,YAAY,GAAG,KAAK;AACpB,IAAA,WAAW;AACX,IAAA,iBAAiB;;AAGjB,IAAA,UAAU,GAAG,IAAI,OAAO,EAAQ;;AAGhC,IAAA,YAAY;;AAGZ,IAAA,SAAS,GAAyB,CAAC,CAAM,KAAI,GAAG;AAEH,IAAA,MAAM;;AAGxC,IAAA,eAAe,GAChC,IAAI,YAAY,EAA0B;AAE5C;;;;;;;AAOG;IACM,KAAK,GAAiB,QAAQ;AAEvC;;;;AAIG;IACM,WAAW,GAAkC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;;AAG3E,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,KAAmB,EAAA;AAC9B,QAAA,MAAM,QAAQ,GAAG,qBAAqB,CAAC,KAAK,CAAC;AAE7C,QAAA,IAAI,QAAQ,KAAK,IAAI,CAAC,SAAS,EAAE;AAC/B,YAAA,IAAI,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,IAAI,CAAC,YAAY,EAAE;AACxE,gBAAA,MAAM,IAAI,KAAK,CACb,2EAA2E,CAC5E;;AAGH,YAAA,IAAI,CAAC,SAAS,GAAG,QAAQ;AACzB,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;;;IAGpF,SAAS,GAAG,IAAI;;AAGxB,IAAA,IACI,4BAA4B,GAAA;QAC9B,OAAO,IAAI,CAAC,6BAA6B;;IAE3C,IAAI,4BAA4B,CAAC,KAAmB,EAAA;AAClD,QAAA,IAAI,CAAC,6BAA6B,GAAG,qBAAqB,CAAC,KAAK,CAAC;;IAE3D,6BAA6B,GACnC,IAAI,CAAC,eAAe,EAAE,4BAA4B,IAAI,KAAK;;IAG7D,eAAe,GAAG,IAAI,cAAc,CAAgB,IAAI,CAAC,SAAS,CAAC;;AAGnE,IAAA,MAAM;;AAGN,IAAA,UAAU,GAAe,MAAK,GAAG;AAEhB,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAI/D,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;AACP,QAAA,IAAI,CAAC,iBAAiB,GAAG,KAAK;;IAGhC,eAAe,GAAA;;;AAGb,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI;QACxB,IAAI,CAAC,oBAAoB,EAAE;;;AAI3B,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;YAClC,IAAI,CAAC,iBAAiB,GAAG;AACvB,gBAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC;AAClF,gBAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC;aACrF;AACH,SAAC,CAAC;AAEF,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,YAAA,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC;;QAGzC,IAAI,CAAC,wBAAwB,EAAE;;AAGjC,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,MAAM,eAAe,GAAG,OAAO,CAAC,UAAU,CAAC;AAC3C,QAAA,MAAM,oBAAoB,GAAG,OAAO,CAAC,eAAe,CAAC;AACrD,QAAA,MAAM,mCAAmC,GAAG,OAAO,CAAC,8BAA8B,CAAC;AAEnF,QAAA,IACE,CAAC,oBAAoB,IAAI,CAAC,oBAAoB,CAAC,WAAW;AAC1D,aAAC,eAAe,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC;aAChD,mCAAmC,IAAI,CAAC,mCAAmC,CAAC,WAAW,CAAC,EACzF;YACA,IAAI,CAAC,oBAAoB,EAAE;;;IAI/B,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE;AAC3B,QAAA,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,OAAO,IAAI,OAAO,EAAE,CAAC;AACrD,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;AACtB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;AAC1B,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI;;;AAI1B,IAAA,KAAK,CAAC,OAAsB,EAAA;QAC1B,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC;;;IAI5C,SAAS,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC;;;IAI1C,WAAW,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC;;;IAI3C,kBAAkB,GAAA;;;;QAIhB,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtC,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,wBAAwB,EAAE;AAC7C,YAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;AACrB,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;;;AAKvB,IAAA,gBAAgB,CAAC,OAAwB,EAAA;AACvC,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,sBAAsB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;;;AAItE,IAAA,UAAU,CAAC,MAAgB,EAAA;AACzB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM;AAEpB,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,CAAC,qBAAqB,CAAC,MAAM,IAAI,EAAE,CAAC;;;;AAK5C,IAAA,gBAAgB,CAAC,UAAmB,EAAA;AAClC,QAAA,IAAI,CAAC,QAAQ,GAAG,UAAU;AAC1B,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;QACtC,IAAI,CAAC,oBAAoB,EAAE;;AAG7B;;;AAGG;AACH,IAAA,IACa,QAAQ,GAAA;QACnB,OAAO,IAAI,CAAC,sBAAsB;;IAEpC,IAAa,QAAQ,CAAC,KAAmB,EAAA;;;;AAIvC,QAAA,IAAI,CAAC,sBAAsB,GAAG,qBAAqB,CAAC,KAAK,CAAC;AAC1D,QAAA,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC/B,IAAI,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;;;IAG/B,sBAAsB,GAAG,KAAK;;AAGtC,IAAA,gBAAgB,CAAC,EAAwB,EAAA;AACvC,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE;;;AAIrB,IAAA,iBAAiB,CAAC,EAAc,EAAA;AAC9B,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE;;;IAId,wBAAwB,GAAA;AAC9B,QAAA,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,IAAG;;AAE9E,YAAA,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,KAAK,EAAE;AAC5B,gBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;;AAGtB,YAAA,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,OAAO,EAAE;AAC9B,gBAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;;AAGvB,YAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE;gBAC1B,IAAI,CAAC,kBAAkB,EAAE;;AAE7B,SAAC,CAAC;;;AAII,IAAA,qBAAqB,CAAC,MAAgB,EAAA;AAC5C,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAE1D,QAAA,MAAM,CAAC,OAAO,CAAC,KAAK,IAAG;YACrB,MAAM,mBAAmB,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,IAAG;;;gBAGrD,OAAO,MAAM,CAAC,QAAQ,GAAG,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC;AACxE,aAAC,CAAC;YAEF,IAAI,mBAAmB,EAAE;AACvB,gBAAA,mBAAmB,CAAC,YAAY,CAAC,IAAI,CAAC;;AAE1C,SAAC,CAAC;;;IAII,wBAAwB,GAAA;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC;;;IAI3E,oBAAoB,GAAA;AAC1B,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;;;AAI1D;;;AAGG;IACK,sBAAsB,CAAC,UAAmB,EAAE,YAAsB,EAAA;;;QAGxE,MAAM,cAAc,GAAoB,EAAE;AAE1C,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,IAAG;AAC5B,YAAA,IAAI,CAAC,CAAC,YAAY,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE;AAC1E,gBAAA,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC;;AAE/B,SAAC,CAAC;AAEF,QAAA,IAAI,cAAc,CAAC,MAAM,EAAE;YACzB,IAAI,CAAC,kBAAkB,EAAE;;AAG3B,QAAA,OAAO,cAAc;;;;;AAMvB,IAAA,IAAI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,MAAM;;;AAIpB,IAAA,cAAc,CAAC,KAAoB,EAAA;AACjC,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU;AAE9C,QAAA,IACE,CAAC,KAAK,CAAC,OAAO,KAAK,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK;AACnD,YAAA,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;YAC5B,UAAU;AACV,YAAA,CAAC,UAAU,CAAC,QAAQ,EACpB;YACA,KAAK,CAAC,cAAc,EAAE;YACtB,UAAU,CAAC,oBAAoB,EAAE;;AAC5B,aAAA,IACL,KAAK,CAAC,OAAO,KAAK,CAAC;AACnB,YAAA,IAAI,CAAC,QAAQ;AACb,YAAA,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;YAC5B,cAAc,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,CAAC,EAC3C;YACA,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;YACtF,KAAK,CAAC,cAAc,EAAE;AACtB,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,sBAAsB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;;aACjE;AACL,YAAA,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC;;;;IAK7B,eAAe,GAAG,MAAK;;QAE7B,UAAU,CAAC,MAAK;AACd,YAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE;gBAC1B,IAAI,CAAC,kBAAkB,EAAE;;AAE7B,SAAC,CAAC;AACJ,KAAC;;AAGO,IAAA,cAAc,GAAG,CAAC,KAAiB,KAAI;AAC7C,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB;;AAGF,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC;AACtB,aAAA,OAAO;AACP,aAAA,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAqB,CAAC,CAAC;AAE1F,QAAA,IAAI,WAAW,GAAG,CAAC,CAAC,EAAE;AACpB,YAAA,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC;;aAC7B;YACL,IAAI,CAAC,kBAAkB,EAAE;;AAE7B,KAAC;AAED;;;;;;;;;;;;AAYG;IACK,oBAAoB,GAAA;QAC1B,IAAI,CAAC,WAAW,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,MAAM;AAC/C,aAAA,cAAc;AACd,aAAA,aAAa;AACb,aAAA,QAAQ;aACR,aAAa,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC;;QAGrC,IAAI,CAAC,kBAAkB,EAAE;;AAGzB,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,eAAe,IAAI,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;;AAG5F,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;AAClE,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU;AAE9C,YAAA,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;gBACnE,IAAI,CAAC,kBAAkB,EAAE;;AAE7B,SAAC,CAAC;;AAGJ;;;AAGG;AACK,IAAA,gBAAgB,CAAC,KAAa,EAAA;AACpC,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,SAAS,KAAK,IAAI,CAAC,YAAY,CAAC,SAAS,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACzF,QAAA,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,KAAK,CAAC;;AAG1C;;;AAGG;IACK,kBAAkB,GAAA;AACxB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;YACzB;;QAGF,MAAM,UAAU,GACd,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK;QAChF,IAAI,CAAC,gBAAgB,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;;;IAI5E,cAAc,GAAA;AACpB,QAAA,MAAM,aAAa,GAAG,iCAAiC,EAAE;AACzD,QAAA,OAAO,aAAa,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,aAAa,CAAC;;uGAvZlE,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,gBAAgB,EAPhB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,WAAA,EAAA,aAAA,EAAA,QAAA,EAAA,UAAA,EAAA,4BAAA,EAAA,8BAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,SAAA,EAAA,EAAA,SAAA,EAAA,EAAA,SAAA,EAAA,wBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,2BAAA,EAAA,UAAA,EAAA,EAAA,cAAA,EAAA,mDAAA,EAAA,EAAA,SAAA,EAAA;YACT,iCAAiC;AACjC,YAAA,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,gBAAgB,EAAC;AACrD,YAAA,EAAC,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,gBAAgB,EAAC;SACzD,EAwBgB,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,QAAA,EAAA,SAAA,EAAA,aAAa,4HA/BpB,2BAA2B,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,omjBAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAU1B,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAnB5B,SAAS;+BACE,oBAAoB,EAAA,QAAA,EACpB,kBAAkB,EACtB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,mDAAmD;AAC5D,wBAAA,MAAM,EAAE,SAAS;AACjB,wBAAA,6BAA6B,EAAE,UAAU;AACzC,wBAAA,WAAW,EAAE,wBAAwB;AACtC,qBAAA,EAAA,QAAA,EACS,2BAA2B,EAAA,aAAA,EAEtB,iBAAiB,CAAC,IAAI,EAC1B,SAAA,EAAA;wBACT,iCAAiC;AACjC,wBAAA,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,kBAAkB,EAAC;AACrD,wBAAA,EAAC,OAAO,EAAE,cAAc,EAAE,WAAW,kBAAkB,EAAC;qBACzD,EACgB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,MAAA,EAAA,CAAA,omjBAAA,CAAA,EAAA;wDAuBM,MAAM,EAAA,CAAA;sBAA1D,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,aAAa,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;gBAGhC,eAAe,EAAA,CAAA;sBAAjC;gBAWQ,KAAK,EAAA,CAAA;sBAAb;gBAOQ,WAAW,EAAA,CAAA;sBAAnB;gBAIG,QAAQ,EAAA,CAAA;sBADX;gBAsBG,4BAA4B,EAAA,CAAA;sBAD/B;gBA6HY,QAAQ,EAAA,CAAA;sBADpB;;;MCzMU,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,YAjCtB,eAAe;YACf,eAAe;YACf,eAAe;YACf,uBAAuB;YACvB,OAAO;YACP,aAAa;YACb,UAAU;YACV,gBAAgB;YAChB,WAAW;YACX,aAAa;YACb,4BAA4B;YAC5B,iBAAiB;YACjB,eAAe;YACf,eAAe;YACf,gBAAgB;AAChB,YAAA,eAAe,aAGf,OAAO;YACP,aAAa;YACb,UAAU;YACV,gBAAgB;YAChB,WAAW;YACX,aAAa;YACb,iBAAiB;YACjB,eAAe;YACf,4BAA4B;YAC5B,gBAAgB;YAChB,eAAe;YACf,gBAAgB;YAChB,eAAe,CAAA,EAAA,CAAA;AAGN,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,YAjCtB,eAAe;YACf,eAAe;YACf,eAAe;AACf,YAAA,uBAAuB,EAwBvB,gBAAgB,CAAA,EAAA,CAAA;;2FAMP,aAAa,EAAA,UAAA,EAAA,CAAA;kBAnCzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE;wBACP,eAAe;wBACf,eAAe;wBACf,eAAe;wBACf,uBAAuB;wBACvB,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,gBAAgB;wBAChB,WAAW;wBACX,aAAa;wBACb,4BAA4B;wBAC5B,iBAAiB;wBACjB,eAAe;wBACf,eAAe;wBACf,gBAAgB;wBAChB,eAAe;AAChB,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,gBAAgB;wBAChB,WAAW;wBACX,aAAa;wBACb,iBAAiB;wBACjB,eAAe;wBACf,4BAA4B;wBAC5B,gBAAgB;wBAChB,eAAe;wBACf,gBAAgB;wBAChB,eAAe;AAChB,qBAAA;AACF,iBAAA;;;;;"}