{"version": 3, "file": "divider.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/divider/divider.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/divider/divider-module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ChangeDetectionStrategy, Component, Input, ViewEncapsulation} from '@angular/core';\nimport {BooleanInput, coerceBooleanProperty} from '@angular/cdk/coercion';\n\n@Component({\n  selector: 'mat-divider',\n  host: {\n    'role': 'separator',\n    '[attr.aria-orientation]': 'vertical ? \"vertical\" : \"horizontal\"',\n    '[class.mat-divider-vertical]': 'vertical',\n    '[class.mat-divider-horizontal]': '!vertical',\n    '[class.mat-divider-inset]': 'inset',\n    'class': 'mat-divider',\n  },\n  template: '',\n  styleUrl: 'divider.css',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class MatDivider {\n  /** Whether the divider is vertically aligned. */\n  @Input()\n  get vertical(): boolean {\n    return this._vertical;\n  }\n  set vertical(value: BooleanInput) {\n    this._vertical = coerceBooleanProperty(value);\n  }\n  private _vertical: boolean = false;\n\n  /** Whether the divider is an inset divider. */\n  @Input()\n  get inset(): boolean {\n    return this._inset;\n  }\n  set inset(value: BooleanInput) {\n    this._inset = coerceBooleanProperty(value);\n  }\n  private _inset: boolean = false;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '../core';\nimport {MatDivider} from './divider';\n\n@NgModule({\n  imports: [MatCommonModule, MatDivider],\n  exports: [MatDivider, MatCommonModule],\n})\nexport class MatDividerModule {}\n"], "names": [], "mappings": ";;;;;;;MA0Ba,UAAU,CAAA;;AAErB,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,KAAmB,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,KAAK,CAAC;;IAEvC,SAAS,GAAY,KAAK;;AAGlC,IAAA,IACI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,MAAM;;IAEpB,IAAI,KAAK,CAAC,KAAmB,EAAA;AAC3B,QAAA,IAAI,CAAC,MAAM,GAAG,qBAAqB,CAAC,KAAK,CAAC;;IAEpC,MAAM,GAAY,KAAK;uGAnBpB,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAV,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAU,uZALX,EAAE,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,yeAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAKD,UAAU,EAAA,UAAA,EAAA,CAAA;kBAftB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,aAAa,EACjB,IAAA,EAAA;AACJ,wBAAA,MAAM,EAAE,WAAW;AACnB,wBAAA,yBAAyB,EAAE,sCAAsC;AACjE,wBAAA,8BAA8B,EAAE,UAAU;AAC1C,wBAAA,gCAAgC,EAAE,WAAW;AAC7C,wBAAA,2BAA2B,EAAE,OAAO;AACpC,wBAAA,OAAO,EAAE,aAAa;qBACvB,EACS,QAAA,EAAA,EAAE,iBAEG,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,MAAA,EAAA,CAAA,yeAAA,CAAA,EAAA;8BAK3C,QAAQ,EAAA,CAAA;sBADX;gBAWG,KAAK,EAAA,CAAA;sBADR;;;MCtBU,gBAAgB,CAAA;uGAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,YAHjB,eAAe,EAAE,UAAU,CAC3B,EAAA,OAAA,EAAA,CAAA,UAAU,EAAE,eAAe,CAAA,EAAA,CAAA;wGAE1B,gBAAgB,EAAA,OAAA,EAAA,CAHjB,eAAe,EACH,eAAe,CAAA,EAAA,CAAA;;2FAE1B,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAJ5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,UAAU,CAAC;AACtC,oBAAA,OAAO,EAAE,CAAC,UAAU,EAAE,eAAe,CAAC;AACvC,iBAAA;;;;;"}