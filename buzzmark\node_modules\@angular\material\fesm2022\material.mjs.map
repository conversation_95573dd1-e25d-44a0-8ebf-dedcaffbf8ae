{"version": 3, "file": "material.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// primary entry-point which is empty as of version 9. All components should\n// be imported through their individual entry-points. This file is needed to\n// satisfy the \"ng_package\" bazel rule which also requires a primary entry-point.\n\n// Workaround for: https://github.com/microsoft/rushstack/issues/2806.\n// This is a private export that can be removed at any time.\nexport const ɵɵtsModuleIndicatorApiExtractorWorkaround = true;\n"], "names": [], "mappings": "AAQA;AACA;AACA;AAEA;AACA;AACO,MAAM,yCAAyC,GAAG;;;;"}