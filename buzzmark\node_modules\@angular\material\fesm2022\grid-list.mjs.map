{"version": 3, "file": "grid-list.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/grid-list/grid-list-base.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/grid-list/grid-tile.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/grid-list/grid-tile.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/grid-list/grid-tile-text.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/grid-list/tile-styler.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/grid-list/grid-list.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/grid-list/grid-list.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/grid-list/grid-list-module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken} from '@angular/core';\n\n/**\n * Injection token used to provide a grid list to a tile and to avoid circular imports.\n * @docs-private\n */\nexport const MAT_GRID_LIST = new InjectionToken<MatGridListBase>('MAT_GRID_LIST');\n\n/**\n * Base interface for a `MatGridList`.\n * @docs-private\n */\nexport interface MatGridListBase {\n  cols: number;\n  gutterSize: string;\n  rowHeight: number | string;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Component,\n  ViewEncapsulation,\n  ElementRef,\n  Input,\n  ContentChildren,\n  QueryList,\n  AfterContentInit,\n  Directive,\n  ChangeDetectionStrategy,\n  inject,\n} from '@angular/core';\nimport {MatLine, setLines} from '../core';\nimport {coerceNumberProperty, NumberInput} from '@angular/cdk/coercion';\nimport {MAT_GRID_LIST, MatGridListBase} from './grid-list-base';\n\n@Component({\n  selector: 'mat-grid-tile',\n  exportAs: 'matGridTile',\n  host: {\n    'class': 'mat-grid-tile',\n    // Ensures that the \"rowspan\" and \"colspan\" input value is reflected in\n    // the DOM. This is needed for the grid-tile harness.\n    '[attr.rowspan]': 'rowspan',\n    '[attr.colspan]': 'colspan',\n  },\n  templateUrl: 'grid-tile.html',\n  styleUrl: 'grid-list.css',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class MatGridTile {\n  private _element = inject<ElementRef<HTMLElement>>(ElementRef);\n  _gridList? = inject<MatGridListBase>(MAT_GRID_LIST, {optional: true});\n\n  _rowspan: number = 1;\n  _colspan: number = 1;\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  /** Amount of rows that the grid tile takes up. */\n  @Input()\n  get rowspan(): number {\n    return this._rowspan;\n  }\n  set rowspan(value: NumberInput) {\n    this._rowspan = Math.round(coerceNumberProperty(value));\n  }\n\n  /** Amount of columns that the grid tile takes up. */\n  @Input()\n  get colspan(): number {\n    return this._colspan;\n  }\n  set colspan(value: NumberInput) {\n    this._colspan = Math.round(coerceNumberProperty(value));\n  }\n\n  /**\n   * Sets the style of the grid-tile element.  Needs to be set manually to avoid\n   * \"Changed after checked\" errors that would occur with HostBinding.\n   */\n  _setStyle(property: string, value: any): void {\n    (this._element.nativeElement.style as any)[property] = value;\n  }\n}\n\n@Component({\n  selector: 'mat-grid-tile-header, mat-grid-tile-footer',\n  templateUrl: 'grid-tile-text.html',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n})\nexport class MatGridTileText implements AfterContentInit {\n  private _element = inject<ElementRef<HTMLElement>>(ElementRef);\n\n  @ContentChildren(MatLine, {descendants: true}) _lines: QueryList<MatLine>;\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  ngAfterContentInit() {\n    setLines(this._lines, this._element);\n  }\n}\n\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\n@Directive({\n  selector: '[mat-grid-avatar], [matGridAvatar]',\n  host: {'class': 'mat-grid-avatar'},\n})\nexport class MatGridAvatarCssMatStyler {}\n\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\n@Directive({\n  selector: 'mat-grid-tile-header',\n  host: {'class': 'mat-grid-tile-header'},\n})\nexport class MatGridTileHeaderCssMatStyler {}\n\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\n@Directive({\n  selector: 'mat-grid-tile-footer',\n  host: {'class': 'mat-grid-tile-footer'},\n})\nexport class MatGridTileFooterCssMatStyler {}\n", "<div class=\"mat-grid-tile-content\">\n  <ng-content></ng-content>\n</div>\n", "<ng-content select=\"[mat-grid-avatar], [matGridAvatar]\"></ng-content>\n<div class=\"mat-grid-list-text\"><ng-content select=\"[mat-line], [matLine]\"></ng-content></div>\n<ng-content></ng-content>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {QueryList} from '@angular/core';\nimport {MatGridTile} from './grid-tile';\nimport {TileCoordinator} from './tile-coordinator';\n\n/**\n * RegExp that can be used to check whether a value will\n * be allowed inside a CSS `calc()` expression.\n */\nconst cssCalcAllowedValue = /^-?\\d+((\\.\\d+)?[A-Za-z%$]?)+$/;\n\n/** Object that can be styled by the `TileStyler`. */\nexport interface TileStyleTarget {\n  _setListStyle(style: [string, string | null] | null): void;\n  _tiles: QueryList<MatGridTile>;\n}\n\n/**\n * Sets the style properties for an individual tile, given the position calculated by the\n * Tile Coordinator.\n * @docs-private\n */\nexport abstract class TileStyler {\n  _gutterSize: string;\n  _rows: number = 0;\n  _rowspan: number = 0;\n  _cols: number;\n  _direction: string;\n\n  /**\n   * Adds grid-list layout info once it is available. Cannot be processed in the constructor\n   * because these properties haven't been calculated by that point.\n   *\n   * @param gutterSize Size of the grid's gutter.\n   * @param tracker Instance of the TileCoordinator.\n   * @param cols Amount of columns in the grid.\n   * @param direction Layout direction of the grid.\n   */\n  init(gutterSize: string, tracker: TileCoordinator, cols: number, direction: string): void {\n    this._gutterSize = normalizeUnits(gutterSize);\n    this._rows = tracker.rowCount;\n    this._rowspan = tracker.rowspan;\n    this._cols = cols;\n    this._direction = direction;\n  }\n\n  /**\n   * Computes the amount of space a single 1x1 tile would take up (width or height).\n   * Used as a basis for other calculations.\n   * @param sizePercent Percent of the total grid-list space that one 1x1 tile would take up.\n   * @param gutterFraction Fraction of the gutter size taken up by one 1x1 tile.\n   * @return The size of a 1x1 tile as an expression that can be evaluated via CSS calc().\n   */\n  getBaseTileSize(sizePercent: number, gutterFraction: number): string {\n    // Take the base size percent (as would be if evenly dividing the size between cells),\n    // and then subtracting the size of one gutter. However, since there are no gutters on the\n    // edges, each tile only uses a fraction (gutterShare = numGutters / numCells) of the gutter\n    // size. (Imagine having one gutter per tile, and then breaking up the extra gutter on the\n    // edge evenly among the cells).\n    return `(${sizePercent}% - (${this._gutterSize} * ${gutterFraction}))`;\n  }\n\n  /**\n   * Gets The horizontal or vertical position of a tile, e.g., the 'top' or 'left' property value.\n   * @param offset Number of tiles that have already been rendered in the row/column.\n   * @param baseSize Base size of a 1x1 tile (as computed in getBaseTileSize).\n   * @return Position of the tile as a CSS calc() expression.\n   */\n  getTilePosition(baseSize: string, offset: number): string {\n    // The position comes the size of a 1x1 tile plus gutter for each previous tile in the\n    // row/column (offset).\n    return offset === 0 ? '0' : calc(`(${baseSize} + ${this._gutterSize}) * ${offset}`);\n  }\n\n  /**\n   * Gets the actual size of a tile, e.g., width or height, taking rowspan or colspan into account.\n   * @param baseSize Base size of a 1x1 tile (as computed in getBaseTileSize).\n   * @param span The tile's rowspan or colspan.\n   * @return Size of the tile as a CSS calc() expression.\n   */\n  getTileSize(baseSize: string, span: number): string {\n    return `(${baseSize} * ${span}) + (${span - 1} * ${this._gutterSize})`;\n  }\n\n  /**\n   * Sets the style properties to be applied to a tile for the given row and column index.\n   * @param tile Tile to which to apply the styling.\n   * @param rowIndex Index of the tile's row.\n   * @param colIndex Index of the tile's column.\n   */\n  setStyle(tile: MatGridTile, rowIndex: number, colIndex: number): void {\n    // Percent of the available horizontal space that one column takes up.\n    let percentWidthPerTile = 100 / this._cols;\n\n    // Fraction of the vertical gutter size that each column takes up.\n    // For example, if there are 5 columns, each column uses 4/5 = 0.8 times the gutter width.\n    let gutterWidthFractionPerTile = (this._cols - 1) / this._cols;\n\n    this.setColStyles(tile, colIndex, percentWidthPerTile, gutterWidthFractionPerTile);\n    this.setRowStyles(tile, rowIndex, percentWidthPerTile, gutterWidthFractionPerTile);\n  }\n\n  /** Sets the horizontal placement of the tile in the list. */\n  setColStyles(tile: MatGridTile, colIndex: number, percentWidth: number, gutterWidth: number) {\n    // Base horizontal size of a column.\n    let baseTileWidth = this.getBaseTileSize(percentWidth, gutterWidth);\n\n    // The width and horizontal position of each tile is always calculated the same way, but the\n    // height and vertical position depends on the rowMode.\n    let side = this._direction === 'rtl' ? 'right' : 'left';\n    tile._setStyle(side, this.getTilePosition(baseTileWidth, colIndex));\n    tile._setStyle('width', calc(this.getTileSize(baseTileWidth, tile.colspan)));\n  }\n\n  /**\n   * Calculates the total size taken up by gutters across one axis of a list.\n   */\n  getGutterSpan(): string {\n    return `${this._gutterSize} * (${this._rowspan} - 1)`;\n  }\n\n  /**\n   * Calculates the total size taken up by tiles across one axis of a list.\n   * @param tileHeight Height of the tile.\n   */\n  getTileSpan(tileHeight: string): string {\n    return `${this._rowspan} * ${this.getTileSize(tileHeight, 1)}`;\n  }\n\n  /**\n   * Sets the vertical placement of the tile in the list.\n   * This method will be implemented by each type of TileStyler.\n   * @docs-private\n   */\n  abstract setRowStyles(\n    tile: MatGridTile,\n    rowIndex: number,\n    percentWidth: number,\n    gutterWidth: number,\n  ): void;\n\n  /**\n   * Calculates the computed height and returns the correct style property to set.\n   * This method can be implemented by each type of TileStyler.\n   * @docs-private\n   */\n  getComputedHeight(): [string, string] | null {\n    return null;\n  }\n\n  /**\n   * Called when the tile styler is swapped out with a different one. To be used for cleanup.\n   * @param list Grid list that the styler was attached to.\n   * @docs-private\n   */\n  abstract reset(list: TileStyleTarget): void;\n}\n\n/**\n * This type of styler is instantiated when the user passes in a fixed row height.\n * Example `<mat-grid-list cols=\"3\" rowHeight=\"100px\">`\n * @docs-private\n */\nexport class FixedTileStyler extends TileStyler {\n  constructor(public fixedRowHeight: string) {\n    super();\n  }\n\n  override init(gutterSize: string, tracker: TileCoordinator, cols: number, direction: string) {\n    super.init(gutterSize, tracker, cols, direction);\n    this.fixedRowHeight = normalizeUnits(this.fixedRowHeight);\n\n    if (\n      !cssCalcAllowedValue.test(this.fixedRowHeight) &&\n      (typeof ngDevMode === 'undefined' || ngDevMode)\n    ) {\n      throw Error(`Invalid value \"${this.fixedRowHeight}\" set as rowHeight.`);\n    }\n  }\n\n  override setRowStyles(tile: MatGridTile, rowIndex: number): void {\n    tile._setStyle('top', this.getTilePosition(this.fixedRowHeight, rowIndex));\n    tile._setStyle('height', calc(this.getTileSize(this.fixedRowHeight, tile.rowspan)));\n  }\n\n  override getComputedHeight(): [string, string] {\n    return ['height', calc(`${this.getTileSpan(this.fixedRowHeight)} + ${this.getGutterSpan()}`)];\n  }\n\n  override reset(list: TileStyleTarget) {\n    list._setListStyle(['height', null]);\n\n    if (list._tiles) {\n      list._tiles.forEach(tile => {\n        tile._setStyle('top', null);\n        tile._setStyle('height', null);\n      });\n    }\n  }\n}\n\n/**\n * This type of styler is instantiated when the user passes in a width:height ratio\n * for the row height.  Example `<mat-grid-list cols=\"3\" rowHeight=\"3:1\">`\n * @docs-private\n */\nexport class RatioTileStyler extends TileStyler {\n  /** Ratio width:height given by user to determine row height. */\n  rowHeightRatio: number;\n  baseTileHeight: string;\n\n  constructor(value: string) {\n    super();\n    this._parseRatio(value);\n  }\n\n  setRowStyles(\n    tile: MatGridTile,\n    rowIndex: number,\n    percentWidth: number,\n    gutterWidth: number,\n  ): void {\n    let percentHeightPerTile = percentWidth / this.rowHeightRatio;\n    this.baseTileHeight = this.getBaseTileSize(percentHeightPerTile, gutterWidth);\n\n    // Use padding-top and margin-top to maintain the given aspect ratio, as\n    // a percentage-based value for these properties is applied versus the *width* of the\n    // containing block. See http://www.w3.org/TR/CSS2/box.html#margin-properties\n    tile._setStyle('marginTop', this.getTilePosition(this.baseTileHeight, rowIndex));\n    tile._setStyle('paddingTop', calc(this.getTileSize(this.baseTileHeight, tile.rowspan)));\n  }\n\n  override getComputedHeight(): [string, string] {\n    return [\n      'paddingBottom',\n      calc(`${this.getTileSpan(this.baseTileHeight)} + ${this.getGutterSpan()}`),\n    ];\n  }\n\n  reset(list: TileStyleTarget) {\n    list._setListStyle(['paddingBottom', null]);\n\n    list._tiles.forEach(tile => {\n      tile._setStyle('marginTop', null);\n      tile._setStyle('paddingTop', null);\n    });\n  }\n\n  private _parseRatio(value: string): void {\n    const ratioParts = value.split(':');\n\n    if (ratioParts.length !== 2 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`mat-grid-list: invalid ratio given for row-height: \"${value}\"`);\n    }\n\n    this.rowHeightRatio = parseFloat(ratioParts[0]) / parseFloat(ratioParts[1]);\n  }\n}\n\n/**\n * This type of styler is instantiated when the user selects a \"fit\" row height mode.\n * In other words, the row height will reflect the total height of the container divided\n * by the number of rows.  Example `<mat-grid-list cols=\"3\" rowHeight=\"fit\">`\n *\n * @docs-private\n */\nexport class FitTileStyler extends TileStyler {\n  setRowStyles(tile: MatGridTile, rowIndex: number): void {\n    // Percent of the available vertical space that one row takes up.\n    let percentHeightPerTile = 100 / this._rowspan;\n\n    // Fraction of the horizontal gutter size that each column takes up.\n    let gutterHeightPerTile = (this._rows - 1) / this._rows;\n\n    // Base vertical size of a column.\n    let baseTileHeight = this.getBaseTileSize(percentHeightPerTile, gutterHeightPerTile);\n\n    tile._setStyle('top', this.getTilePosition(baseTileHeight, rowIndex));\n    tile._setStyle('height', calc(this.getTileSize(baseTileHeight, tile.rowspan)));\n  }\n\n  reset(list: TileStyleTarget) {\n    if (list._tiles) {\n      list._tiles.forEach(tile => {\n        tile._setStyle('top', null);\n        tile._setStyle('height', null);\n      });\n    }\n  }\n}\n\n/** Wraps a CSS string in a calc function */\nfunction calc(exp: string): string {\n  return `calc(${exp})`;\n}\n\n/** Appends pixels to a CSS string if no units are given. */\nfunction normalizeUnits(value: string): string {\n  return value.match(/([A-Za-z%]+)$/) ? value : `${value}px`;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Component,\n  ViewEncapsulation,\n  AfterContentChecked,\n  OnInit,\n  Input,\n  ContentChildren,\n  QueryList,\n  ElementRef,\n  ChangeDetectionStrategy,\n  inject,\n} from '@angular/core';\nimport {MatGridTile} from './grid-tile';\nimport {TileCoordinator} from './tile-coordinator';\nimport {\n  TileStyler,\n  FitTileStyler,\n  RatioTileStyler,\n  FixedTileStyler,\n  TileStyleTarget,\n} from './tile-styler';\nimport {Directionality} from '@angular/cdk/bidi';\nimport {coerceNumberProperty, NumberInput} from '@angular/cdk/coercion';\nimport {MAT_GRID_LIST, MatGridListBase} from './grid-list-base';\n\n// TODO(kara): Conditional (responsive) column count / row size.\n// TODO(kara): Re-layout on window resize / media change (debounced).\n// TODO(kara): gridTileHeader and gridTileFooter.\n\nconst MAT_FIT_MODE = 'fit';\n\n@Component({\n  selector: 'mat-grid-list',\n  exportAs: 'matGridList',\n  templateUrl: 'grid-list.html',\n  styleUrl: 'grid-list.css',\n  host: {\n    'class': 'mat-grid-list',\n    // Ensures that the \"cols\" input value is reflected in the DOM. This is\n    // needed for the grid-list harness.\n    '[attr.cols]': 'cols',\n  },\n  providers: [\n    {\n      provide: MAT_GRID_LIST,\n      useExisting: MatGridList,\n    },\n  ],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n})\nexport class MatGridList implements MatGridListBase, OnInit, AfterContentChecked, TileStyleTarget {\n  private _element = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _dir = inject(Directionality, {optional: true});\n\n  /** Number of columns being rendered. */\n  private _cols: number;\n\n  /** Used for determining the position of each tile in the grid. */\n  private _tileCoordinator: TileCoordinator;\n\n  /**\n   * Row height value passed in by user. This can be one of three types:\n   * - Number value (ex: \"100px\"):  sets a fixed row height to that value\n   * - Ratio value (ex: \"4:3\"): sets the row height based on width:height ratio\n   * - \"Fit\" mode (ex: \"fit\"): sets the row height to total height divided by number of rows\n   */\n  private _rowHeight: string;\n\n  /** The amount of space between tiles. This will be something like '5px' or '2em'. */\n  private _gutter: string = '1px';\n\n  /** Sets position and size styles for a tile */\n  private _tileStyler: TileStyler;\n\n  /** Query list of tiles that are being rendered. */\n  @ContentChildren(MatGridTile, {descendants: true}) _tiles: QueryList<MatGridTile>;\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  /** Amount of columns in the grid list. */\n  @Input()\n  get cols(): number {\n    return this._cols;\n  }\n  set cols(value: NumberInput) {\n    this._cols = Math.max(1, Math.round(coerceNumberProperty(value)));\n  }\n\n  /** Size of the grid list's gutter in pixels. */\n  @Input()\n  get gutterSize(): string {\n    return this._gutter;\n  }\n  set gutterSize(value: string) {\n    this._gutter = `${value == null ? '' : value}`;\n  }\n\n  /** Set internal representation of row height from the user-provided value. */\n  @Input()\n  get rowHeight(): string | number {\n    return this._rowHeight;\n  }\n  set rowHeight(value: string | number) {\n    const newValue = `${value == null ? '' : value}`;\n\n    if (newValue !== this._rowHeight) {\n      this._rowHeight = newValue;\n      this._setTileStyler(this._rowHeight);\n    }\n  }\n\n  ngOnInit() {\n    this._checkCols();\n    this._checkRowHeight();\n  }\n\n  /**\n   * The layout calculation is fairly cheap if nothing changes, so there's little cost\n   * to run it frequently.\n   */\n  ngAfterContentChecked() {\n    this._layoutTiles();\n  }\n\n  /** Throw a friendly error if cols property is missing */\n  private _checkCols() {\n    if (!this.cols && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(\n        `mat-grid-list: must pass in number of columns. ` + `Example: <mat-grid-list cols=\"3\">`,\n      );\n    }\n  }\n\n  /** Default to equal width:height if rowHeight property is missing */\n  private _checkRowHeight(): void {\n    if (!this._rowHeight) {\n      this._setTileStyler('1:1');\n    }\n  }\n\n  /** Creates correct Tile Styler subtype based on rowHeight passed in by user */\n  private _setTileStyler(rowHeight: string): void {\n    if (this._tileStyler) {\n      this._tileStyler.reset(this);\n    }\n\n    if (rowHeight === MAT_FIT_MODE) {\n      this._tileStyler = new FitTileStyler();\n    } else if (rowHeight && rowHeight.indexOf(':') > -1) {\n      this._tileStyler = new RatioTileStyler(rowHeight);\n    } else {\n      this._tileStyler = new FixedTileStyler(rowHeight);\n    }\n  }\n\n  /** Computes and applies the size and position for all children grid tiles. */\n  private _layoutTiles(): void {\n    if (!this._tileCoordinator) {\n      this._tileCoordinator = new TileCoordinator();\n    }\n\n    const tracker = this._tileCoordinator;\n    const tiles = this._tiles.filter(tile => !tile._gridList || tile._gridList === this);\n    const direction = this._dir ? this._dir.value : 'ltr';\n\n    this._tileCoordinator.update(this.cols, tiles);\n    this._tileStyler.init(this.gutterSize, tracker, this.cols, direction);\n\n    tiles.forEach((tile, index) => {\n      const pos = tracker.positions[index];\n      this._tileStyler.setStyle(tile, pos.row, pos.col);\n    });\n\n    this._setListStyle(this._tileStyler.getComputedHeight());\n  }\n\n  /** Sets style on the main grid-list element, given the style name and value. */\n  _setListStyle(style: [string, string | null] | null): void {\n    if (style) {\n      (this._element.nativeElement.style as any)[style[0]] = style[1];\n    }\n  }\n}\n", "<div>\n  <ng-content></ng-content>\n</div>", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatLineModule, MatCommonModule} from '../core';\nimport {\n  MatGridTile,\n  MatGridTileText,\n  MatGridTileFooterCssMatStyler,\n  MatGridTileHeaderCssMatStyler,\n  MatGridAvatarCssMatStyler,\n} from './grid-tile';\nimport {MatGridList} from './grid-list';\n\n// Export required to fix compiler confusion about import module paths\nexport {MatLine} from '../core';\n\n@NgModule({\n  imports: [\n    MatLineModule,\n    MatCommonModule,\n    MatGridList,\n    MatGridTile,\n    MatGridTileText,\n    MatGridTileHeaderCssMatStyler,\n    MatGridTileFooterCssMatStyler,\n    MatGridAvatarCssMatStyler,\n  ],\n  exports: [\n    MatGridList,\n    MatGridTile,\n    MatGridTileText,\n    MatLineModule,\n    MatCommonModule,\n    MatGridTileHeaderCssMatStyler,\n    MatGridTileFooterCssMatStyler,\n    MatGridAvatarCssMatStyler,\n  ],\n})\nexport class MatGridListModule {}\n"], "names": [], "mappings": ";;;;;;;;;;;AAUA;;;AAGG;AACI,MAAM,aAAa,GAAG,IAAI,cAAc,CAAkB,eAAe,CAAC;;MCyBpE,WAAW,CAAA;AACd,IAAA,QAAQ,GAAG,MAAM,CAA0B,UAAU,CAAC;IAC9D,SAAS,GAAI,MAAM,CAAkB,aAAa,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;IAErE,QAAQ,GAAW,CAAC;IACpB,QAAQ,GAAW,CAAC;AAGpB,IAAA,WAAA,GAAA;;AAGA,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ;;IAEtB,IAAI,OAAO,CAAC,KAAkB,EAAA;AAC5B,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;;;AAIzD,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ;;IAEtB,IAAI,OAAO,CAAC,KAAkB,EAAA;AAC5B,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;;AAGzD;;;AAGG;IACH,SAAS,CAAC,QAAgB,EAAE,KAAU,EAAA;QACnC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAa,CAAC,QAAQ,CAAC,GAAG,KAAK;;uGAjCnD,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAX,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,WAAW,yQCvCxB,8EAGA,EAAA,MAAA,EAAA,CAAA,69DAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FDoCa,WAAW,EAAA,UAAA,EAAA,CAAA;kBAfvB,SAAS;+BACE,eAAe,EAAA,QAAA,EACf,aAAa,EACjB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,eAAe;;;AAGxB,wBAAA,gBAAgB,EAAE,SAAS;AAC3B,wBAAA,gBAAgB,EAAE,SAAS;AAC5B,qBAAA,EAAA,aAAA,EAGc,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,8EAAA,EAAA,MAAA,EAAA,CAAA,69DAAA,CAAA,EAAA;wDAc3C,OAAO,EAAA,CAAA;sBADV;gBAUG,OAAO,EAAA,CAAA;sBADV;;MAuBU,eAAe,CAAA;AAClB,IAAA,QAAQ,GAAG,MAAM,CAA0B,UAAU,CAAC;AAEf,IAAA,MAAM;AAGrD,IAAA,WAAA,GAAA;IAEA,kBAAkB,GAAA;QAChB,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC;;uGAT3B,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAf,eAAe,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,4CAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,QAAA,EAAA,SAAA,EAGT,OAAO,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EErF1B,0MAGA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FF+Ea,eAAe,EAAA,UAAA,EAAA,CAAA;kBAN3B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,4CAA4C,mBAErC,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,QAAA,EAAA,0MAAA,EAAA;wDAKU,MAAM,EAAA,CAAA;sBAApD,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,OAAO,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;;AAU/C;;;AAGG;MAKU,yBAAyB,CAAA;uGAAzB,yBAAyB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAzB,yBAAyB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,oCAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,iBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAzB,yBAAyB,EAAA,UAAA,EAAA,CAAA;kBAJrC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oCAAoC;AAC9C,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,iBAAiB,EAAC;AACnC,iBAAA;;AAGD;;;AAGG;MAKU,6BAA6B,CAAA;uGAA7B,6BAA6B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAA7B,6BAA6B,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAA7B,6BAA6B,EAAA,UAAA,EAAA,CAAA;kBAJzC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sBAAsB;AAChC,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,sBAAsB,EAAC;AACxC,iBAAA;;AAGD;;;AAGG;MAKU,6BAA6B,CAAA;uGAA7B,6BAA6B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAA7B,6BAA6B,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAA7B,6BAA6B,EAAA,UAAA,EAAA,CAAA;kBAJzC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sBAAsB;AAChC,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,sBAAsB,EAAC;AACxC,iBAAA;;;AG9GD;;;AAGG;AACH,MAAM,mBAAmB,GAAG,+BAA+B;AAQ3D;;;;AAIG;MACmB,UAAU,CAAA;AAC9B,IAAA,WAAW;IACX,KAAK,GAAW,CAAC;IACjB,QAAQ,GAAW,CAAC;AACpB,IAAA,KAAK;AACL,IAAA,UAAU;AAEV;;;;;;;;AAQG;AACH,IAAA,IAAI,CAAC,UAAkB,EAAE,OAAwB,EAAE,IAAY,EAAE,SAAiB,EAAA;AAChF,QAAA,IAAI,CAAC,WAAW,GAAG,cAAc,CAAC,UAAU,CAAC;AAC7C,QAAA,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,QAAQ;AAC7B,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO;AAC/B,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI;AACjB,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS;;AAG7B;;;;;;AAMG;IACH,eAAe,CAAC,WAAmB,EAAE,cAAsB,EAAA;;;;;;QAMzD,OAAO,CAAA,CAAA,EAAI,WAAW,CAAQ,KAAA,EAAA,IAAI,CAAC,WAAW,CAAA,GAAA,EAAM,cAAc,CAAA,EAAA,CAAI;;AAGxE;;;;;AAKG;IACH,eAAe,CAAC,QAAgB,EAAE,MAAc,EAAA;;;QAG9C,OAAO,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,CAAI,CAAA,EAAA,QAAQ,MAAM,IAAI,CAAC,WAAW,CAAO,IAAA,EAAA,MAAM,CAAE,CAAA,CAAC;;AAGrF;;;;;AAKG;IACH,WAAW,CAAC,QAAgB,EAAE,IAAY,EAAA;AACxC,QAAA,OAAO,CAAI,CAAA,EAAA,QAAQ,CAAM,GAAA,EAAA,IAAI,CAAQ,KAAA,EAAA,IAAI,GAAG,CAAC,CAAM,GAAA,EAAA,IAAI,CAAC,WAAW,GAAG;;AAGxE;;;;;AAKG;AACH,IAAA,QAAQ,CAAC,IAAiB,EAAE,QAAgB,EAAE,QAAgB,EAAA;;AAE5D,QAAA,IAAI,mBAAmB,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK;;;AAI1C,QAAA,IAAI,0BAA0B,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK;QAE9D,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAE,mBAAmB,EAAE,0BAA0B,CAAC;QAClF,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAE,mBAAmB,EAAE,0BAA0B,CAAC;;;AAIpF,IAAA,YAAY,CAAC,IAAiB,EAAE,QAAgB,EAAE,YAAoB,EAAE,WAAmB,EAAA;;QAEzF,IAAI,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,WAAW,CAAC;;;AAInE,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,KAAK,KAAK,GAAG,OAAO,GAAG,MAAM;AACvD,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;AACnE,QAAA,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;;AAG9E;;AAEG;IACH,aAAa,GAAA;QACX,OAAO,CAAA,EAAG,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC,QAAQ,CAAA,KAAA,CAAO;;AAGvD;;;AAGG;AACH,IAAA,WAAW,CAAC,UAAkB,EAAA;AAC5B,QAAA,OAAO,CAAG,EAAA,IAAI,CAAC,QAAQ,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE;;AAehE;;;;AAIG;IACH,iBAAiB,GAAA;AACf,QAAA,OAAO,IAAI;;AASd;AAED;;;;AAIG;AACG,MAAO,eAAgB,SAAQ,UAAU,CAAA;AAC1B,IAAA,cAAA;AAAnB,IAAA,WAAA,CAAmB,cAAsB,EAAA;AACvC,QAAA,KAAK,EAAE;QADU,IAAc,CAAA,cAAA,GAAd,cAAc;;AAIxB,IAAA,IAAI,CAAC,UAAkB,EAAE,OAAwB,EAAE,IAAY,EAAE,SAAiB,EAAA;QACzF,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC;QAChD,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC;QAEzD,IACE,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC;aAC7C,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAC/C;YACA,MAAM,KAAK,CAAC,CAAkB,eAAA,EAAA,IAAI,CAAC,cAAc,CAAA,mBAAA,CAAqB,CAAC;;;IAIlE,YAAY,CAAC,IAAiB,EAAE,QAAgB,EAAA;AACvD,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;QAC1E,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;;IAG5E,iBAAiB,GAAA;QACxB,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAG,EAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,CAAA,CAAE,CAAC,CAAC;;AAGtF,IAAA,KAAK,CAAC,IAAqB,EAAA;QAClC,IAAI,CAAC,aAAa,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AAEpC,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,IAAG;AACzB,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC;AAC3B,gBAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC;AAChC,aAAC,CAAC;;;AAGP;AAED;;;;AAIG;AACG,MAAO,eAAgB,SAAQ,UAAU,CAAA;;AAE7C,IAAA,cAAc;AACd,IAAA,cAAc;AAEd,IAAA,WAAA,CAAY,KAAa,EAAA;AACvB,QAAA,KAAK,EAAE;AACP,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;;AAGzB,IAAA,YAAY,CACV,IAAiB,EACjB,QAAgB,EAChB,YAAoB,EACpB,WAAmB,EAAA;AAEnB,QAAA,IAAI,oBAAoB,GAAG,YAAY,GAAG,IAAI,CAAC,cAAc;QAC7D,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,oBAAoB,EAAE,WAAW,CAAC;;;;AAK7E,QAAA,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;QAChF,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;;IAGhF,iBAAiB,GAAA;QACxB,OAAO;YACL,eAAe;AACf,YAAA,IAAI,CAAC,CAAG,EAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC;SAC3E;;AAGH,IAAA,KAAK,CAAC,IAAqB,EAAA;QACzB,IAAI,CAAC,aAAa,CAAC,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;AAE3C,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,IAAG;AACzB,YAAA,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC;AACjC,YAAA,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC;AACpC,SAAC,CAAC;;AAGI,IAAA,WAAW,CAAC,KAAa,EAAA;QAC/B,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC;AAEnC,QAAA,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAC9E,YAAA,MAAM,KAAK,CAAC,CAAA,oDAAA,EAAuD,KAAK,CAAA,CAAA,CAAG,CAAC;;AAG9E,QAAA,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;;AAE9E;AAED;;;;;;AAMG;AACG,MAAO,aAAc,SAAQ,UAAU,CAAA;IAC3C,YAAY,CAAC,IAAiB,EAAE,QAAgB,EAAA;;AAE9C,QAAA,IAAI,oBAAoB,GAAG,GAAG,GAAG,IAAI,CAAC,QAAQ;;AAG9C,QAAA,IAAI,mBAAmB,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK;;QAGvD,IAAI,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,oBAAoB,EAAE,mBAAmB,CAAC;AAEpF,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;AACrE,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;;AAGhF,IAAA,KAAK,CAAC,IAAqB,EAAA;AACzB,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,IAAG;AACzB,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC;AAC3B,gBAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC;AAChC,aAAC,CAAC;;;AAGP;AAED;AACA,SAAS,IAAI,CAAC,GAAW,EAAA;IACvB,OAAO,CAAA,KAAA,EAAQ,GAAG,CAAA,CAAA,CAAG;AACvB;AAEA;AACA,SAAS,cAAc,CAAC,KAAa,EAAA;AACnC,IAAA,OAAO,KAAK,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,KAAK,GAAG,CAAG,EAAA,KAAK,IAAI;AAC5D;;ACjRA;AACA;AACA;AAEA,MAAM,YAAY,GAAG,KAAK;MAsBb,WAAW,CAAA;AACd,IAAA,QAAQ,GAAG,MAAM,CAA0B,UAAU,CAAC;IACtD,IAAI,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;;AAG/C,IAAA,KAAK;;AAGL,IAAA,gBAAgB;AAExB;;;;;AAKG;AACK,IAAA,UAAU;;IAGV,OAAO,GAAW,KAAK;;AAGvB,IAAA,WAAW;;AAGgC,IAAA,MAAM;AAGzD,IAAA,WAAA,GAAA;;AAGA,IAAA,IACI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,KAAK;;IAEnB,IAAI,IAAI,CAAC,KAAkB,EAAA;AACzB,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC;;;AAInE,IAAA,IACI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,OAAO;;IAErB,IAAI,UAAU,CAAC,KAAa,EAAA;AAC1B,QAAA,IAAI,CAAC,OAAO,GAAG,CAAG,EAAA,KAAK,IAAI,IAAI,GAAG,EAAE,GAAG,KAAK,EAAE;;;AAIhD,IAAA,IACI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU;;IAExB,IAAI,SAAS,CAAC,KAAsB,EAAA;AAClC,QAAA,MAAM,QAAQ,GAAG,CAAG,EAAA,KAAK,IAAI,IAAI,GAAG,EAAE,GAAG,KAAK,EAAE;AAEhD,QAAA,IAAI,QAAQ,KAAK,IAAI,CAAC,UAAU,EAAE;AAChC,YAAA,IAAI,CAAC,UAAU,GAAG,QAAQ;AAC1B,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC;;;IAIxC,QAAQ,GAAA;QACN,IAAI,CAAC,UAAU,EAAE;QACjB,IAAI,CAAC,eAAe,EAAE;;AAGxB;;;AAGG;IACH,qBAAqB,GAAA;QACnB,IAAI,CAAC,YAAY,EAAE;;;IAIb,UAAU,GAAA;AAChB,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AACjE,YAAA,MAAM,KAAK,CACT,CAAA,+CAAA,CAAiD,GAAG,CAAA,iCAAA,CAAmC,CACxF;;;;IAKG,eAAe,GAAA;AACrB,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACpB,YAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;;;;AAKtB,IAAA,cAAc,CAAC,SAAiB,EAAA;AACtC,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC;;AAG9B,QAAA,IAAI,SAAS,KAAK,YAAY,EAAE;AAC9B,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,aAAa,EAAE;;AACjC,aAAA,IAAI,SAAS,IAAI,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;YACnD,IAAI,CAAC,WAAW,GAAG,IAAI,eAAe,CAAC,SAAS,CAAC;;aAC5C;YACL,IAAI,CAAC,WAAW,GAAG,IAAI,eAAe,CAAC,SAAS,CAAC;;;;IAK7C,YAAY,GAAA;AAClB,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;AAC1B,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,eAAe,EAAE;;AAG/C,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB;QACrC,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC;AACpF,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK;QAErD,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC;AAC9C,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC;QAErE,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAI;YAC5B,MAAM,GAAG,GAAG,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC;AACpC,YAAA,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC;AACnD,SAAC,CAAC;QAEF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAC;;;AAI1D,IAAA,aAAa,CAAC,KAAqC,EAAA;QACjD,IAAI,KAAK,EAAE;AACR,YAAA,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;;;uGAlIxD,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAX,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,WAAW,EATX,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,cAAA,EAAA,eAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,aAAa;AACtB,gBAAA,WAAW,EAAE,WAAW;AACzB,aAAA;SACF,EA6BgB,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,QAAA,EAAA,SAAA,EAAA,WAAW,2ECpF9B,4CAEM,EAAA,MAAA,EAAA,CAAA,69DAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FDyDO,WAAW,EAAA,UAAA,EAAA,CAAA;kBApBvB,SAAS;+BACE,eAAe,EAAA,QAAA,EACf,aAAa,EAGjB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,eAAe;;;AAGxB,wBAAA,aAAa,EAAE,MAAM;qBACtB,EACU,SAAA,EAAA;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,aAAa;AACtB,4BAAA,WAAW,EAAa,WAAA;AACzB,yBAAA;AACF,qBAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,QAAA,EAAA,4CAAA,EAAA,MAAA,EAAA,CAAA,69DAAA,CAAA,EAAA;wDA2Bc,MAAM,EAAA,CAAA;sBAAxD,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,WAAW,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;gBAO7C,IAAI,EAAA,CAAA;sBADP;gBAUG,UAAU,EAAA,CAAA;sBADb;gBAUG,SAAS,EAAA,CAAA;sBADZ;;;MEhEU,iBAAiB,CAAA;uGAAjB,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,YApB1B,aAAa;YACb,eAAe;YACf,WAAW;YACX,WAAW;YACX,eAAe;YACf,6BAA6B;YAC7B,6BAA6B;AAC7B,YAAA,yBAAyB,aAGzB,WAAW;YACX,WAAW;YACX,eAAe;YACf,aAAa;YACb,eAAe;YACf,6BAA6B;YAC7B,6BAA6B;YAC7B,yBAAyB,CAAA,EAAA,CAAA;AAGhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,YApB1B,aAAa;AACb,YAAA,eAAe,EAYf,aAAa;YACb,eAAe,CAAA,EAAA,CAAA;;2FAMN,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAtB7B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE;wBACP,aAAa;wBACb,eAAe;wBACf,WAAW;wBACX,WAAW;wBACX,eAAe;wBACf,6BAA6B;wBAC7B,6BAA6B;wBAC7B,yBAAyB;AAC1B,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,WAAW;wBACX,WAAW;wBACX,eAAe;wBACf,aAAa;wBACb,eAAe;wBACf,6BAA6B;wBAC7B,6BAA6B;wBAC7B,yBAAyB;AAC1B,qBAAA;AACF,iBAAA;;;;;"}