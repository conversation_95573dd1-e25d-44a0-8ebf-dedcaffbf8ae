{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/progress-bar/testing/progress-bar-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {coerceNumberProperty} from '@angular/cdk/coercion';\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {ProgressBarHarnessFilters} from './progress-bar-harness-filters';\n\n/** <PERSON>rness for interacting with a `mat-progress-bar` in tests. */\nexport class MatProgressBarHarness extends ComponentHarness {\n  static hostSelector = '.mat-mdc-progress-bar';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a progress bar with specific\n   * attributes.\n   * @param options Options for filtering which progress bar instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatProgressBarHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: ProgressBarHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options);\n  }\n\n  /** Gets a promise for the progress bar's value. */\n  async getValue(): Promise<number | null> {\n    const host = await this.host();\n    const ariaValue = await host.getAttribute('aria-valuenow');\n    return ariaValue ? coerceNumberProperty(ariaValue) : null;\n  }\n\n  /** Gets a promise for the progress bar's mode. */\n  async getMode(): Promise<string | null> {\n    return (await this.host()).getAttribute('mode');\n  }\n}\n"], "names": [], "mappings": ";;;AAgBA;AACM,MAAO,qBAAsB,SAAQ,gBAAgB,CAAA;AACzD,IAAA,OAAO,YAAY,GAAG,uBAAuB;AAE7C;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAqC,EAAE,EAAA;AAEvC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC;;;AAI5C,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;QAC9B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC;AAC1D,QAAA,OAAO,SAAS,GAAG,oBAAoB,CAAC,SAAS,CAAC,GAAG,IAAI;;;AAI3D,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,MAAM,CAAC;;;;;;"}