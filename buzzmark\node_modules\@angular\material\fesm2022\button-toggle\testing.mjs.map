{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/button-toggle/testing/button-toggle-harness.ts", "../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/button-toggle/testing/button-toggle-group-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ComponentHarness, HarnessPredicate, parallel} from '@angular/cdk/testing';\nimport {coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {MatButtonToggleAppearance} from '../button-toggle';\nimport {ButtonToggleHarnessFilters} from './button-toggle-harness-filters';\n\n/** Harness for interacting with a standard mat-button-toggle in tests. */\nexport class MatButtonToggleHarness extends ComponentHarness {\n  /** The selector for the host element of a `MatButton` instance. */\n  static hostSelector = '.mat-button-toggle';\n\n  private _label = this.locatorFor('.mat-button-toggle-label-content');\n  private _button = this.locatorFor('.mat-button-toggle-button');\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatButtonToggleHarness` that meets\n   * certain criteria.\n   * @param options Options for filtering which button toggle instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: ButtonToggleHarnessFilters = {}): HarnessPredicate<MatButtonToggleHarness> {\n    return new HarnessPredicate(MatButtonToggleHarness, options)\n      .addOption('text', options.text, (harness, text) =>\n        HarnessPredicate.stringMatches(harness.getText(), text),\n      )\n      .addOption('name', options.name, (harness, name) =>\n        HarnessPredicate.stringMatches(harness.getName(), name),\n      )\n      .addOption(\n        'checked',\n        options.checked,\n        async (harness, checked) => (await harness.isChecked()) === checked,\n      )\n      .addOption('disabled', options.disabled, async (harness, disabled) => {\n        return (await harness.isDisabled()) === disabled;\n      });\n  }\n\n  /** Gets a boolean promise indicating if the button toggle is checked. */\n  async isChecked(): Promise<boolean> {\n    const button = await this._button();\n    const [checked, pressed] = await parallel(() => [\n      button.getAttribute('aria-checked'),\n      button.getAttribute('aria-pressed'),\n    ]);\n    return coerceBooleanProperty(checked) || coerceBooleanProperty(pressed);\n  }\n\n  /** Gets a boolean promise indicating if the button toggle is disabled. */\n  async isDisabled(): Promise<boolean> {\n    const host = await this.host();\n    return host.hasClass('mat-button-toggle-disabled');\n  }\n\n  /** Gets a promise for the button toggle's name. */\n  async getName(): Promise<string | null> {\n    return (await this._button()).getAttribute('name');\n  }\n\n  /** Gets a promise for the button toggle's aria-label. */\n  async getAriaLabel(): Promise<string | null> {\n    return (await this._button()).getAttribute('aria-label');\n  }\n\n  /** Gets a promise for the button toggles's aria-labelledby. */\n  async getAriaLabelledby(): Promise<string | null> {\n    return (await this._button()).getAttribute('aria-labelledby');\n  }\n\n  /** Gets a promise for the button toggle's text. */\n  async getText(): Promise<string> {\n    return (await this._label()).text();\n  }\n\n  /** Gets the appearance that the button toggle is using. */\n  async getAppearance(): Promise<MatButtonToggleAppearance> {\n    const host = await this.host();\n    const className = 'mat-button-toggle-appearance-standard';\n    return (await host.hasClass(className)) ? 'standard' : 'legacy';\n  }\n\n  /** Focuses the toggle. */\n  async focus(): Promise<void> {\n    return (await this._button()).focus();\n  }\n\n  /** Blurs the toggle. */\n  async blur(): Promise<void> {\n    return (await this._button()).blur();\n  }\n\n  /** Whether the toggle is focused. */\n  async isFocused(): Promise<boolean> {\n    return (await this._button()).isFocused();\n  }\n\n  /** Toggle the checked state of the buttons toggle. */\n  async toggle(): Promise<void> {\n    return (await this._button()).click();\n  }\n\n  /**\n   * Puts the button toggle in a checked state by toggling it if it's\n   * currently unchecked, or doing nothing if it is already checked.\n   */\n  async check(): Promise<void> {\n    if (!(await this.isChecked())) {\n      await this.toggle();\n    }\n  }\n\n  /**\n   * Puts the button toggle in an unchecked state by toggling it if it's\n   * currently checked, or doing nothing if it's already unchecked.\n   */\n  async uncheck(): Promise<void> {\n    if (await this.isChecked()) {\n      await this.toggle();\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ComponentHarness, HarnessPredicate} from '@angular/cdk/testing';\nimport {MatButtonToggleAppearance} from '../button-toggle';\nimport {ButtonToggleGroupHarnessFilters} from './button-toggle-group-harness-filters';\nimport {ButtonToggleHarnessFilters} from './button-toggle-harness-filters';\nimport {MatButtonToggleHarness} from './button-toggle-harness';\n\n/** Harness for interacting with a standard mat-button-toggle in tests. */\nexport class MatButtonToggleGroupHarness extends ComponentHarness {\n  /** The selector for the host element of a `MatButton` instance. */\n  static hostSelector = '.mat-button-toggle-group';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatButtonToggleGroupHarness`\n   * that meets certain criteria.\n   * @param options Options for filtering which button toggle instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(\n    options: ButtonToggleGroupHarnessFilters = {},\n  ): HarnessPredicate<MatButtonToggleGroupHarness> {\n    return new HarnessPredicate(MatButtonToggleGroupHarness, options).addOption(\n      'disabled',\n      options.disabled,\n      async (harness, disabled) => {\n        return (await harness.isDisabled()) === disabled;\n      },\n    );\n  }\n\n  /**\n   * Gets the button toggles that are inside the group.\n   * @param filter Optionally filters which toggles are included.\n   */\n  async getToggles(filter: ButtonToggleHarnessFilters = {}): Promise<MatButtonToggleHarness[]> {\n    return this.locatorForAll(MatButtonToggleHarness.with(filter))();\n  }\n\n  /** Gets whether the button toggle group is disabled. */\n  async isDisabled(): Promise<boolean> {\n    return (await (await this.host()).getAttribute('aria-disabled')) === 'true';\n  }\n\n  /** Gets whether the button toggle group is laid out vertically. */\n  async isVertical(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-button-toggle-vertical');\n  }\n\n  /** Gets the appearance that the group is using. */\n  async getAppearance(): Promise<MatButtonToggleAppearance> {\n    const host = await this.host();\n    const className = 'mat-button-toggle-group-appearance-standard';\n    return (await host.hasClass(className)) ? 'standard' : 'legacy';\n  }\n}\n"], "names": [], "mappings": ";;;AAaA;AACM,MAAO,sBAAuB,SAAQ,gBAAgB,CAAA;;AAE1D,IAAA,OAAO,YAAY,GAAG,oBAAoB;AAElC,IAAA,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,kCAAkC,CAAC;AAC5D,IAAA,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,2BAA2B,CAAC;AAE9D;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAAsC,EAAE,EAAA;AAClD,QAAA,OAAO,IAAI,gBAAgB,CAAC,sBAAsB,EAAE,OAAO;aACxD,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,IAAI,KAC7C,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC;aAExD,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,IAAI,KAC7C,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC;aAExD,SAAS,CACR,SAAS,EACT,OAAO,CAAC,OAAO,EACf,OAAO,OAAO,EAAE,OAAO,KAAK,CAAC,MAAM,OAAO,CAAC,SAAS,EAAE,MAAM,OAAO;AAEpE,aAAA,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,OAAO,EAAE,QAAQ,KAAI;YACnE,OAAO,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,QAAQ;AAClD,SAAC,CAAC;;;AAIN,IAAA,MAAM,SAAS,GAAA;AACb,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE;QACnC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,MAAM,QAAQ,CAAC,MAAM;AAC9C,YAAA,MAAM,CAAC,YAAY,CAAC,cAAc,CAAC;AACnC,YAAA,MAAM,CAAC,YAAY,CAAC,cAAc,CAAC;AACpC,SAAA,CAAC;QACF,OAAO,qBAAqB,CAAC,OAAO,CAAC,IAAI,qBAAqB,CAAC,OAAO,CAAC;;;AAIzE,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;AAC9B,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,4BAA4B,CAAC;;;AAIpD,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE,YAAY,CAAC,MAAM,CAAC;;;AAIpD,IAAA,MAAM,YAAY,GAAA;AAChB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE,YAAY,CAAC,YAAY,CAAC;;;AAI1D,IAAA,MAAM,iBAAiB,GAAA;AACrB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE,YAAY,CAAC,iBAAiB,CAAC;;;AAI/D,IAAA,MAAM,OAAO,GAAA;QACX,OAAO,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE;;;AAIrC,IAAA,MAAM,aAAa,GAAA;AACjB,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;QAC9B,MAAM,SAAS,GAAG,uCAAuC;AACzD,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,UAAU,GAAG,QAAQ;;;AAIjE,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE;;;AAIvC,IAAA,MAAM,IAAI,GAAA;QACR,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE;;;AAItC,IAAA,MAAM,SAAS,GAAA;QACb,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE;;;AAI3C,IAAA,MAAM,MAAM,GAAA;QACV,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE;;AAGvC;;;AAGG;AACH,IAAA,MAAM,KAAK,GAAA;QACT,IAAI,EAAE,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE;AAC7B,YAAA,MAAM,IAAI,CAAC,MAAM,EAAE;;;AAIvB;;;AAGG;AACH,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,IAAI,MAAM,IAAI,CAAC,SAAS,EAAE,EAAE;AAC1B,YAAA,MAAM,IAAI,CAAC,MAAM,EAAE;;;;;AC9GzB;AACM,MAAO,2BAA4B,SAAQ,gBAAgB,CAAA;;AAE/D,IAAA,OAAO,YAAY,GAAG,0BAA0B;AAEhD;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CACT,OAAA,GAA2C,EAAE,EAAA;QAE7C,OAAO,IAAI,gBAAgB,CAAC,2BAA2B,EAAE,OAAO,CAAC,CAAC,SAAS,CACzE,UAAU,EACV,OAAO,CAAC,QAAQ,EAChB,OAAO,OAAO,EAAE,QAAQ,KAAI;YAC1B,OAAO,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,QAAQ;AAClD,SAAC,CACF;;AAGH;;;AAGG;AACH,IAAA,MAAM,UAAU,CAAC,MAAA,GAAqC,EAAE,EAAA;AACtD,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE;;;AAIlE,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,eAAe,CAAC,MAAM,MAAM;;;AAI7E,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,4BAA4B,CAAC;;;AAInE,IAAA,MAAM,aAAa,GAAA;AACjB,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;QAC9B,MAAM,SAAS,GAAG,6CAA6C;AAC/D,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,UAAU,GAAG,QAAQ;;;;;;"}