{"version": 3, "file": "expansion.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/expansion/accordion-base.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/expansion/expansion-panel-base.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/expansion/expansion-panel-content.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/expansion/expansion-panel.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/expansion/expansion-panel.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/expansion/expansion-panel-header.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/expansion/expansion-panel-header.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/expansion/accordion.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/expansion/expansion-module.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/expansion/expansion-animations.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken} from '@angular/core';\nimport {CdkAccordion} from '@angular/cdk/accordion';\n\n/** MatAccordion's display modes. */\nexport type MatAccordionDisplayMode = 'default' | 'flat';\n\n/** MatAccordion's toggle positions. */\nexport type MatAccordionTogglePosition = 'before' | 'after';\n\n/**\n * Base interface for a `MatAccordion`.\n * @docs-private\n */\nexport interface MatAccordionBase extends CdkAccordion {\n  /** Whether the expansion indicator should be hidden. */\n  hideToggle: boolean;\n\n  /** Display mode used for all expansion panels in the accordion. */\n  displayMode: MatAccordionDisplayMode;\n\n  /** The position of the expansion indicator. */\n  togglePosition: MatAccordionTogglePosition;\n\n  /** Handles keyboard events coming in from the panel headers. */\n  _handleHeaderKeydown: (event: KeyboardEvent) => void;\n\n  /** Handles focus events on the panel headers. */\n  _handleHeaderFocus: (header: any) => void;\n}\n\n/**\n * <PERSON>ken used to provide a `MatAccordion` to `MatExpansionPanel`.\n * Used primarily to avoid circular imports between `MatAccordion` and `MatExpansionPanel`.\n */\nexport const MAT_ACCORDION = new InjectionToken<MatAccordionBase>('MAT_ACCORDION');\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken} from '@angular/core';\nimport {CdkAccordionItem} from '@angular/cdk/accordion';\n\n/**\n * Base interface for a `MatExpansionPanel`.\n * @docs-private\n */\nexport interface MatExpansionPanelBase extends CdkAccordionItem {\n  /** Whether the toggle indicator should be hidden. */\n  hideToggle: boolean;\n}\n\n/**\n * Token used to provide a `MatExpansionPanel` to `MatExpansionPanelContent`.\n * Used to avoid circular imports between `MatExpansionPanel` and `MatExpansionPanelContent`.\n */\nexport const MAT_EXPANSION_PANEL = new InjectionToken<MatExpansionPanelBase>('MAT_EXPANSION_PANEL');\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, TemplateRef, inject} from '@angular/core';\nimport {MAT_EXPANSION_PANEL, MatExpansionPanelBase} from './expansion-panel-base';\n\n/**\n * Expansion panel content that will be rendered lazily\n * after the panel is opened for the first time.\n */\n@Directive({\n  selector: 'ng-template[matExpansionPanelContent]',\n})\nexport class MatExpansionPanelContent {\n  _template = inject<TemplateRef<any>>(TemplateRef);\n  _expansionPanel = inject<MatExpansionPanelBase>(MAT_EXPANSION_PANEL, {optional: true});\n\n  constructor(...args: unknown[]);\n  constructor() {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {CdkAccordionItem} from '@angular/cdk/accordion';\nimport {UniqueSelectionDispatcher} from '@angular/cdk/collections';\nimport {CdkPortalOutlet, TemplatePortal} from '@angular/cdk/portal';\nimport {DOCUMENT} from '@angular/common';\nimport {\n  AfterContentInit,\n  ChangeDetectionStrategy,\n  Component,\n  ContentChild,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  InjectionToken,\n  Input,\n  OnChanges,\n  OnDestroy,\n  Output,\n  SimpleChanges,\n  ViewChild,\n  ViewContainerRef,\n  ViewEncapsulation,\n  booleanAttribute,\n  ANIMATION_MODULE_TYPE,\n  inject,\n  NgZone,\n  Renderer2,\n} from '@angular/core';\nimport {_IdGenerator} from '@angular/cdk/a11y';\nimport {Subject} from 'rxjs';\nimport {filter, startWith, take} from 'rxjs/operators';\nimport {MatAccordionBase, MatAccordionTogglePosition, MAT_ACCORDION} from './accordion-base';\nimport {MAT_EXPANSION_PANEL} from './expansion-panel-base';\nimport {MatExpansionPanelContent} from './expansion-panel-content';\n\n/** MatExpansionPanel's states. */\nexport type MatExpansionPanelState = 'expanded' | 'collapsed';\n\n/**\n * Object that can be used to override the default options\n * for all of the expansion panels in a module.\n */\nexport interface MatExpansionPanelDefaultOptions {\n  /** Height of the header while the panel is expanded. */\n  expandedHeight: string;\n\n  /** Height of the header while the panel is collapsed. */\n  collapsedHeight: string;\n\n  /** Whether the toggle indicator should be hidden. */\n  hideToggle: boolean;\n}\n\n/**\n * Injection token that can be used to configure the default\n * options for the expansion panel component.\n */\nexport const MAT_EXPANSION_PANEL_DEFAULT_OPTIONS =\n  new InjectionToken<MatExpansionPanelDefaultOptions>('MAT_EXPANSION_PANEL_DEFAULT_OPTIONS');\n\n/**\n * This component can be used as a single element to show expandable content, or as one of\n * multiple children of an element with the MatAccordion directive attached.\n */\n@Component({\n  styleUrl: 'expansion-panel.css',\n  selector: 'mat-expansion-panel',\n  exportAs: 'matExpansionPanel',\n  templateUrl: 'expansion-panel.html',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [\n    // Provide MatAccordion as undefined to prevent nested expansion panels from registering\n    // to the same accordion.\n    {provide: MAT_ACCORDION, useValue: undefined},\n    {provide: MAT_EXPANSION_PANEL, useExisting: MatExpansionPanel},\n  ],\n  host: {\n    'class': 'mat-expansion-panel',\n    '[class.mat-expanded]': 'expanded',\n    '[class.mat-expansion-panel-spacing]': '_hasSpacing()',\n  },\n  imports: [CdkPortalOutlet],\n})\nexport class MatExpansionPanel\n  extends CdkAccordionItem\n  implements AfterContentInit, OnChanges, OnDestroy\n{\n  private _viewContainerRef = inject(ViewContainerRef);\n  private readonly _animationsDisabled =\n    inject(ANIMATION_MODULE_TYPE, {optional: true}) === 'NoopAnimations';\n  private _document = inject(DOCUMENT);\n  private _ngZone = inject(NgZone);\n  private _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _renderer = inject(Renderer2);\n  private _cleanupTransitionEnd: (() => void) | undefined;\n\n  /** Whether the toggle indicator should be hidden. */\n  @Input({transform: booleanAttribute})\n  get hideToggle(): boolean {\n    return this._hideToggle || (this.accordion && this.accordion.hideToggle);\n  }\n  set hideToggle(value: boolean) {\n    this._hideToggle = value;\n  }\n  private _hideToggle = false;\n\n  /** The position of the expansion indicator. */\n  @Input()\n  get togglePosition(): MatAccordionTogglePosition {\n    return this._togglePosition || (this.accordion && this.accordion.togglePosition);\n  }\n  set togglePosition(value: MatAccordionTogglePosition) {\n    this._togglePosition = value;\n  }\n  private _togglePosition: MatAccordionTogglePosition;\n\n  /** An event emitted after the body's expansion animation happens. */\n  @Output() readonly afterExpand = new EventEmitter<void>();\n\n  /** An event emitted after the body's collapse animation happens. */\n  @Output() readonly afterCollapse = new EventEmitter<void>();\n\n  /** Stream that emits for changes in `@Input` properties. */\n  readonly _inputChanges = new Subject<SimpleChanges>();\n\n  /** Optionally defined accordion the expansion panel belongs to. */\n  override accordion = inject<MatAccordionBase>(MAT_ACCORDION, {optional: true, skipSelf: true})!;\n\n  /** Content that will be rendered lazily. */\n  @ContentChild(MatExpansionPanelContent) _lazyContent: MatExpansionPanelContent;\n\n  /** Element containing the panel's user-provided content. */\n  @ViewChild('body') _body: ElementRef<HTMLElement>;\n\n  /** Element wrapping the panel body. */\n  @ViewChild('bodyWrapper')\n  protected _bodyWrapper: ElementRef<HTMLElement> | undefined;\n\n  /** Portal holding the user's content. */\n  _portal: TemplatePortal;\n\n  /** ID for the associated header element. Used for a11y labelling. */\n  _headerId: string = inject(_IdGenerator).getId('mat-expansion-panel-header-');\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n\n    const defaultOptions = inject<MatExpansionPanelDefaultOptions>(\n      MAT_EXPANSION_PANEL_DEFAULT_OPTIONS,\n      {optional: true},\n    );\n\n    this._expansionDispatcher = inject(UniqueSelectionDispatcher);\n\n    if (defaultOptions) {\n      this.hideToggle = defaultOptions.hideToggle;\n    }\n  }\n\n  /** Determines whether the expansion panel should have spacing between it and its siblings. */\n  _hasSpacing(): boolean {\n    if (this.accordion) {\n      return this.expanded && this.accordion.displayMode === 'default';\n    }\n    return false;\n  }\n\n  /** Gets the expanded state string. */\n  _getExpandedState(): MatExpansionPanelState {\n    return this.expanded ? 'expanded' : 'collapsed';\n  }\n\n  /** Toggles the expanded state of the expansion panel. */\n  override toggle(): void {\n    this.expanded = !this.expanded;\n  }\n\n  /** Sets the expanded state of the expansion panel to false. */\n  override close(): void {\n    this.expanded = false;\n  }\n\n  /** Sets the expanded state of the expansion panel to true. */\n  override open(): void {\n    this.expanded = true;\n  }\n\n  ngAfterContentInit() {\n    if (this._lazyContent && this._lazyContent._expansionPanel === this) {\n      // Render the content as soon as the panel becomes open.\n      this.opened\n        .pipe(\n          startWith(null),\n          filter(() => this.expanded && !this._portal),\n          take(1),\n        )\n        .subscribe(() => {\n          this._portal = new TemplatePortal(this._lazyContent._template, this._viewContainerRef);\n        });\n    }\n\n    this._setupAnimationEvents();\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    this._inputChanges.next(changes);\n  }\n\n  override ngOnDestroy() {\n    super.ngOnDestroy();\n    this._cleanupTransitionEnd?.();\n    this._inputChanges.complete();\n  }\n\n  /** Checks whether the expansion panel's content contains the currently-focused element. */\n  _containsFocus(): boolean {\n    if (this._body) {\n      const focusedElement = this._document.activeElement;\n      const bodyElement = this._body.nativeElement;\n      return focusedElement === bodyElement || bodyElement.contains(focusedElement);\n    }\n\n    return false;\n  }\n\n  private _transitionEndListener = ({target, propertyName}: TransitionEvent) => {\n    if (target === this._bodyWrapper?.nativeElement && propertyName === 'grid-template-rows') {\n      this._ngZone.run(() => {\n        if (this.expanded) {\n          this.afterExpand.emit();\n        } else {\n          this.afterCollapse.emit();\n        }\n      });\n    }\n  };\n\n  protected _setupAnimationEvents() {\n    // This method is defined separately, because we need to\n    // disable this logic in some internal components.\n    this._ngZone.runOutsideAngular(() => {\n      if (this._animationsDisabled) {\n        this.opened.subscribe(() => this._ngZone.run(() => this.afterExpand.emit()));\n        this.closed.subscribe(() => this._ngZone.run(() => this.afterCollapse.emit()));\n      } else {\n        setTimeout(() => {\n          const element = this._elementRef.nativeElement;\n          this._cleanupTransitionEnd = this._renderer.listen(\n            element,\n            'transitionend',\n            this._transitionEndListener,\n          );\n          element.classList.add('mat-expansion-panel-animations-enabled');\n        }, 200);\n      }\n    });\n  }\n}\n\n/**\n * Actions of a `<mat-expansion-panel>`.\n */\n@Directive({\n  selector: 'mat-action-row',\n  host: {\n    class: 'mat-action-row',\n  },\n})\nexport class MatExpansionPanelActionRow {}\n", "<ng-content select=\"mat-expansion-panel-header\"></ng-content>\n<div class=\"mat-expansion-panel-content-wrapper\" [attr.inert]=\"expanded ? null : ''\" #bodyWrapper>\n  <div class=\"mat-expansion-panel-content\"\n       role=\"region\"\n       [attr.aria-labelledby]=\"_headerId\"\n       [id]=\"id\"\n       #body>\n    <div class=\"mat-expansion-panel-body\">\n      <ng-content></ng-content>\n      <ng-template [cdkPortalOutlet]=\"_portal\"></ng-template>\n    </div>\n    <ng-content select=\"mat-action-row\"></ng-content>\n  </div>\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {FocusableOption, FocusMonitor, FocusOrigin} from '@angular/cdk/a11y';\nimport {ENTER, hasModifier<PERSON>ey, SPACE} from '@angular/cdk/keycodes';\nimport {\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  Directive,\n  ElementRef,\n  Input,\n  numberAttribute,\n  OnDestroy,\n  ViewEncapsulation,\n  inject,\n  HostAttributeToken,\n} from '@angular/core';\nimport {EMPTY, merge, Subscription} from 'rxjs';\nimport {filter} from 'rxjs/operators';\nimport {MatAccordionTogglePosition} from './accordion-base';\nimport {\n  MatExpansionPanel,\n  MatExpansionPanelDefaultOptions,\n  MAT_EXPANSION_PANEL_DEFAULT_OPTIONS,\n} from './expansion-panel';\nimport {_CdkPrivateStyleLoader} from '@angular/cdk/private';\nimport {_StructuralStylesLoader} from '../core';\n\n/**\n * Header element of a `<mat-expansion-panel>`.\n */\n@Component({\n  selector: 'mat-expansion-panel-header',\n  styleUrl: 'expansion-panel-header.css',\n  templateUrl: 'expansion-panel-header.html',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  host: {\n    'class': 'mat-expansion-panel-header mat-focus-indicator',\n    'role': 'button',\n    '[attr.id]': 'panel._headerId',\n    '[attr.tabindex]': 'disabled ? -1 : tabIndex',\n    '[attr.aria-controls]': '_getPanelId()',\n    '[attr.aria-expanded]': '_isExpanded()',\n    '[attr.aria-disabled]': 'panel.disabled',\n    '[class.mat-expanded]': '_isExpanded()',\n    '[class.mat-expansion-toggle-indicator-after]': `_getTogglePosition() === 'after'`,\n    '[class.mat-expansion-toggle-indicator-before]': `_getTogglePosition() === 'before'`,\n    '[style.height]': '_getHeaderHeight()',\n    '(click)': '_toggle()',\n    '(keydown)': '_keydown($event)',\n  },\n})\nexport class MatExpansionPanelHeader implements AfterViewInit, OnDestroy, FocusableOption {\n  panel = inject(MatExpansionPanel, {host: true});\n  private _element = inject(ElementRef);\n  private _focusMonitor = inject(FocusMonitor);\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n\n  private _parentChangeSubscription = Subscription.EMPTY;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    const panel = this.panel;\n    const defaultOptions = inject<MatExpansionPanelDefaultOptions>(\n      MAT_EXPANSION_PANEL_DEFAULT_OPTIONS,\n      {optional: true},\n    );\n    const tabIndex = inject(new HostAttributeToken('tabindex'), {optional: true});\n\n    const accordionHideToggleChange = panel.accordion\n      ? panel.accordion._stateChanges.pipe(\n          filter(changes => !!(changes['hideToggle'] || changes['togglePosition'])),\n        )\n      : EMPTY;\n    this.tabIndex = parseInt(tabIndex || '') || 0;\n\n    // Since the toggle state depends on an @Input on the panel, we\n    // need to subscribe and trigger change detection manually.\n    this._parentChangeSubscription = merge(\n      panel.opened,\n      panel.closed,\n      accordionHideToggleChange,\n      panel._inputChanges.pipe(\n        filter(changes => {\n          return !!(changes['hideToggle'] || changes['disabled'] || changes['togglePosition']);\n        }),\n      ),\n    ).subscribe(() => this._changeDetectorRef.markForCheck());\n\n    // Avoids focus being lost if the panel contained the focused element and was closed.\n    panel.closed\n      .pipe(filter(() => panel._containsFocus()))\n      .subscribe(() => this._focusMonitor.focusVia(this._element, 'program'));\n\n    if (defaultOptions) {\n      this.expandedHeight = defaultOptions.expandedHeight;\n      this.collapsedHeight = defaultOptions.collapsedHeight;\n    }\n  }\n\n  /** Height of the header while the panel is expanded. */\n  @Input() expandedHeight: string;\n\n  /** Height of the header while the panel is collapsed. */\n  @Input() collapsedHeight: string;\n\n  /** Tab index of the header. */\n  @Input({\n    transform: (value: unknown) => (value == null ? 0 : numberAttribute(value)),\n  })\n  tabIndex: number = 0;\n\n  /**\n   * Whether the associated panel is disabled. Implemented as a part of `FocusableOption`.\n   * @docs-private\n   */\n  get disabled(): boolean {\n    return this.panel.disabled;\n  }\n\n  /** Toggles the expanded state of the panel. */\n  _toggle(): void {\n    if (!this.disabled) {\n      this.panel.toggle();\n    }\n  }\n\n  /** Gets whether the panel is expanded. */\n  _isExpanded(): boolean {\n    return this.panel.expanded;\n  }\n\n  /** Gets the expanded state string of the panel. */\n  _getExpandedState(): string {\n    return this.panel._getExpandedState();\n  }\n\n  /** Gets the panel id. */\n  _getPanelId(): string {\n    return this.panel.id;\n  }\n\n  /** Gets the toggle position for the header. */\n  _getTogglePosition(): MatAccordionTogglePosition {\n    return this.panel.togglePosition;\n  }\n\n  /** Gets whether the expand indicator should be shown. */\n  _showToggle(): boolean {\n    return !this.panel.hideToggle && !this.panel.disabled;\n  }\n\n  /**\n   * Gets the current height of the header. Null if no custom height has been\n   * specified, and if the default height from the stylesheet should be used.\n   */\n  _getHeaderHeight(): string | null {\n    const isExpanded = this._isExpanded();\n    if (isExpanded && this.expandedHeight) {\n      return this.expandedHeight;\n    } else if (!isExpanded && this.collapsedHeight) {\n      return this.collapsedHeight;\n    }\n    return null;\n  }\n\n  /** Handle keydown event calling to toggle() if appropriate. */\n  _keydown(event: KeyboardEvent) {\n    switch (event.keyCode) {\n      // Toggle for space and enter keys.\n      case SPACE:\n      case ENTER:\n        if (!hasModifierKey(event)) {\n          event.preventDefault();\n          this._toggle();\n        }\n\n        break;\n      default:\n        if (this.panel.accordion) {\n          this.panel.accordion._handleHeaderKeydown(event);\n        }\n\n        return;\n    }\n  }\n\n  /**\n   * Focuses the panel header. Implemented as a part of `FocusableOption`.\n   * @param origin Origin of the action that triggered the focus.\n   * @docs-private\n   */\n  focus(origin?: FocusOrigin, options?: FocusOptions) {\n    if (origin) {\n      this._focusMonitor.focusVia(this._element, origin, options);\n    } else {\n      this._element.nativeElement.focus(options);\n    }\n  }\n\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this._element).subscribe(origin => {\n      if (origin && this.panel.accordion) {\n        this.panel.accordion._handleHeaderFocus(this);\n      }\n    });\n  }\n\n  ngOnDestroy() {\n    this._parentChangeSubscription.unsubscribe();\n    this._focusMonitor.stopMonitoring(this._element);\n  }\n}\n\n/**\n * Description element of a `<mat-expansion-panel-header>`.\n */\n@Directive({\n  selector: 'mat-panel-description',\n  host: {\n    class: 'mat-expansion-panel-header-description',\n  },\n})\nexport class MatExpansionPanelDescription {}\n\n/**\n * Title element of a `<mat-expansion-panel-header>`.\n */\n@Directive({\n  selector: 'mat-panel-title',\n  host: {\n    class: 'mat-expansion-panel-header-title',\n  },\n})\nexport class MatExpansionPanelTitle {}\n", "<span class=\"mat-content\" [class.mat-content-hide-toggle]=\"!_showToggle()\">\n  <ng-content select=\"mat-panel-title\"></ng-content>\n  <ng-content select=\"mat-panel-description\"></ng-content>\n  <ng-content></ng-content>\n</span>\n\n@if (_showToggle()) {\n  <span class=\"mat-expansion-indicator\">\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      viewBox=\"0 -960 960 960\"\n      aria-hidden=\"true\"\n      focusable=\"false\">\n      <path d=\"M480-345 240-585l56-56 184 184 184-184 56 56-240 240Z\"/>\n    </svg>\n  </span>\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Directive,\n  Input,\n  ContentChildren,\n  QueryList,\n  AfterContentInit,\n  OnDestroy,\n  booleanAttribute,\n} from '@angular/core';\nimport {CdkAccordion} from '@angular/cdk/accordion';\nimport {FocusKeyManager} from '@angular/cdk/a11y';\nimport {startWith} from 'rxjs/operators';\nimport {\n  MAT_ACCORDION,\n  MatAccordionBase,\n  MatAccordionDisplayMode,\n  MatAccordionTogglePosition,\n} from './accordion-base';\nimport {MatExpansionPanelHeader} from './expansion-panel-header';\n\n/**\n * Directive for a Material Design Accordion.\n */\n@Directive({\n  selector: 'mat-accordion',\n  exportAs: 'matAccordion',\n  providers: [\n    {\n      provide: MAT_ACCORDION,\n      useExisting: MatAccordion,\n    },\n  ],\n  host: {\n    class: 'mat-accordion',\n    // Class binding which is only used by the test harness as there is no other\n    // way for the harness to detect if multiple panel support is enabled.\n    '[class.mat-accordion-multi]': 'this.multi',\n  },\n})\nexport class MatAccordion\n  extends CdkAccordion\n  implements MatAccordionBase, AfterContentInit, OnDestroy\n{\n  private _keyManager: FocusKeyManager<MatExpansionPanelHeader>;\n\n  /** Headers belonging to this accordion. */\n  private _ownHeaders = new QueryList<MatExpansionPanelHeader>();\n\n  /** All headers inside the accordion. Includes headers inside nested accordions. */\n  @ContentChildren(MatExpansionPanelHeader, {descendants: true})\n  _headers: QueryList<MatExpansionPanelHeader>;\n\n  /** Whether the expansion indicator should be hidden. */\n  @Input({transform: booleanAttribute})\n  hideToggle: boolean = false;\n\n  /**\n   * Display mode used for all expansion panels in the accordion. Currently two display\n   * modes exist:\n   *  default - a gutter-like spacing is placed around any expanded panel, placing the expanded\n   *     panel at a different elevation from the rest of the accordion.\n   *  flat - no spacing is placed around expanded panels, showing all panels at the same\n   *     elevation.\n   */\n  @Input() displayMode: MatAccordionDisplayMode = 'default';\n\n  /** The position of the expansion indicator. */\n  @Input() togglePosition: MatAccordionTogglePosition = 'after';\n\n  ngAfterContentInit() {\n    this._headers.changes\n      .pipe(startWith(this._headers))\n      .subscribe((headers: QueryList<MatExpansionPanelHeader>) => {\n        this._ownHeaders.reset(headers.filter(header => header.panel.accordion === this));\n        this._ownHeaders.notifyOnChanges();\n      });\n\n    this._keyManager = new FocusKeyManager(this._ownHeaders).withWrap().withHomeAndEnd();\n  }\n\n  /** Handles keyboard events coming in from the panel headers. */\n  _handleHeaderKeydown(event: KeyboardEvent) {\n    this._keyManager.onKeydown(event);\n  }\n\n  _handleHeaderFocus(header: MatExpansionPanelHeader) {\n    this._keyManager.updateActiveItem(header);\n  }\n\n  override ngOnDestroy() {\n    super.ngOnDestroy();\n    this._keyManager?.destroy();\n    this._ownHeaders.destroy();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {CdkAccordionModule} from '@angular/cdk/accordion';\nimport {PortalModule} from '@angular/cdk/portal';\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '../core';\nimport {MatAccordion} from './accordion';\nimport {MatExpansionPanel, MatExpansionPanelActionRow} from './expansion-panel';\nimport {MatExpansionPanelContent} from './expansion-panel-content';\nimport {\n  MatExpansionPanelDescription,\n  MatExpansionPanelHeader,\n  MatExpansionPanelTitle,\n} from './expansion-panel-header';\n\n@NgModule({\n  imports: [\n    MatCommonModule,\n    CdkAccordionModule,\n    PortalModule,\n    MatAccordion,\n    MatExpansionPanel,\n    MatExpansionPanelActionRow,\n    MatExpansionPanelHeader,\n    MatExpansionPanelTitle,\n    MatExpansionPanelDescription,\n    MatExpansionPanelContent,\n  ],\n  exports: [\n    MatAccordion,\n    MatExpansionPanel,\n    MatExpansionPanelActionRow,\n    MatExpansionPanelHeader,\n    MatExpansionPanelTitle,\n    MatExpansionPanelDescription,\n    MatExpansionPanelContent,\n  ],\n})\nexport class MatExpansionModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Time and timing curve for expansion panel animations.\n * @deprecated No longer used. Will be removed.\n * @breaking-change 21.0.0\n */\nexport const EXPANSION_PANEL_ANIMATION_TIMING = '225ms cubic-bezier(0.4,0.0,0.2,1)';\n\n/**\n * Animations used by the Material expansion panel.\n *\n * A bug in angular animation's `state` when ViewContainers are moved using ViewContainerRef.move()\n * causes the animation state of moved components to become `void` upon exit, and not update again\n * upon reentry into the DOM. This can lead a to situation for the expansion panel where the state\n * of the panel is `expanded` or `collapsed` but the animation state is `void`.\n *\n * To correctly handle animating to the next state, we animate between `void` and `collapsed` which\n * are defined to have the same styles. Since angular animates from the current styles to the\n * destination state's style definition, in situations where we are moving from `void`'s styles to\n * `collapsed` this acts a noop since no style values change.\n *\n * In the case where angular's animation state is out of sync with the expansion panel's state, the\n * expansion panel being `expanded` and angular animations being `void`, the animation from the\n * `expanded`'s effective styles (though in a `void` animation state) to the collapsed state will\n * occur as expected.\n *\n * Angular Bug: https://github.com/angular/angular/issues/18847\n *\n * @docs-private\n * @deprecated No longer being used, to be removed.\n * @breaking-change 21.0.0\n */\nexport const matExpansionAnimations: {\n  readonly indicatorRotate: any;\n  readonly bodyExpansion: any;\n} = {\n  // Represents:\n  // trigger('indicatorRotate', [\n  //   state('collapsed, void', style({transform: 'rotate(0deg)'})),\n  //   state('expanded', style({transform: 'rotate(180deg)'})),\n  //   transition(\n  //     'expanded <=> collapsed, void => collapsed',\n  //     animate(EXPANSION_PANEL_ANIMATION_TIMING),\n  //   ),\n  // ])\n\n  /** Animation that rotates the indicator arrow. */\n  indicatorRotate: {\n    type: 7,\n    name: 'indicatorRotate',\n    definitions: [\n      {\n        type: 0,\n        name: 'collapsed, void',\n        styles: {type: 6, styles: {transform: 'rotate(0deg)'}, offset: null},\n      },\n      {\n        type: 0,\n        name: 'expanded',\n        styles: {type: 6, styles: {transform: 'rotate(180deg)'}, offset: null},\n      },\n      {\n        type: 1,\n        expr: 'expanded <=> collapsed, void => collapsed',\n        animation: {type: 4, styles: null, timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'},\n        options: null,\n      },\n    ],\n    options: {},\n  },\n\n  // Represents:\n  // trigger('bodyExpansion', [\n  //   state('collapsed, void', style({height: '0px', visibility: 'hidden'})),\n  //   // Clear the `visibility` while open, otherwise the content will be visible when placed in\n  //   // a parent that's `visibility: hidden`, because `visibility` doesn't apply to descendants\n  //   // that have a `visibility` of their own (see #27436).\n  //   state('expanded', style({height: '*', visibility: ''})),\n  //   transition(\n  //     'expanded <=> collapsed, void => collapsed',\n  //     animate(EXPANSION_PANEL_ANIMATION_TIMING),\n  //   ),\n  // ])\n\n  /** Animation that expands and collapses the panel content. */\n  bodyExpansion: {\n    type: 7,\n    name: 'bodyExpansion',\n    definitions: [\n      {\n        type: 0,\n        name: 'collapsed, void',\n        styles: {type: 6, styles: {'height': '0px', 'visibility': 'hidden'}, offset: null},\n      },\n      {\n        type: 0,\n        name: 'expanded',\n        styles: {type: 6, styles: {'height': '*', 'visibility': ''}, offset: null},\n      },\n      {\n        type: 1,\n        expr: 'expanded <=> collapsed, void => collapsed',\n        animation: {type: 4, styles: null, timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'},\n        options: null,\n      },\n    ],\n    options: {},\n  },\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAsCA;;;AAGG;MACU,aAAa,GAAG,IAAI,cAAc,CAAmB,eAAe;;ACtBjF;;;AAGG;MACU,mBAAmB,GAAG,IAAI,cAAc,CAAwB,qBAAqB;;ACblG;;;AAGG;MAIU,wBAAwB,CAAA;AACnC,IAAA,SAAS,GAAG,MAAM,CAAmB,WAAW,CAAC;IACjD,eAAe,GAAG,MAAM,CAAwB,mBAAmB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAGtF,IAAA,WAAA,GAAA;uGALW,wBAAwB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAxB,wBAAwB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,uCAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAxB,wBAAwB,EAAA,UAAA,EAAA,CAAA;kBAHpC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,uCAAuC;AAClD,iBAAA;;;AC2CD;;;AAGG;MACU,mCAAmC,GAC9C,IAAI,cAAc,CAAkC,qCAAqC;AAE3F;;;AAGG;AAqBG,MAAO,iBACX,SAAQ,gBAAgB,CAAA;AAGhB,IAAA,iBAAiB,GAAG,MAAM,CAAC,gBAAgB,CAAC;AACnC,IAAA,mBAAmB,GAClC,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,KAAK,gBAAgB;AAC9D,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AACzD,IAAA,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;AAC7B,IAAA,qBAAqB;;AAG7B,IAAA,IACI,UAAU,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;;IAE1E,IAAI,UAAU,CAAC,KAAc,EAAA;AAC3B,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK;;IAElB,WAAW,GAAG,KAAK;;AAG3B,IAAA,IACI,cAAc,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,eAAe,KAAK,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;;IAElF,IAAI,cAAc,CAAC,KAAiC,EAAA;AAClD,QAAA,IAAI,CAAC,eAAe,GAAG,KAAK;;AAEtB,IAAA,eAAe;;AAGJ,IAAA,WAAW,GAAG,IAAI,YAAY,EAAQ;;AAGtC,IAAA,aAAa,GAAG,IAAI,YAAY,EAAQ;;AAGlD,IAAA,aAAa,GAAG,IAAI,OAAO,EAAiB;;AAG5C,IAAA,SAAS,GAAG,MAAM,CAAmB,aAAa,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAE;;AAGvD,IAAA,YAAY;;AAGjC,IAAA,KAAK;;AAId,IAAA,YAAY;;AAGtB,IAAA,OAAO;;IAGP,SAAS,GAAW,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,6BAA6B,CAAC;AAI7E,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;AAEP,QAAA,MAAM,cAAc,GAAG,MAAM,CAC3B,mCAAmC,EACnC,EAAC,QAAQ,EAAE,IAAI,EAAC,CACjB;AAED,QAAA,IAAI,CAAC,oBAAoB,GAAG,MAAM,CAAC,yBAAyB,CAAC;QAE7D,IAAI,cAAc,EAAE;AAClB,YAAA,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU;;;;IAK/C,WAAW,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,KAAK,SAAS;;AAElE,QAAA,OAAO,KAAK;;;IAId,iBAAiB,GAAA;QACf,OAAO,IAAI,CAAC,QAAQ,GAAG,UAAU,GAAG,WAAW;;;IAIxC,MAAM,GAAA;AACb,QAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ;;;IAIvB,KAAK,GAAA;AACZ,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;;;IAId,IAAI,GAAA;AACX,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;;IAGtB,kBAAkB,GAAA;AAChB,QAAA,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,eAAe,KAAK,IAAI,EAAE;;AAEnE,YAAA,IAAI,CAAC;iBACF,IAAI,CACH,SAAS,CAAC,IAAI,CAAC,EACf,MAAM,CAAC,MAAM,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAC5C,IAAI,CAAC,CAAC,CAAC;iBAER,SAAS,CAAC,MAAK;AACd,gBAAA,IAAI,CAAC,OAAO,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC;AACxF,aAAC,CAAC;;QAGN,IAAI,CAAC,qBAAqB,EAAE;;AAG9B,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC;;IAGzB,WAAW,GAAA;QAClB,KAAK,CAAC,WAAW,EAAE;AACnB,QAAA,IAAI,CAAC,qBAAqB,IAAI;AAC9B,QAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;;;IAI/B,cAAc,GAAA;AACZ,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa;AACnD,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa;YAC5C,OAAO,cAAc,KAAK,WAAW,IAAI,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC;;AAG/E,QAAA,OAAO,KAAK;;IAGN,sBAAsB,GAAG,CAAC,EAAC,MAAM,EAAE,YAAY,EAAkB,KAAI;AAC3E,QAAA,IAAI,MAAM,KAAK,IAAI,CAAC,YAAY,EAAE,aAAa,IAAI,YAAY,KAAK,oBAAoB,EAAE;AACxF,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAK;AACpB,gBAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,oBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;;qBAClB;AACL,oBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE;;AAE7B,aAAC,CAAC;;AAEN,KAAC;IAES,qBAAqB,GAAA;;;AAG7B,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC5B,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;gBAC5E,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;;iBACzE;gBACL,UAAU,CAAC,MAAK;AACd,oBAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;AAC9C,oBAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAChD,OAAO,EACP,eAAe,EACf,IAAI,CAAC,sBAAsB,CAC5B;AACD,oBAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,wCAAwC,CAAC;iBAChE,EAAE,GAAG,CAAC;;AAEX,SAAC,CAAC;;uGA9KO,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAjB,iBAAiB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAcT,gBAAgB,CA3BxB,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,aAAA,EAAA,eAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,oBAAA,EAAA,UAAA,EAAA,mCAAA,EAAA,eAAA,EAAA,EAAA,cAAA,EAAA,qBAAA,EAAA,EAAA,SAAA,EAAA;;;AAGT,YAAA,EAAC,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAC;AAC7C,YAAA,EAAC,OAAO,EAAE,mBAAmB,EAAE,WAAW,EAAE,iBAAiB,EAAC;AAC/D,SAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAsDa,wBAAwB,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,OAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,MAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,aAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECzIxC,wiBAcA,EAAA,MAAA,EAAA,CAAA,6xGAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,ED2EY,eAAe,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,UAAA,CAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEd,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBApB7B,SAAS;+BAEE,qBAAqB,EAAA,QAAA,EACrB,mBAAmB,EAAA,aAAA,EAEd,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EACpC,SAAA,EAAA;;;AAGT,wBAAA,EAAC,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAC;AAC7C,wBAAA,EAAC,OAAO,EAAE,mBAAmB,EAAE,WAAW,mBAAmB,EAAC;qBAC/D,EACK,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,qBAAqB;AAC9B,wBAAA,sBAAsB,EAAE,UAAU;AAClC,wBAAA,qCAAqC,EAAE,eAAe;qBACvD,EACQ,OAAA,EAAA,CAAC,eAAe,CAAC,EAAA,QAAA,EAAA,wiBAAA,EAAA,MAAA,EAAA,CAAA,6xGAAA,CAAA,EAAA;wDAiBtB,UAAU,EAAA,CAAA;sBADb,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAWhC,cAAc,EAAA,CAAA;sBADjB;gBAUkB,WAAW,EAAA,CAAA;sBAA7B;gBAGkB,aAAa,EAAA,CAAA;sBAA/B;gBASuC,YAAY,EAAA,CAAA;sBAAnD,YAAY;uBAAC,wBAAwB;gBAGnB,KAAK,EAAA,CAAA;sBAAvB,SAAS;uBAAC,MAAM;gBAIP,YAAY,EAAA,CAAA;sBADrB,SAAS;uBAAC,aAAa;;AA8H1B;;AAEG;MAOU,0BAA0B,CAAA;uGAA1B,0BAA0B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAA1B,0BAA0B,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAA1B,0BAA0B,EAAA,UAAA,EAAA,CAAA;kBANtC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,gBAAgB;AACxB,qBAAA;AACF,iBAAA;;;AElPD;;AAEG;MAuBU,uBAAuB,CAAA;IAClC,KAAK,GAAG,MAAM,CAAC,iBAAiB,EAAE,EAAC,IAAI,EAAE,IAAI,EAAC,CAAC;AACvC,IAAA,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC;AAC7B,IAAA,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC;AACpC,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAE9C,IAAA,yBAAyB,GAAG,YAAY,CAAC,KAAK;AAItD,IAAA,WAAA,GAAA;QACE,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC;AAC5D,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK;AACxB,QAAA,MAAM,cAAc,GAAG,MAAM,CAC3B,mCAAmC,EACnC,EAAC,QAAQ,EAAE,IAAI,EAAC,CACjB;AACD,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,kBAAkB,CAAC,UAAU,CAAC,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAE7E,QAAA,MAAM,yBAAyB,GAAG,KAAK,CAAC;AACtC,cAAE,KAAK,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAChC,MAAM,CAAC,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC,YAAY,CAAC,IAAI,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC;cAE3E,KAAK;QACT,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,IAAI,EAAE,CAAC,IAAI,CAAC;;;QAI7C,IAAI,CAAC,yBAAyB,GAAG,KAAK,CACpC,KAAK,CAAC,MAAM,EACZ,KAAK,CAAC,MAAM,EACZ,yBAAyB,EACzB,KAAK,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,OAAO,IAAG;AACf,YAAA,OAAO,CAAC,EAAE,OAAO,CAAC,YAAY,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,gBAAgB,CAAC,CAAC;AACtF,SAAC,CAAC,CACH,CACF,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;;AAGzD,QAAA,KAAK,CAAC;aACH,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,cAAc,EAAE,CAAC;AACzC,aAAA,SAAS,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QAEzE,IAAI,cAAc,EAAE;AAClB,YAAA,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC,cAAc;AACnD,YAAA,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC,eAAe;;;;AAKhD,IAAA,cAAc;;AAGd,IAAA,eAAe;;IAMxB,QAAQ,GAAW,CAAC;AAEpB;;;AAGG;AACH,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;;;IAI5B,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;;;;IAKvB,WAAW,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;;;IAI5B,iBAAiB,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE;;;IAIvC,WAAW,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE;;;IAItB,kBAAkB,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc;;;IAIlC,WAAW,GAAA;AACT,QAAA,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ;;AAGvD;;;AAGG;IACH,gBAAgB,GAAA;AACd,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,EAAE;AACrC,QAAA,IAAI,UAAU,IAAI,IAAI,CAAC,cAAc,EAAE;YACrC,OAAO,IAAI,CAAC,cAAc;;AACrB,aAAA,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,eAAe,EAAE;YAC9C,OAAO,IAAI,CAAC,eAAe;;AAE7B,QAAA,OAAO,IAAI;;;AAIb,IAAA,QAAQ,CAAC,KAAoB,EAAA;AAC3B,QAAA,QAAQ,KAAK,CAAC,OAAO;;AAEnB,YAAA,KAAK,KAAK;AACV,YAAA,KAAK,KAAK;AACR,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;oBAC1B,KAAK,CAAC,cAAc,EAAE;oBACtB,IAAI,CAAC,OAAO,EAAE;;gBAGhB;AACF,YAAA;AACE,gBAAA,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;oBACxB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,oBAAoB,CAAC,KAAK,CAAC;;gBAGlD;;;AAIN;;;;AAIG;IACH,KAAK,CAAC,MAAoB,EAAE,OAAsB,EAAA;QAChD,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC;;aACtD;YACL,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC;;;IAI9C,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,MAAM,IAAG;YAC3D,IAAI,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;gBAClC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC;;AAEjD,SAAC,CAAC;;IAGJ,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,yBAAyB,CAAC,WAAW,EAAE;QAC5C,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC;;uGAhKvC,uBAAuB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAvB,uBAAuB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,4BAAA,EAAA,MAAA,EAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EA0DrB,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,WAAA,EAAA,SAAA,EAAA,kBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,SAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,0BAAA,EAAA,oBAAA,EAAA,eAAA,EAAA,oBAAA,EAAA,eAAA,EAAA,oBAAA,EAAA,gBAAA,EAAA,oBAAA,EAAA,eAAA,EAAA,4CAAA,EAAA,kCAAA,EAAA,6CAAA,EAAA,mCAAA,EAAA,cAAA,EAAA,oBAAA,EAAA,EAAA,cAAA,EAAA,gDAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECtH/E,yiBAiBA,EAAA,MAAA,EAAA,CAAA,6gJAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FD2Ca,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBAtBnC,SAAS;+BACE,4BAA4B,EAAA,aAAA,EAGvB,iBAAiB,CAAC,IAAI,mBACpB,uBAAuB,CAAC,MAAM,EACzC,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,gDAAgD;AACzD,wBAAA,MAAM,EAAE,QAAQ;AAChB,wBAAA,WAAW,EAAE,iBAAiB;AAC9B,wBAAA,iBAAiB,EAAE,0BAA0B;AAC7C,wBAAA,sBAAsB,EAAE,eAAe;AACvC,wBAAA,sBAAsB,EAAE,eAAe;AACvC,wBAAA,sBAAsB,EAAE,gBAAgB;AACxC,wBAAA,sBAAsB,EAAE,eAAe;AACvC,wBAAA,8CAA8C,EAAE,CAAkC,gCAAA,CAAA;AAClF,wBAAA,+CAA+C,EAAE,CAAmC,iCAAA,CAAA;AACpF,wBAAA,gBAAgB,EAAE,oBAAoB;AACtC,wBAAA,SAAS,EAAE,WAAW;AACtB,wBAAA,WAAW,EAAE,kBAAkB;AAChC,qBAAA,EAAA,QAAA,EAAA,yiBAAA,EAAA,MAAA,EAAA,CAAA,6gJAAA,CAAA,EAAA;wDAqDQ,cAAc,EAAA,CAAA;sBAAtB;gBAGQ,eAAe,EAAA,CAAA;sBAAvB;gBAMD,QAAQ,EAAA,CAAA;sBAHP,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA;wBACL,SAAS,EAAE,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;AAC5E,qBAAA;;AAyGH;;AAEG;MAOU,4BAA4B,CAAA;uGAA5B,4BAA4B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAA5B,4BAA4B,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,uBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,wCAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAA5B,4BAA4B,EAAA,UAAA,EAAA,CAAA;kBANxC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,uBAAuB;AACjC,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,wCAAwC;AAChD,qBAAA;AACF,iBAAA;;AAGD;;AAEG;MAOU,sBAAsB,CAAA;uGAAtB,sBAAsB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAtB,sBAAsB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,kCAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAtB,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBANlC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iBAAiB;AAC3B,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,kCAAkC;AAC1C,qBAAA;AACF,iBAAA;;;AEvND;;AAEG;AAiBG,MAAO,YACX,SAAQ,YAAY,CAAA;AAGZ,IAAA,WAAW;;AAGX,IAAA,WAAW,GAAG,IAAI,SAAS,EAA2B;;AAI9D,IAAA,QAAQ;;IAIR,UAAU,GAAY,KAAK;AAE3B;;;;;;;AAOG;IACM,WAAW,GAA4B,SAAS;;IAGhD,cAAc,GAA+B,OAAO;IAE7D,kBAAkB,GAAA;QAChB,IAAI,CAAC,QAAQ,CAAC;AACX,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC7B,aAAA,SAAS,CAAC,CAAC,OAA2C,KAAI;YACzD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,SAAS,KAAK,IAAI,CAAC,CAAC;AACjF,YAAA,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE;AACpC,SAAC,CAAC;AAEJ,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,CAAC,cAAc,EAAE;;;AAItF,IAAA,oBAAoB,CAAC,KAAoB,EAAA;AACvC,QAAA,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC;;AAGnC,IAAA,kBAAkB,CAAC,MAA+B,EAAA;AAChD,QAAA,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,MAAM,CAAC;;IAGlC,WAAW,GAAA;QAClB,KAAK,CAAC,WAAW,EAAE;AACnB,QAAA,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE;AAC3B,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;;uGArDjB,YAAY,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAZ,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAcJ,gBAAgB,CA3BxB,EAAA,WAAA,EAAA,aAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,2BAAA,EAAA,YAAA,EAAA,EAAA,cAAA,EAAA,eAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,aAAa;AACtB,gBAAA,WAAW,EAAE,YAAY;AAC1B,aAAA;AACF,SAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,UAAA,EAAA,SAAA,EAkBgB,uBAAuB,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAV7B,YAAY,EAAA,UAAA,EAAA,CAAA;kBAhBxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,eAAe;AACzB,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,SAAS,EAAE;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,aAAa;AACtB,4BAAA,WAAW,EAAc,YAAA;AAC1B,yBAAA;AACF,qBAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,eAAe;;;AAGtB,wBAAA,6BAA6B,EAAE,YAAY;AAC5C,qBAAA;AACF,iBAAA;8BAYC,QAAQ,EAAA,CAAA;sBADP,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,uBAAuB,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;gBAK7D,UAAU,EAAA,CAAA;sBADT,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAW3B,WAAW,EAAA,CAAA;sBAAnB;gBAGQ,cAAc,EAAA,CAAA;sBAAtB;;;MC/BU,kBAAkB,CAAA;uGAAlB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAlB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,YArB3B,eAAe;YACf,kBAAkB;YAClB,YAAY;YACZ,YAAY;YACZ,iBAAiB;YACjB,0BAA0B;YAC1B,uBAAuB;YACvB,sBAAsB;YACtB,4BAA4B;AAC5B,YAAA,wBAAwB,aAGxB,YAAY;YACZ,iBAAiB;YACjB,0BAA0B;YAC1B,uBAAuB;YACvB,sBAAsB;YACtB,4BAA4B;YAC5B,wBAAwB,CAAA,EAAA,CAAA;AAGf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,YArB3B,eAAe;YACf,kBAAkB;YAClB,YAAY,CAAA,EAAA,CAAA;;2FAmBH,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAvB9B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE;wBACP,eAAe;wBACf,kBAAkB;wBAClB,YAAY;wBACZ,YAAY;wBACZ,iBAAiB;wBACjB,0BAA0B;wBAC1B,uBAAuB;wBACvB,sBAAsB;wBACtB,4BAA4B;wBAC5B,wBAAwB;AACzB,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,YAAY;wBACZ,iBAAiB;wBACjB,0BAA0B;wBAC1B,uBAAuB;wBACvB,sBAAsB;wBACtB,4BAA4B;wBAC5B,wBAAwB;AACzB,qBAAA;AACF,iBAAA;;;ACnCD;;;;AAIG;AACI,MAAM,gCAAgC,GAAG;AAEhD;;;;;;;;;;;;;;;;;;;;;;;AAuBG;AACU,MAAA,sBAAsB,GAG/B;;;;;;;;;;;AAYF,IAAA,eAAe,EAAE;AACf,QAAA,IAAI,EAAE,CAAC;AACP,QAAA,IAAI,EAAE,iBAAiB;AACvB,QAAA,WAAW,EAAE;AACX,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,iBAAiB;AACvB,gBAAA,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,SAAS,EAAE,cAAc,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACrE,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,UAAU;AAChB,gBAAA,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,SAAS,EAAE,gBAAgB,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACvE,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,2CAA2C;AACjD,gBAAA,SAAS,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,mCAAmC,EAAC;AAChF,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACF,SAAA;AACD,QAAA,OAAO,EAAE,EAAE;AACZ,KAAA;;;;;;;;;;;;;;AAgBD,IAAA,aAAa,EAAE;AACb,QAAA,IAAI,EAAE,CAAC;AACP,QAAA,IAAI,EAAE,eAAe;AACrB,QAAA,WAAW,EAAE;AACX,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,iBAAiB;gBACvB,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,QAAQ,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACnF,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,UAAU;gBAChB,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,QAAQ,EAAE,GAAG,EAAE,YAAY,EAAE,EAAE,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AAC3E,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,2CAA2C;AACjD,gBAAA,SAAS,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,mCAAmC,EAAC;AAChF,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACF,SAAA;AACD,QAAA,OAAO,EAAE,EAAE;AACZ,KAAA;;;;;"}