{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tooltip/testing/tooltip-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {TooltipHarnessFilters} from './tooltip-harness-filters';\n\n/** <PERSON><PERSON>ss for interacting with a mat-tooltip in tests. */\nexport class MatTooltipHarness extends ComponentHarness {\n  static hostSelector = '.mat-mdc-tooltip-trigger';\n\n  private _optionalPanel = this.documentRootLocatorFactory().locatorForOptional('.mat-mdc-tooltip');\n  private _hiddenClass = 'mat-mdc-tooltip-hide';\n  private _disabledClass = 'mat-mdc-tooltip-disabled';\n  private _showAnimationName = 'mat-mdc-tooltip-show';\n  private _hideAnimationName = 'mat-mdc-tooltip-hide';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a tooltip trigger with specific\n   * attributes.\n   * @param options Options for narrowing the search.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatTooltipHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: TooltipHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options);\n  }\n\n  /** Shows the tooltip. */\n  async show(): Promise<void> {\n    const host = await this.host();\n\n    // We need to dispatch both `touchstart` and a hover event, because the tooltip binds\n    // different events depending on the device. The `changedTouches` is there in case the\n    // element has ripples.\n    await host.dispatchEvent('touchstart', {changedTouches: []});\n    await host.hover();\n    const panel = await this._optionalPanel();\n    await panel?.dispatchEvent('animationend', {animationName: this._showAnimationName});\n  }\n\n  /** Hides the tooltip. */\n  async hide(): Promise<void> {\n    const host = await this.host();\n\n    // We need to dispatch both `touchstart` and a hover event, because\n    // the tooltip binds different events depending on the device.\n    await host.dispatchEvent('touchend');\n    await host.mouseAway();\n    const panel = await this._optionalPanel();\n    await panel?.dispatchEvent('animationend', {animationName: this._hideAnimationName});\n  }\n\n  /** Gets whether the tooltip is open. */\n  async isOpen(): Promise<boolean> {\n    const panel = await this._optionalPanel();\n    return !!panel && !(await panel.hasClass(this._hiddenClass));\n  }\n\n  /** Gets whether the tooltip is disabled */\n  async isDisabled(): Promise<boolean> {\n    const host = await this.host();\n    return host.hasClass(this._disabledClass);\n  }\n\n  /** Gets a promise for the tooltip panel's text. */\n  async getTooltipText(): Promise<string> {\n    const panel = await this._optionalPanel();\n    return panel ? panel.text() : '';\n  }\n}\n"], "names": [], "mappings": ";;AAeA;AACM,MAAO,iBAAkB,SAAQ,gBAAgB,CAAA;AACrD,IAAA,OAAO,YAAY,GAAG,0BAA0B;IAExC,cAAc,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC,kBAAkB,CAAC,kBAAkB,CAAC;IACzF,YAAY,GAAG,sBAAsB;IACrC,cAAc,GAAG,0BAA0B;IAC3C,kBAAkB,GAAG,sBAAsB;IAC3C,kBAAkB,GAAG,sBAAsB;AAEnD;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAiC,EAAE,EAAA;AAEnC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC;;;AAI5C,IAAA,MAAM,IAAI,GAAA;AACR,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;;;;AAK9B,QAAA,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,EAAC,cAAc,EAAE,EAAE,EAAC,CAAC;AAC5D,QAAA,MAAM,IAAI,CAAC,KAAK,EAAE;AAClB,QAAA,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE;AACzC,QAAA,MAAM,KAAK,EAAE,aAAa,CAAC,cAAc,EAAE,EAAC,aAAa,EAAE,IAAI,CAAC,kBAAkB,EAAC,CAAC;;;AAItF,IAAA,MAAM,IAAI,GAAA;AACR,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;;;AAI9B,QAAA,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;AACpC,QAAA,MAAM,IAAI,CAAC,SAAS,EAAE;AACtB,QAAA,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE;AACzC,QAAA,MAAM,KAAK,EAAE,aAAa,CAAC,cAAc,EAAE,EAAC,aAAa,EAAE,IAAI,CAAC,kBAAkB,EAAC,CAAC;;;AAItF,IAAA,MAAM,MAAM,GAAA;AACV,QAAA,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE;AACzC,QAAA,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;;;AAI9D,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;QAC9B,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC;;;AAI3C,IAAA,MAAM,cAAc,GAAA;AAClB,QAAA,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE;AACzC,QAAA,OAAO,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE;;;;;;"}