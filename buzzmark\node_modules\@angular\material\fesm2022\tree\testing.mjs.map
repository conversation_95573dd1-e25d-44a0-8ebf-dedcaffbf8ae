{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tree/testing/node-harness.ts", "../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tree/testing/tree-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ComponentHarnessConstructor,\n  ContentContainerComponentHarness,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {TreeNodeHarnessFilters} from './tree-harness-filters';\nimport {coerceBooleanProperty, coerceNumberProperty} from '@angular/cdk/coercion';\n\n/** Harness for interacting with a standard Angular Material tree node. */\nexport class MatTreeNodeHarness extends ContentContainerComponentHarness<string> {\n  /** The selector of the host element of a `MatTreeNode` instance. */\n  static hostSelector = '.mat-tree-node, .mat-nested-tree-node';\n\n  _toggle = this.locatorForOptional('[matTreeNodeToggle]');\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a tree node with specific attributes.\n   * @param options Options for narrowing the search\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: TreeNodeHarnessFilters = {}): HarnessPredicate<MatTreeNodeHarness> {\n    return getNodePredicate(MatTreeNodeHarness, options);\n  }\n\n  /** Whether the tree node is expanded. */\n  async isExpanded(): Promise<boolean> {\n    return coerceBooleanProperty(await (await this.host()).getAttribute('aria-expanded'));\n  }\n\n  /** Whether the tree node is expandable. */\n  async isExpandable(): Promise<boolean> {\n    return (await (await this.host()).getAttribute('aria-expanded')) !== null;\n  }\n\n  /** Whether the tree node is disabled. */\n  async isDisabled(): Promise<boolean> {\n    return coerceBooleanProperty(await (await this.host()).getProperty('aria-disabled'));\n  }\n\n  /** Gets the level of the tree node. Note that this gets the aria-level and is 1 indexed. */\n  async getLevel(): Promise<number> {\n    return coerceNumberProperty(await (await this.host()).getAttribute('aria-level'));\n  }\n\n  /** Gets the tree node's text. */\n  async getText(): Promise<string> {\n    return (await this.host()).text({exclude: '.mat-tree-node, .mat-nested-tree-node, button'});\n  }\n\n  /** Toggles node between expanded/collapsed. Only works when node is not disabled. */\n  async toggle(): Promise<void> {\n    const toggle = await this._toggle();\n    if (toggle) {\n      return toggle.click();\n    }\n  }\n\n  /** Expands the node if it is collapsed. Only works when node is not disabled. */\n  async expand(): Promise<void> {\n    if (!(await this.isExpanded())) {\n      await this.toggle();\n    }\n  }\n\n  /** Collapses the node if it is expanded. Only works when node is not disabled. */\n  async collapse(): Promise<void> {\n    if (await this.isExpanded()) {\n      await this.toggle();\n    }\n  }\n}\n\nfunction getNodePredicate<T extends MatTreeNodeHarness>(\n  type: ComponentHarnessConstructor<T>,\n  options: TreeNodeHarnessFilters,\n): HarnessPredicate<T> {\n  return new HarnessPredicate(type, options)\n    .addOption('text', options.text, (harness, text) =>\n      HarnessPredicate.stringMatches(harness.getText(), text),\n    )\n    .addOption(\n      'disabled',\n      options.disabled,\n      async (harness, disabled) => (await harness.isDisabled()) === disabled,\n    )\n    .addOption(\n      'expanded',\n      options.expanded,\n      async (harness, expanded) => (await harness.isExpanded()) === expanded,\n    )\n    .addOption(\n      'level',\n      options.level,\n      async (harness, level) => (await harness.getLevel()) === level,\n    );\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ComponentHarness, HarnessPredicate, parallel} from '@angular/cdk/testing';\nimport {MatTreeNodeHarness} from './node-harness';\nimport {TreeHarnessFilters, TreeNodeHarnessFilters} from './tree-harness-filters';\n\nexport type TextTree = {\n  text?: string;\n  children?: TextTree[];\n};\n\n/** Harness for interacting with a standard mat-tree in tests. */\nexport class MatTreeHarness extends ComponentHarness {\n  /** The selector for the host element of a `MatTableHarness` instance. */\n  static hostSelector = '.mat-tree';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a tree with specific attributes.\n   * @param options Options for narrowing the search\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: TreeHarnessFilters = {}): HarnessPredicate<MatTreeHarness> {\n    return new HarnessPredicate(MatTreeHarness, options);\n  }\n\n  /** Gets all of the nodes in the tree. */\n  async getNodes(filter: TreeNodeHarnessFilters = {}): Promise<MatTreeNodeHarness[]> {\n    return this.locatorForAll(MatTreeNodeHarness.with(filter))();\n  }\n\n  /**\n   * Gets an object representation for the visible tree structure\n   * If a node is under an unexpanded node it will not be included.\n   * Eg.\n   * Tree (all nodes expanded):\n   * `\n   * <mat-tree>\n   *   <mat-tree-node>Node 1<mat-tree-node>\n   *   <mat-nested-tree-node>\n   *     Node 2\n   *     <mat-nested-tree-node>\n   *       Node 2.1\n   *       <mat-tree-node>\n   *         Node 2.1.1\n   *       <mat-tree-node>\n   *     <mat-nested-tree-node>\n   *     <mat-tree-node>\n   *       Node 2.2\n   *     <mat-tree-node>\n   *   <mat-nested-tree-node>\n   * </mat-tree>`\n   *\n   * Tree structure:\n   * {\n   *  children: [\n   *    {\n   *      text: 'Node 1',\n   *      children: [\n   *        {\n   *          text: 'Node 2',\n   *          children: [\n   *            {\n   *              text: 'Node 2.1',\n   *              children: [{text: 'Node 2.1.1'}]\n   *            },\n   *            {text: 'Node 2.2'}\n   *          ]\n   *        }\n   *      ]\n   *    }\n   *  ]\n   * };\n   */\n  async getTreeStructure(): Promise<TextTree> {\n    const nodes = await this.getNodes();\n    const nodeInformation = await parallel(() =>\n      nodes.map(node => {\n        return parallel(() => [node.getLevel(), node.getText(), node.isExpanded()]);\n      }),\n    );\n    return this._getTreeStructure(nodeInformation, 1, true);\n  }\n\n  /**\n   * Recursively collect the structured text of the tree nodes.\n   * @param nodes A list of tree nodes\n   * @param level The level of nodes that are being accounted for during this iteration\n   * @param parentExpanded Whether the parent of the first node in param nodes is expanded\n   */\n  private _getTreeStructure(\n    nodes: [number, string, boolean][],\n    level: number,\n    parentExpanded: boolean,\n  ): TextTree {\n    const result: TextTree = {};\n    for (let i = 0; i < nodes.length; i++) {\n      const [nodeLevel, text, expanded] = nodes[i];\n      const nextNodeLevel = nodes[i + 1]?.[0] ?? -1;\n\n      // Return the accumulated value for the current level once we reach a shallower level node\n      if (nodeLevel < level) {\n        return result;\n      }\n      // Skip deeper level nodes during this iteration, they will be picked up in a later iteration\n      if (nodeLevel > level) {\n        continue;\n      }\n      // Only add to representation if it is visible (parent is expanded)\n      if (parentExpanded) {\n        // Collect the data under this node according to the following rules:\n        // 1. If the next node in the list is a sibling of the current node add it to the child list\n        // 2. If the next node is a child of the current node, get the sub-tree structure for the\n        //    child and add it under this node\n        // 3. If the next node has a shallower level, we've reached the end of the child nodes for\n        //    the current parent.\n        if (nextNodeLevel === level) {\n          this._addChildToNode(result, {text});\n        } else if (nextNodeLevel > level) {\n          let children = this._getTreeStructure(\n            nodes.slice(i + 1),\n            nextNodeLevel,\n            expanded,\n          )?.children;\n          let child = children ? {text, children} : {text};\n          this._addChildToNode(result, child);\n        } else {\n          this._addChildToNode(result, {text});\n          return result;\n        }\n      }\n    }\n    return result;\n  }\n\n  private _addChildToNode(result: TextTree, child: TextTree) {\n    result.children ? result.children.push(child) : (result.children = [child]);\n  }\n}\n"], "names": [], "mappings": ";;;AAgBA;AACM,MAAO,kBAAmB,SAAQ,gCAAwC,CAAA;;AAE9E,IAAA,OAAO,YAAY,GAAG,uCAAuC;AAE7D,IAAA,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CAAC;AAExD;;;;AAIG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAAkC,EAAE,EAAA;AAC9C,QAAA,OAAO,gBAAgB,CAAC,kBAAkB,EAAE,OAAO,CAAC;;;AAItD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,qBAAqB,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,eAAe,CAAC,CAAC;;;AAIvF,IAAA,MAAM,YAAY,GAAA;AAChB,QAAA,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,eAAe,CAAC,MAAM,IAAI;;;AAI3E,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,qBAAqB,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAC,eAAe,CAAC,CAAC;;;AAItF,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,OAAO,oBAAoB,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,YAAY,CAAC,CAAC;;;AAInF,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,EAAC,OAAO,EAAE,+CAA+C,EAAC,CAAC;;;AAI7F,IAAA,MAAM,MAAM,GAAA;AACV,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE;QACnC,IAAI,MAAM,EAAE;AACV,YAAA,OAAO,MAAM,CAAC,KAAK,EAAE;;;;AAKzB,IAAA,MAAM,MAAM,GAAA;QACV,IAAI,EAAE,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE;AAC9B,YAAA,MAAM,IAAI,CAAC,MAAM,EAAE;;;;AAKvB,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,IAAI,MAAM,IAAI,CAAC,UAAU,EAAE,EAAE;AAC3B,YAAA,MAAM,IAAI,CAAC,MAAM,EAAE;;;;AAKzB,SAAS,gBAAgB,CACvB,IAAoC,EACpC,OAA+B,EAAA;AAE/B,IAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO;SACtC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,IAAI,KAC7C,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC;SAExD,SAAS,CACR,UAAU,EACV,OAAO,CAAC,QAAQ,EAChB,OAAO,OAAO,EAAE,QAAQ,KAAK,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,QAAQ;SAEvE,SAAS,CACR,UAAU,EACV,OAAO,CAAC,QAAQ,EAChB,OAAO,OAAO,EAAE,QAAQ,KAAK,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,QAAQ;SAEvE,SAAS,CACR,OAAO,EACP,OAAO,CAAC,KAAK,EACb,OAAO,OAAO,EAAE,KAAK,KAAK,CAAC,MAAM,OAAO,CAAC,QAAQ,EAAE,MAAM,KAAK,CAC/D;AACL;;ACtFA;AACM,MAAO,cAAe,SAAQ,gBAAgB,CAAA;;AAElD,IAAA,OAAO,YAAY,GAAG,WAAW;AAEjC;;;;AAIG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAA8B,EAAE,EAAA;AAC1C,QAAA,OAAO,IAAI,gBAAgB,CAAC,cAAc,EAAE,OAAO,CAAC;;;AAItD,IAAA,MAAM,QAAQ,CAAC,MAAA,GAAiC,EAAE,EAAA;AAChD,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE;;AAG9D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CG;AACH,IAAA,MAAM,gBAAgB,GAAA;AACpB,QAAA,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE;AACnC,QAAA,MAAM,eAAe,GAAG,MAAM,QAAQ,CAAC,MACrC,KAAK,CAAC,GAAG,CAAC,IAAI,IAAG;YACf,OAAO,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;SAC5E,CAAC,CACH;QACD,OAAO,IAAI,CAAC,iBAAiB,CAAC,eAAe,EAAE,CAAC,EAAE,IAAI,CAAC;;AAGzD;;;;;AAKG;AACK,IAAA,iBAAiB,CACvB,KAAkC,EAClC,KAAa,EACb,cAAuB,EAAA;QAEvB,MAAM,MAAM,GAAa,EAAE;AAC3B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACrC,YAAA,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,QAAQ,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAC5C,YAAA,MAAM,aAAa,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;;AAG7C,YAAA,IAAI,SAAS,GAAG,KAAK,EAAE;AACrB,gBAAA,OAAO,MAAM;;;AAGf,YAAA,IAAI,SAAS,GAAG,KAAK,EAAE;gBACrB;;;YAGF,IAAI,cAAc,EAAE;;;;;;;AAOlB,gBAAA,IAAI,aAAa,KAAK,KAAK,EAAE;oBAC3B,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,EAAC,IAAI,EAAC,CAAC;;AAC/B,qBAAA,IAAI,aAAa,GAAG,KAAK,EAAE;oBAChC,IAAI,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CACnC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAClB,aAAa,EACb,QAAQ,CACT,EAAE,QAAQ;AACX,oBAAA,IAAI,KAAK,GAAG,QAAQ,GAAG,EAAC,IAAI,EAAE,QAAQ,EAAC,GAAG,EAAC,IAAI,EAAC;AAChD,oBAAA,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,KAAK,CAAC;;qBAC9B;oBACL,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,EAAC,IAAI,EAAC,CAAC;AACpC,oBAAA,OAAO,MAAM;;;;AAInB,QAAA,OAAO,MAAM;;IAGP,eAAe,CAAC,MAAgB,EAAE,KAAe,EAAA;QACvD,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,QAAQ,GAAG,CAAC,KAAK,CAAC,CAAC;;;;;;"}