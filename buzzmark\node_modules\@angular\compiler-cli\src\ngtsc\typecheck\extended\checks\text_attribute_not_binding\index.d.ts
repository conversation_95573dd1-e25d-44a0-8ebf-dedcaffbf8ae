/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { ErrorCode, ExtendedTemplateDiagnosticName } from '../../../../diagnostics';
import { TemplateCheckFactory } from '../../api';
export declare const factory: TemplateCheckFactory<ErrorCode.TEXT_ATTRIBUTE_NOT_BINDING, ExtendedTemplateDiagnosticName.TEXT_ATTRIBUTE_NOT_BINDING>;
