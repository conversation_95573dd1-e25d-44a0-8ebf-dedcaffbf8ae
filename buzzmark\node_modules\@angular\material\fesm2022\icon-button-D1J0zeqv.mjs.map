{"version": 3, "file": "icon-button-D1J0zeqv.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/button/button-base.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/button/icon-button.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/button/icon-button.html"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {FocusMonitor, FocusOrigin} from '@angular/cdk/a11y';\nimport {\n  AfterViewInit,\n  ANIMATION_MODULE_TYPE,\n  booleanAttribute,\n  Directive,\n  ElementRef,\n  inject,\n  InjectionToken,\n  Input,\n  NgZone,\n  numberAttribute,\n  OnDestroy,\n  OnInit,\n  Renderer2,\n} from '@angular/core';\nimport {_StructuralStylesLoader, MatRippleLoader, ThemePalette} from '../core';\nimport {_CdkPrivateStyleLoader} from '@angular/cdk/private';\n\n/** Object that can be used to configure the default options for the button component. */\nexport interface MatButtonConfig {\n  /** Whether disabled buttons should be interactive. */\n  disabledInteractive?: boolean;\n\n  /** Default palette color to apply to buttons. */\n  color?: ThemePalette;\n}\n\n/** Injection token that can be used to provide the default options the button component. */\nexport const MAT_BUTTON_CONFIG = new InjectionToken<MatButtonConfig>('MAT_BUTTON_CONFIG');\n\n/** Shared host configuration for all buttons */\nexport const MAT_BUTTON_HOST = {\n  '[attr.disabled]': '_getDisabledAttribute()',\n  '[attr.aria-disabled]': '_getAriaDisabled()',\n  '[class.mat-mdc-button-disabled]': 'disabled',\n  '[class.mat-mdc-button-disabled-interactive]': 'disabledInteractive',\n  '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n  // MDC automatically applies the primary theme color to the button, but we want to support\n  // an unthemed version. If color is undefined, apply a CSS class that makes it easy to\n  // select and style this \"theme\".\n  '[class.mat-unthemed]': '!color',\n  // Add a class that applies to all buttons. This makes it easier to target if somebody\n  // wants to target all Material buttons.\n  '[class.mat-mdc-button-base]': 'true',\n  '[class]': 'color ? \"mat-\" + color : \"\"',\n};\n\n/** List of classes to add to buttons instances based on host attribute selector. */\nconst HOST_SELECTOR_MDC_CLASS_PAIR: {attribute: string; mdcClasses: string[]}[] = [\n  {\n    attribute: 'mat-button',\n    mdcClasses: ['mdc-button', 'mat-mdc-button'],\n  },\n  {\n    attribute: 'mat-flat-button',\n    mdcClasses: ['mdc-button', 'mdc-button--unelevated', 'mat-mdc-unelevated-button'],\n  },\n  {\n    attribute: 'mat-raised-button',\n    mdcClasses: ['mdc-button', 'mdc-button--raised', 'mat-mdc-raised-button'],\n  },\n  {\n    attribute: 'mat-stroked-button',\n    mdcClasses: ['mdc-button', 'mdc-button--outlined', 'mat-mdc-outlined-button'],\n  },\n  {\n    attribute: 'mat-fab',\n    mdcClasses: ['mdc-fab', 'mat-mdc-fab-base', 'mat-mdc-fab'],\n  },\n  {\n    attribute: 'mat-mini-fab',\n    mdcClasses: ['mdc-fab', 'mat-mdc-fab-base', 'mdc-fab--mini', 'mat-mdc-mini-fab'],\n  },\n  {\n    attribute: 'mat-icon-button',\n    mdcClasses: ['mdc-icon-button', 'mat-mdc-icon-button'],\n  },\n];\n\n/** Base class for all buttons.  */\n@Directive()\nexport class MatButtonBase implements AfterViewInit, OnDestroy {\n  _elementRef = inject(ElementRef);\n  _ngZone = inject(NgZone);\n  _animationMode = inject(ANIMATION_MODULE_TYPE, {optional: true});\n\n  private readonly _focusMonitor = inject(FocusMonitor);\n\n  /**\n   * Handles the lazy creation of the MatButton ripple.\n   * Used to improve initial load time of large applications.\n   */\n  protected _rippleLoader: MatRippleLoader = inject(MatRippleLoader);\n\n  /** Whether this button is a FAB. Used to apply the correct class on the ripple. */\n  protected _isFab = false;\n\n  /**\n   * Theme color of the button. This API is supported in M2 themes only, it has\n   * no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/button/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  @Input() color?: string | null;\n\n  /** Whether the ripple effect is disabled or not. */\n  @Input({transform: booleanAttribute})\n  get disableRipple(): boolean {\n    return this._disableRipple;\n  }\n  set disableRipple(value: any) {\n    this._disableRipple = value;\n    this._updateRippleDisabled();\n  }\n  private _disableRipple: boolean = false;\n\n  /** Whether the button is disabled. */\n  @Input({transform: booleanAttribute})\n  get disabled(): boolean {\n    return this._disabled;\n  }\n  set disabled(value: any) {\n    this._disabled = value;\n    this._updateRippleDisabled();\n  }\n  private _disabled: boolean = false;\n\n  /** `aria-disabled` value of the button. */\n  @Input({transform: booleanAttribute, alias: 'aria-disabled'})\n  ariaDisabled: boolean | undefined;\n\n  /**\n   * Natively disabled buttons prevent focus and any pointer events from reaching the button.\n   * In some scenarios this might not be desirable, because it can prevent users from finding out\n   * why the button is disabled (e.g. via tooltip). This is also useful for buttons that may\n   * become disabled when activated, which would cause focus to be transferred to the document\n   * body instead of remaining on the button.\n   *\n   * Enabling this input will change the button so that it is styled to be disabled and will be\n   * marked as `aria-disabled`, but it will allow the button to receive events and focus.\n   *\n   * Note that by enabling this, you need to set the `tabindex` yourself if the button isn't\n   * meant to be tabbable and you have to prevent the button action (e.g. form submissions).\n   */\n  @Input({transform: booleanAttribute})\n  disabledInteractive: boolean;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    const config = inject(MAT_BUTTON_CONFIG, {optional: true});\n    const element = this._elementRef.nativeElement;\n    const classList = (element as HTMLElement).classList;\n\n    this.disabledInteractive = config?.disabledInteractive ?? false;\n    this.color = config?.color ?? null;\n    this._rippleLoader?.configureRipple(element, {className: 'mat-mdc-button-ripple'});\n\n    // For each of the variant selectors that is present in the button's host\n    // attributes, add the correct corresponding MDC classes.\n    for (const {attribute, mdcClasses} of HOST_SELECTOR_MDC_CLASS_PAIR) {\n      if (element.hasAttribute(attribute)) {\n        classList.add(...mdcClasses);\n      }\n    }\n  }\n\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this._elementRef, true);\n  }\n\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    this._rippleLoader?.destroyRipple(this._elementRef.nativeElement);\n  }\n\n  /** Focuses the button. */\n  focus(origin: FocusOrigin = 'program', options?: FocusOptions): void {\n    if (origin) {\n      this._focusMonitor.focusVia(this._elementRef.nativeElement, origin, options);\n    } else {\n      this._elementRef.nativeElement.focus(options);\n    }\n  }\n\n  protected _getAriaDisabled() {\n    if (this.ariaDisabled != null) {\n      return this.ariaDisabled;\n    }\n\n    return this.disabled && this.disabledInteractive ? true : null;\n  }\n\n  protected _getDisabledAttribute() {\n    return this.disabledInteractive || !this.disabled ? null : true;\n  }\n\n  private _updateRippleDisabled(): void {\n    this._rippleLoader?.setDisabled(\n      this._elementRef.nativeElement,\n      this.disableRipple || this.disabled,\n    );\n  }\n}\n\n/** Shared host configuration for buttons using the `<a>` tag. */\nexport const MAT_ANCHOR_HOST = {\n  // Note that this is basically a noop on anchors,\n  // but it appears that some internal apps depend on it.\n  '[attr.disabled]': '_getDisabledAttribute()',\n  '[class.mat-mdc-button-disabled]': 'disabled',\n  '[class.mat-mdc-button-disabled-interactive]': 'disabledInteractive',\n  '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n\n  // Note that we ignore the user-specified tabindex when it's disabled for\n  // consistency with the `mat-button` applied on native buttons where even\n  // though they have an index, they're not tabbable.\n  '[attr.tabindex]': 'disabled && !disabledInteractive ? -1 : tabIndex',\n  '[attr.aria-disabled]': '_getAriaDisabled()',\n  // MDC automatically applies the primary theme color to the button, but we want to support\n  // an unthemed version. If color is undefined, apply a CSS class that makes it easy to\n  // select and style this \"theme\".\n  '[class.mat-unthemed]': '!color',\n  // Add a class that applies to all buttons. This makes it easier to target if somebody\n  // wants to target all Material buttons.\n  '[class.mat-mdc-button-base]': 'true',\n  '[class]': 'color ? \"mat-\" + color : \"\"',\n};\n\n/**\n * Anchor button base.\n */\n@Directive()\nexport class MatAnchorBase extends MatButtonBase implements OnInit, OnDestroy {\n  private _renderer = inject(Renderer2);\n  private _cleanupClick: () => void;\n\n  @Input({\n    transform: (value: unknown) => {\n      return value == null ? undefined : numberAttribute(value);\n    },\n  })\n  tabIndex: number;\n\n  ngOnInit(): void {\n    this._ngZone.runOutsideAngular(() => {\n      this._cleanupClick = this._renderer.listen(\n        this._elementRef.nativeElement,\n        'click',\n        this._haltDisabledEvents,\n      );\n    });\n  }\n\n  override ngOnDestroy(): void {\n    super.ngOnDestroy();\n    this._cleanupClick?.();\n  }\n\n  _haltDisabledEvents = (event: Event): void => {\n    // A disabled button shouldn't apply any actions\n    if (this.disabled) {\n      event.preventDefault();\n      event.stopImmediatePropagation();\n    }\n  };\n\n  protected override _getAriaDisabled() {\n    if (this.ariaDisabled != null) {\n      return this.ariaDisabled;\n    }\n    return this.disabled || null;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ChangeDetectionStrategy, Component, ViewEncapsulation} from '@angular/core';\nimport {MAT_ANCHOR_HOST, MAT_BUTTON_HOST, MatAnchorBase, MatButtonBase} from './button-base';\n\n/**\n * Material Design icon button component. This type of button displays a single interactive icon for\n * users to perform an action.\n * See https://material.io/develop/web/components/buttons/icon-buttons/\n */\n@Component({\n  selector: `button[mat-icon-button]`,\n  templateUrl: 'icon-button.html',\n  styleUrls: ['icon-button.css', 'button-high-contrast.css'],\n  host: MAT_BUTTON_HOST,\n  exportAs: 'matButton',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class MatIcon<PERSON>utton extends MatButtonBase {\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n    this._rippleLoader.configureRipple(this._elementRef.nativeElement, {centered: true});\n  }\n}\n\n/**\n * Material Design icon button component for anchor elements. This button displays a single\n * interaction icon that allows users to navigate across different routes or pages.\n * See https://material.io/develop/web/components/buttons/icon-buttons/\n */\n@Component({\n  selector: `a[mat-icon-button]`,\n  templateUrl: 'icon-button.html',\n  styleUrls: ['icon-button.css', 'button-high-contrast.css'],\n  host: MAT_ANCHOR_HOST,\n  exportAs: 'matButton, matAnchor',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class MatIconAnchor extends MatAnchorBase {}\n", "<span class=\"mat-mdc-button-persistent-ripple mdc-icon-button__ripple\"></span>\n\n<ng-content></ng-content>\n\n<!--\n  The indicator can't be directly on the button, because MDC uses ::before for high contrast\n  indication and it can't be on the ripple, because it has a border radius and overflow: hidden.\n-->\n<span class=\"mat-focus-indicator\"></span>\n\n<span class=\"mat-mdc-button-touch-target\"></span>\n"], "names": [], "mappings": ";;;;;;;AAoCA;MACa,iBAAiB,GAAG,IAAI,cAAc,CAAkB,mBAAmB;AAExF;AACa,MAAA,eAAe,GAAG;AAC7B,IAAA,iBAAiB,EAAE,yBAAyB;AAC5C,IAAA,sBAAsB,EAAE,oBAAoB;AAC5C,IAAA,iCAAiC,EAAE,UAAU;AAC7C,IAAA,6CAA6C,EAAE,qBAAqB;AACpE,IAAA,iCAAiC,EAAE,qCAAqC;;;;AAIxE,IAAA,sBAAsB,EAAE,QAAQ;;;AAGhC,IAAA,6BAA6B,EAAE,MAAM;AACrC,IAAA,SAAS,EAAE,6BAA6B;;AAG1C;AACA,MAAM,4BAA4B,GAAgD;AAChF,IAAA;AACE,QAAA,SAAS,EAAE,YAAY;AACvB,QAAA,UAAU,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC;AAC7C,KAAA;AACD,IAAA;AACE,QAAA,SAAS,EAAE,iBAAiB;AAC5B,QAAA,UAAU,EAAE,CAAC,YAAY,EAAE,wBAAwB,EAAE,2BAA2B,CAAC;AAClF,KAAA;AACD,IAAA;AACE,QAAA,SAAS,EAAE,mBAAmB;AAC9B,QAAA,UAAU,EAAE,CAAC,YAAY,EAAE,oBAAoB,EAAE,uBAAuB,CAAC;AAC1E,KAAA;AACD,IAAA;AACE,QAAA,SAAS,EAAE,oBAAoB;AAC/B,QAAA,UAAU,EAAE,CAAC,YAAY,EAAE,sBAAsB,EAAE,yBAAyB,CAAC;AAC9E,KAAA;AACD,IAAA;AACE,QAAA,SAAS,EAAE,SAAS;AACpB,QAAA,UAAU,EAAE,CAAC,SAAS,EAAE,kBAAkB,EAAE,aAAa,CAAC;AAC3D,KAAA;AACD,IAAA;AACE,QAAA,SAAS,EAAE,cAAc;QACzB,UAAU,EAAE,CAAC,SAAS,EAAE,kBAAkB,EAAE,eAAe,EAAE,kBAAkB,CAAC;AACjF,KAAA;AACD,IAAA;AACE,QAAA,SAAS,EAAE,iBAAiB;AAC5B,QAAA,UAAU,EAAE,CAAC,iBAAiB,EAAE,qBAAqB,CAAC;AACvD,KAAA;CACF;AAED;MAEa,aAAa,CAAA;AACxB,IAAA,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC;AAChC,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;IACxB,cAAc,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAE/C,IAAA,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC;AAErD;;;AAGG;AACO,IAAA,aAAa,GAAoB,MAAM,CAAC,eAAe,CAAC;;IAGxD,MAAM,GAAG,KAAK;AAExB;;;;;;AAMG;AACM,IAAA,KAAK;;AAGd,IAAA,IACI,aAAa,GAAA;QACf,OAAO,IAAI,CAAC,cAAc;;IAE5B,IAAI,aAAa,CAAC,KAAU,EAAA;AAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK;QAC3B,IAAI,CAAC,qBAAqB,EAAE;;IAEtB,cAAc,GAAY,KAAK;;AAGvC,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,KAAU,EAAA;AACrB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC,qBAAqB,EAAE;;IAEtB,SAAS,GAAY,KAAK;;AAIlC,IAAA,YAAY;AAEZ;;;;;;;;;;;;AAYG;AAEH,IAAA,mBAAmB;AAInB,IAAA,WAAA,GAAA;QACE,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC;AAC5D,QAAA,MAAM,MAAM,GAAG,MAAM,CAAC,iBAAiB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAC1D,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;AAC9C,QAAA,MAAM,SAAS,GAAI,OAAuB,CAAC,SAAS;QAEpD,IAAI,CAAC,mBAAmB,GAAG,MAAM,EAAE,mBAAmB,IAAI,KAAK;QAC/D,IAAI,CAAC,KAAK,GAAG,MAAM,EAAE,KAAK,IAAI,IAAI;AAClC,QAAA,IAAI,CAAC,aAAa,EAAE,eAAe,CAAC,OAAO,EAAE,EAAC,SAAS,EAAE,uBAAuB,EAAC,CAAC;;;QAIlF,KAAK,MAAM,EAAC,SAAS,EAAE,UAAU,EAAC,IAAI,4BAA4B,EAAE;AAClE,YAAA,IAAI,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE;AACnC,gBAAA,SAAS,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC;;;;IAKlC,eAAe,GAAA;QACb,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC;;IAGpD,WAAW,GAAA;QACT,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC;QACnD,IAAI,CAAC,aAAa,EAAE,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;;;AAInE,IAAA,KAAK,CAAC,MAAA,GAAsB,SAAS,EAAE,OAAsB,EAAA;QAC3D,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,MAAM,EAAE,OAAO,CAAC;;aACvE;YACL,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC;;;IAIvC,gBAAgB,GAAA;AACxB,QAAA,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,EAAE;YAC7B,OAAO,IAAI,CAAC,YAAY;;AAG1B,QAAA,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,mBAAmB,GAAG,IAAI,GAAG,IAAI;;IAGtD,qBAAqB,GAAA;AAC7B,QAAA,OAAO,IAAI,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI;;IAGzD,qBAAqB,GAAA;AAC3B,QAAA,IAAI,CAAC,aAAa,EAAE,WAAW,CAC7B,IAAI,CAAC,WAAW,CAAC,aAAa,EAC9B,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,CACpC;;uGA1HQ,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,kGA0BL,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAWhB,gBAAgB,CAWhB,EAAA,YAAA,EAAA,CAAA,eAAA,EAAA,cAAA,EAAA,gBAAgB,uEAgBhB,gBAAgB,CAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAhExB,aAAa,EAAA,UAAA,EAAA,CAAA;kBADzB;wDAwBU,KAAK,EAAA,CAAA;sBAAb;gBAIG,aAAa,EAAA,CAAA;sBADhB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAYhC,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAYpC,YAAY,EAAA,CAAA;sBADX,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,SAAS,EAAE,gBAAgB,EAAE,KAAK,EAAE,eAAe,EAAC;gBAiB5D,mBAAmB,EAAA,CAAA;sBADlB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;;AA8DtC;AACa,MAAA,eAAe,GAAG;;;AAG7B,IAAA,iBAAiB,EAAE,yBAAyB;AAC5C,IAAA,iCAAiC,EAAE,UAAU;AAC7C,IAAA,6CAA6C,EAAE,qBAAqB;AACpE,IAAA,iCAAiC,EAAE,qCAAqC;;;;AAKxE,IAAA,iBAAiB,EAAE,kDAAkD;AACrE,IAAA,sBAAsB,EAAE,oBAAoB;;;;AAI5C,IAAA,sBAAsB,EAAE,QAAQ;;;AAGhC,IAAA,6BAA6B,EAAE,MAAM;AACrC,IAAA,SAAS,EAAE,6BAA6B;;AAG1C;;AAEG;AAEG,MAAO,aAAc,SAAQ,aAAa,CAAA;AACtC,IAAA,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;AAC7B,IAAA,aAAa;AAOrB,IAAA,QAAQ;IAER,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;YAClC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CACxC,IAAI,CAAC,WAAW,CAAC,aAAa,EAC9B,OAAO,EACP,IAAI,CAAC,mBAAmB,CACzB;AACH,SAAC,CAAC;;IAGK,WAAW,GAAA;QAClB,KAAK,CAAC,WAAW,EAAE;AACnB,QAAA,IAAI,CAAC,aAAa,IAAI;;AAGxB,IAAA,mBAAmB,GAAG,CAAC,KAAY,KAAU;;AAE3C,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,KAAK,CAAC,cAAc,EAAE;YACtB,KAAK,CAAC,wBAAwB,EAAE;;AAEpC,KAAC;IAEkB,gBAAgB,GAAA;AACjC,QAAA,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,EAAE;YAC7B,OAAO,IAAI,CAAC,YAAY;;AAE1B,QAAA,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI;;uGAtCnB,aAAa,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,EAKX,YAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,CAAC,KAAc,KAAI;AAC5B,oBAAA,OAAO,KAAK,IAAI,IAAI,GAAG,SAAS,GAAG,eAAe,CAAC,KAAK,CAAC;iBAC1D,CAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAPQ,aAAa,EAAA,UAAA,EAAA,CAAA;kBADzB;8BAUC,QAAQ,EAAA,CAAA;sBALP,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA;AACL,wBAAA,SAAS,EAAE,CAAC,KAAc,KAAI;AAC5B,4BAAA,OAAO,KAAK,IAAI,IAAI,GAAG,SAAS,GAAG,eAAe,CAAC,KAAK,CAAC;yBAC1D;AACF,qBAAA;;;ACjPH;;;;AAIG;AAUG,MAAO,aAAc,SAAQ,aAAa,CAAA;AAG9C,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;AACP,QAAA,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;;uGAL3E,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,iiBCzB1B,kaAWA,EAAA,MAAA,EAAA,CAAA,gzHAAA,EAAA,kVAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FDca,aAAa,EAAA,UAAA,EAAA,CAAA;kBATzB,SAAS;+BACE,CAAyB,uBAAA,CAAA,EAAA,IAAA,EAG7B,eAAe,EAAA,QAAA,EACX,WAAW,EAAA,aAAA,EACN,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,kaAAA,EAAA,MAAA,EAAA,CAAA,gzHAAA,EAAA,kVAAA,CAAA,EAAA;;AAWjD;;;;AAIG;AAUG,MAAO,aAAc,SAAQ,aAAa,CAAA;uGAAnC,aAAa,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,8mBChD1B,kaAWA,EAAA,MAAA,EAAA,CAAA,gzHAAA,EAAA,kVAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FDqCa,aAAa,EAAA,UAAA,EAAA,CAAA;kBATzB,SAAS;+BACE,CAAoB,kBAAA,CAAA,EAAA,IAAA,EAGxB,eAAe,EAAA,QAAA,EACX,sBAAsB,EAAA,aAAA,EACjB,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,kaAAA,EAAA,MAAA,EAAA,CAAA,gzHAAA,EAAA,kVAAA,CAAA,EAAA;;;;;"}