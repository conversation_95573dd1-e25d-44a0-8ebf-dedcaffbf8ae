{"version": 3, "file": "input.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/input/input-errors.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/input/input.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/input/module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/** @docs-private */\nexport function getMatInputUnsupportedTypeError(type: string): Error {\n  return Error(`Input type \"${type}\" isn't supported by matInput.`);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {BooleanInput, coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {getSupportedInputTypes, Platform} from '@angular/cdk/platform';\nimport {AutofillMonitor} from '@angular/cdk/text-field';\nimport {\n  AfterViewInit,\n  booleanAttribute,\n  Directive,\n  DoCheck,\n  effect,\n  ElementRef,\n  inject,\n  InjectionToken,\n  Input,\n  isSignal,\n  NgZone,\n  OnChanges,\n  OnDestroy,\n  Renderer2,\n  WritableSignal,\n} from '@angular/core';\nimport {_IdGenerator} from '@angular/cdk/a11y';\nimport {FormGroupDirective, NgControl, NgForm, Validators} from '@angular/forms';\nimport {ErrorStateMatcher, _ErrorStateTracker} from '../core';\nimport {<PERSON>FormFieldControl, MatFormField, MAT_FORM_FIELD} from '../form-field';\nimport {Subject} from 'rxjs';\nimport {getMatInputUnsupportedTypeError} from './input-errors';\nimport {MAT_INPUT_VALUE_ACCESSOR} from './input-value-accessor';\n\n// Invalid input type. Using one of these will throw an MatInputUnsupportedTypeError.\nconst MAT_INPUT_INVALID_TYPES = [\n  'button',\n  'checkbox',\n  'file',\n  'hidden',\n  'image',\n  'radio',\n  'range',\n  'reset',\n  'submit',\n];\n\n/** Object that can be used to configure the default options for the input. */\nexport interface MatInputConfig {\n  /** Whether disabled inputs should be interactive. */\n  disabledInteractive?: boolean;\n}\n\n/** Injection token that can be used to provide the default options for the input. */\nexport const MAT_INPUT_CONFIG = new InjectionToken<MatInputConfig>('MAT_INPUT_CONFIG');\n\n@Directive({\n  selector: `input[matInput], textarea[matInput], select[matNativeControl],\n      input[matNativeControl], textarea[matNativeControl]`,\n  exportAs: 'matInput',\n  host: {\n    'class': 'mat-mdc-input-element',\n    // The BaseMatInput parent class adds `mat-input-element`, `mat-form-field-control` and\n    // `mat-form-field-autofill-control` to the CSS class list, but this should not be added for\n    // this MDC equivalent input.\n    '[class.mat-input-server]': '_isServer',\n    '[class.mat-mdc-form-field-textarea-control]': '_isInFormField && _isTextarea',\n    '[class.mat-mdc-form-field-input-control]': '_isInFormField',\n    '[class.mat-mdc-input-disabled-interactive]': 'disabledInteractive',\n    '[class.mdc-text-field__input]': '_isInFormField',\n    '[class.mat-mdc-native-select-inline]': '_isInlineSelect()',\n    // Native input properties that are overwritten by Angular inputs need to be synced with\n    // the native input element. Otherwise property bindings for those don't work.\n    '[id]': 'id',\n    '[disabled]': 'disabled && !disabledInteractive',\n    '[required]': 'required',\n    '[attr.name]': 'name || null',\n    '[attr.readonly]': '_getReadonlyAttribute()',\n    '[attr.aria-disabled]': 'disabled && disabledInteractive ? \"true\" : null',\n    // Only mark the input as invalid for assistive technology if it has a value since the\n    // state usually overlaps with `aria-required` when the input is empty and can be redundant.\n    '[attr.aria-invalid]': '(empty && required) ? null : errorState',\n    '[attr.aria-required]': 'required',\n    // Native input properties that are overwritten by Angular inputs need to be synced with\n    // the native input element. Otherwise property bindings for those don't work.\n    '[attr.id]': 'id',\n    '(focus)': '_focusChanged(true)',\n    '(blur)': '_focusChanged(false)',\n    '(input)': '_onInput()',\n  },\n  providers: [{provide: MatFormFieldControl, useExisting: MatInput}],\n})\nexport class MatInput\n  implements MatFormFieldControl<any>, OnChanges, OnDestroy, AfterViewInit, DoCheck\n{\n  protected _elementRef =\n    inject<ElementRef<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>>(ElementRef);\n  protected _platform = inject(Platform);\n  ngControl = inject(NgControl, {optional: true, self: true})!;\n  private _autofillMonitor = inject(AutofillMonitor);\n  private _ngZone = inject(NgZone);\n  protected _formField? = inject<MatFormField>(MAT_FORM_FIELD, {optional: true});\n  private _renderer = inject(Renderer2);\n\n  protected _uid = inject(_IdGenerator).getId('mat-input-');\n  protected _previousNativeValue: any;\n  private _inputValueAccessor: {value: any};\n  private _signalBasedValueAccessor?: {value: WritableSignal<any>};\n  private _previousPlaceholder: string | null;\n  private _errorStateTracker: _ErrorStateTracker;\n  private _config = inject(MAT_INPUT_CONFIG, {optional: true});\n  private _cleanupIosKeyup: (() => void) | undefined;\n  private _cleanupWebkitWheel: (() => void) | undefined;\n\n  /** `aria-describedby` IDs assigned by the form field. */\n  private _formFieldDescribedBy: string[] | undefined;\n\n  /** Whether the component is being rendered on the server. */\n  readonly _isServer: boolean;\n\n  /** Whether the component is a native html select. */\n  readonly _isNativeSelect: boolean;\n\n  /** Whether the component is a textarea. */\n  readonly _isTextarea: boolean;\n\n  /** Whether the input is inside of a form field. */\n  readonly _isInFormField: boolean;\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  focused: boolean = false;\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  readonly stateChanges: Subject<void> = new Subject<void>();\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  controlType: string = 'mat-input';\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  autofilled = false;\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  @Input()\n  get disabled(): boolean {\n    return this._disabled;\n  }\n  set disabled(value: BooleanInput) {\n    this._disabled = coerceBooleanProperty(value);\n\n    // Browsers may not fire the blur event if the input is disabled too quickly.\n    // Reset from here to ensure that the element doesn't become stuck.\n    if (this.focused) {\n      this.focused = false;\n      this.stateChanges.next();\n    }\n  }\n  protected _disabled = false;\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  @Input()\n  get id(): string {\n    return this._id;\n  }\n  set id(value: string) {\n    this._id = value || this._uid;\n  }\n  protected _id: string;\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  @Input() placeholder: string;\n\n  /**\n   * Name of the input.\n   * @docs-private\n   */\n  @Input() name: string;\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  @Input()\n  get required(): boolean {\n    return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n  }\n  set required(value: BooleanInput) {\n    this._required = coerceBooleanProperty(value);\n  }\n  protected _required: boolean | undefined;\n\n  /** Input type of the element. */\n  @Input()\n  get type(): string {\n    return this._type;\n  }\n  set type(value: string) {\n    const prevType = this._type;\n    this._type = value || 'text';\n    this._validateType();\n\n    // When using Angular inputs, developers are no longer able to set the properties on the native\n    // input element. To ensure that bindings for `type` work, we need to sync the setter\n    // with the native property. Textarea elements don't support the type property or attribute.\n    if (!this._isTextarea && getSupportedInputTypes().has(this._type)) {\n      (this._elementRef.nativeElement as HTMLInputElement).type = this._type;\n    }\n\n    if (this._type !== prevType) {\n      this._ensureWheelDefaultBehavior();\n    }\n  }\n  protected _type = 'text';\n\n  /** An object used to control when error messages are shown. */\n  @Input()\n  get errorStateMatcher() {\n    return this._errorStateTracker.matcher;\n  }\n  set errorStateMatcher(value: ErrorStateMatcher) {\n    this._errorStateTracker.matcher = value;\n  }\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  @Input('aria-describedby') userAriaDescribedBy: string;\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  @Input()\n  get value(): string {\n    return this._signalBasedValueAccessor\n      ? this._signalBasedValueAccessor.value()\n      : this._inputValueAccessor.value;\n  }\n  set value(value: any) {\n    if (value !== this.value) {\n      if (this._signalBasedValueAccessor) {\n        this._signalBasedValueAccessor.value.set(value);\n      } else {\n        this._inputValueAccessor.value = value;\n      }\n\n      this.stateChanges.next();\n    }\n  }\n\n  /** Whether the element is readonly. */\n  @Input()\n  get readonly(): boolean {\n    return this._readonly;\n  }\n  set readonly(value: BooleanInput) {\n    this._readonly = coerceBooleanProperty(value);\n  }\n  private _readonly = false;\n\n  /** Whether the input should remain interactive when it is disabled. */\n  @Input({transform: booleanAttribute})\n  disabledInteractive: boolean;\n\n  /** Whether the input is in an error state. */\n  get errorState() {\n    return this._errorStateTracker.errorState;\n  }\n  set errorState(value: boolean) {\n    this._errorStateTracker.errorState = value;\n  }\n\n  protected _neverEmptyInputTypes = [\n    'date',\n    'datetime',\n    'datetime-local',\n    'month',\n    'time',\n    'week',\n  ].filter(t => getSupportedInputTypes().has(t));\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const parentForm = inject(NgForm, {optional: true});\n    const parentFormGroup = inject(FormGroupDirective, {optional: true});\n    const defaultErrorStateMatcher = inject(ErrorStateMatcher);\n    const accessor = inject(MAT_INPUT_VALUE_ACCESSOR, {optional: true, self: true});\n\n    const element = this._elementRef.nativeElement;\n    const nodeName = element.nodeName.toLowerCase();\n\n    if (accessor) {\n      if (isSignal(accessor.value)) {\n        this._signalBasedValueAccessor = accessor;\n      } else {\n        this._inputValueAccessor = accessor;\n      }\n    } else {\n      // If no input value accessor was explicitly specified, use the element as the input value\n      // accessor.\n      this._inputValueAccessor = element;\n    }\n\n    this._previousNativeValue = this.value;\n\n    // Force setter to be called in case id was not specified.\n    this.id = this.id;\n\n    // On some versions of iOS the caret gets stuck in the wrong place when holding down the delete\n    // key. In order to get around this we need to \"jiggle\" the caret loose. Since this bug only\n    // exists on iOS, we only bother to install the listener on iOS.\n    if (this._platform.IOS) {\n      this._ngZone.runOutsideAngular(() => {\n        this._cleanupIosKeyup = this._renderer.listen(element, 'keyup', this._iOSKeyupListener);\n      });\n    }\n\n    this._errorStateTracker = new _ErrorStateTracker(\n      defaultErrorStateMatcher,\n      this.ngControl,\n      parentFormGroup,\n      parentForm,\n      this.stateChanges,\n    );\n    this._isServer = !this._platform.isBrowser;\n    this._isNativeSelect = nodeName === 'select';\n    this._isTextarea = nodeName === 'textarea';\n    this._isInFormField = !!this._formField;\n    this.disabledInteractive = this._config?.disabledInteractive || false;\n\n    if (this._isNativeSelect) {\n      this.controlType = (element as HTMLSelectElement).multiple\n        ? 'mat-native-select-multiple'\n        : 'mat-native-select';\n    }\n\n    if (this._signalBasedValueAccessor) {\n      effect(() => {\n        // Read the value so the effect can register the dependency.\n        this._signalBasedValueAccessor!.value();\n        this.stateChanges.next();\n      });\n    }\n  }\n\n  ngAfterViewInit() {\n    if (this._platform.isBrowser) {\n      this._autofillMonitor.monitor(this._elementRef.nativeElement).subscribe(event => {\n        this.autofilled = event.isAutofilled;\n        this.stateChanges.next();\n      });\n    }\n  }\n\n  ngOnChanges() {\n    this.stateChanges.next();\n  }\n\n  ngOnDestroy() {\n    this.stateChanges.complete();\n\n    if (this._platform.isBrowser) {\n      this._autofillMonitor.stopMonitoring(this._elementRef.nativeElement);\n    }\n\n    this._cleanupIosKeyup?.();\n    this._cleanupWebkitWheel?.();\n  }\n\n  ngDoCheck() {\n    if (this.ngControl) {\n      // We need to re-evaluate this on every change detection cycle, because there are some\n      // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n      // that whatever logic is in here has to be super lean or we risk destroying the performance.\n      this.updateErrorState();\n\n      // Since the input isn't a `ControlValueAccessor`, we don't have a good way of knowing when\n      // the disabled state has changed. We can't use the `ngControl.statusChanges`, because it\n      // won't fire if the input is disabled with `emitEvents = false`, despite the input becoming\n      // disabled.\n      if (this.ngControl.disabled !== null && this.ngControl.disabled !== this.disabled) {\n        this.disabled = this.ngControl.disabled;\n        this.stateChanges.next();\n      }\n    }\n\n    // We need to dirty-check the native element's value, because there are some cases where\n    // we won't be notified when it changes (e.g. the consumer isn't using forms or they're\n    // updating the value using `emitEvent: false`).\n    this._dirtyCheckNativeValue();\n\n    // We need to dirty-check and set the placeholder attribute ourselves, because whether it's\n    // present or not depends on a query which is prone to \"changed after checked\" errors.\n    this._dirtyCheckPlaceholder();\n  }\n\n  /** Focuses the input. */\n  focus(options?: FocusOptions): void {\n    this._elementRef.nativeElement.focus(options);\n  }\n\n  /** Refreshes the error state of the input. */\n  updateErrorState() {\n    this._errorStateTracker.updateErrorState();\n  }\n\n  /** Callback for the cases where the focused state of the input changes. */\n  _focusChanged(isFocused: boolean) {\n    if (isFocused === this.focused) {\n      return;\n    }\n\n    if (!this._isNativeSelect && isFocused && this.disabled && this.disabledInteractive) {\n      const element = this._elementRef.nativeElement as HTMLInputElement;\n\n      // Focusing an input that has text will cause all the text to be selected. Clear it since\n      // the user won't be able to change it. This is based on the internal implementation.\n      if (element.type === 'number') {\n        // setSelectionRange doesn't work on number inputs so it needs to be set briefly to text.\n        element.type = 'text';\n        element.setSelectionRange(0, 0);\n        element.type = 'number';\n      } else {\n        element.setSelectionRange(0, 0);\n      }\n    }\n\n    this.focused = isFocused;\n    this.stateChanges.next();\n  }\n\n  _onInput() {\n    // This is a noop function and is used to let Angular know whenever the value changes.\n    // Angular will run a new change detection each time the `input` event has been dispatched.\n    // It's necessary that Angular recognizes the value change, because when floatingLabel\n    // is set to false and Angular forms aren't used, the placeholder won't recognize the\n    // value changes and will not disappear.\n    // Listening to the input event wouldn't be necessary when the input is using the\n    // FormsModule or ReactiveFormsModule, because Angular forms also listens to input events.\n  }\n\n  /** Does some manual dirty checking on the native input `value` property. */\n  protected _dirtyCheckNativeValue() {\n    const newValue = this._elementRef.nativeElement.value;\n\n    if (this._previousNativeValue !== newValue) {\n      this._previousNativeValue = newValue;\n      this.stateChanges.next();\n    }\n  }\n\n  /** Does some manual dirty checking on the native input `placeholder` attribute. */\n  private _dirtyCheckPlaceholder() {\n    const placeholder = this._getPlaceholder();\n    if (placeholder !== this._previousPlaceholder) {\n      const element = this._elementRef.nativeElement;\n      this._previousPlaceholder = placeholder;\n      placeholder\n        ? element.setAttribute('placeholder', placeholder)\n        : element.removeAttribute('placeholder');\n    }\n  }\n\n  /** Gets the current placeholder of the form field. */\n  protected _getPlaceholder(): string | null {\n    return this.placeholder || null;\n  }\n\n  /** Make sure the input is a supported type. */\n  protected _validateType() {\n    if (\n      MAT_INPUT_INVALID_TYPES.indexOf(this._type) > -1 &&\n      (typeof ngDevMode === 'undefined' || ngDevMode)\n    ) {\n      throw getMatInputUnsupportedTypeError(this._type);\n    }\n  }\n\n  /** Checks whether the input type is one of the types that are never empty. */\n  protected _isNeverEmpty() {\n    return this._neverEmptyInputTypes.indexOf(this._type) > -1;\n  }\n\n  /** Checks whether the input is invalid based on the native validation. */\n  protected _isBadInput() {\n    // The `validity` property won't be present on platform-server.\n    let validity = (this._elementRef.nativeElement as HTMLInputElement).validity;\n    return validity && validity.badInput;\n  }\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get empty(): boolean {\n    return (\n      !this._isNeverEmpty() &&\n      !this._elementRef.nativeElement.value &&\n      !this._isBadInput() &&\n      !this.autofilled\n    );\n  }\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get shouldLabelFloat(): boolean {\n    if (this._isNativeSelect) {\n      // For a single-selection `<select>`, the label should float when the selected option has\n      // a non-empty display value. For a `<select multiple>`, the label *always* floats to avoid\n      // overlapping the label with the options.\n      const selectElement = this._elementRef.nativeElement as HTMLSelectElement;\n      const firstOption: HTMLOptionElement | undefined = selectElement.options[0];\n\n      // On most browsers the `selectedIndex` will always be 0, however on IE and Edge it'll be\n      // -1 if the `value` is set to something, that isn't in the list of options, at a later point.\n      return (\n        this.focused ||\n        selectElement.multiple ||\n        !this.empty ||\n        !!(selectElement.selectedIndex > -1 && firstOption && firstOption.label)\n      );\n    } else {\n      return (this.focused && !this.disabled) || !this.empty;\n    }\n  }\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  setDescribedByIds(ids: string[]) {\n    const element = this._elementRef.nativeElement;\n    const existingDescribedBy = element.getAttribute('aria-describedby');\n    let toAssign: string[];\n\n    // In some cases there might be some `aria-describedby` IDs that were assigned directly,\n    // like by the `AriaDescriber` (see #30011). Attempt to preserve them by taking the previous\n    // attribute value and filtering out the IDs that came from the previous `setDescribedByIds`\n    // call. Note the `|| ids` here allows us to avoid duplicating IDs on the first render.\n    if (existingDescribedBy) {\n      const exclude = this._formFieldDescribedBy || ids;\n      toAssign = ids.concat(\n        existingDescribedBy.split(' ').filter(id => id && !exclude.includes(id)),\n      );\n    } else {\n      toAssign = ids;\n    }\n\n    this._formFieldDescribedBy = ids;\n\n    if (toAssign.length) {\n      element.setAttribute('aria-describedby', toAssign.join(' '));\n    } else {\n      element.removeAttribute('aria-describedby');\n    }\n  }\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  onContainerClick() {\n    // Do not re-focus the input element if the element is already focused. Otherwise it can happen\n    // that someone clicks on a time input and the cursor resets to the \"hours\" field while the\n    // \"minutes\" field was actually clicked. See: https://github.com/angular/components/issues/12849\n    if (!this.focused) {\n      this.focus();\n    }\n  }\n\n  /** Whether the form control is a native select that is displayed inline. */\n  _isInlineSelect(): boolean {\n    const element = this._elementRef.nativeElement as HTMLSelectElement;\n    return this._isNativeSelect && (element.multiple || element.size > 1);\n  }\n\n  private _iOSKeyupListener = (event: Event): void => {\n    const el = event.target as HTMLInputElement;\n\n    // Note: We specifically check for 0, rather than `!el.selectionStart`, because the two\n    // indicate different things. If the value is 0, it means that the caret is at the start\n    // of the input, whereas a value of `null` means that the input doesn't support\n    // manipulating the selection range. Inputs that don't support setting the selection range\n    // will throw an error so we want to avoid calling `setSelectionRange` on them. See:\n    // https://html.spec.whatwg.org/multipage/input.html#do-not-apply\n    if (!el.value && el.selectionStart === 0 && el.selectionEnd === 0) {\n      // Note: Just setting `0, 0` doesn't fix the issue. Setting\n      // `1, 1` fixes it for the first time that you type text and\n      // then hold delete. Toggling to `1, 1` and then back to\n      // `0, 0` seems to completely fix it.\n      el.setSelectionRange(1, 1);\n      el.setSelectionRange(0, 0);\n    }\n  };\n\n  private _webkitBlinkWheelListener = (): void => {\n    // This is a noop function and is used to enable mouse wheel input\n    // on number inputs\n    // on blink and webkit browsers.\n  };\n\n  /**\n   * In blink and webkit browsers a focused number input does not increment or decrement its value\n   * on mouse wheel interaction unless a wheel event listener is attached to it or one of its\n   * ancestors or a passive wheel listener is attached somewhere in the DOM. For example: Hitting\n   * a tooltip once enables the mouse wheel input for all number inputs as long as it exists. In\n   * order to get reliable and intuitive behavior we apply a wheel event on our own thus making\n   * sure increment and decrement by mouse wheel works every time.\n   * @docs-private\n   */\n  private _ensureWheelDefaultBehavior(): void {\n    this._cleanupWebkitWheel?.();\n\n    if (this._type === 'number' && (this._platform.BLINK || this._platform.WEBKIT)) {\n      this._cleanupWebkitWheel = this._renderer.listen(\n        this._elementRef.nativeElement,\n        'wheel',\n        this._webkitBlinkWheelListener,\n      );\n    }\n  }\n\n  /** Gets the value to set on the `readonly` attribute. */\n  protected _getReadonlyAttribute(): string | null {\n    if (this._isNativeSelect) {\n      return null;\n    }\n\n    if (this.readonly || (this.disabled && this.disabledInteractive)) {\n      return 'true';\n    }\n\n    return null;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {TextFieldModule} from '@angular/cdk/text-field';\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '../core';\nimport {MatFormFieldModule} from '../form-field';\nimport {MatInput} from './input';\n\n@NgModule({\n  imports: [MatCommonModule, MatFormFieldModule, MatInput],\n  exports: [MatInput, MatFormFieldModule, TextFieldModule, MatCommonModule],\n})\nexport class MatInputModule {}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAQA;AACM,SAAU,+BAA+B,CAAC,IAAY,EAAA;AAC1D,IAAA,OAAO,KAAK,CAAC,CAAA,YAAA,EAAe,IAAI,CAAA,8BAAA,CAAgC,CAAC;AACnE;;ACyBA;AACA,MAAM,uBAAuB,GAAG;IAC9B,QAAQ;IACR,UAAU;IACV,MAAM;IACN,QAAQ;IACR,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;CACT;AAQD;MACa,gBAAgB,GAAG,IAAI,cAAc,CAAiB,kBAAkB;MAsCxE,QAAQ,CAAA;AAGT,IAAA,WAAW,GACnB,MAAM,CAAyE,UAAU,CAAC;AAClF,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AACtC,IAAA,SAAS,GAAG,MAAM,CAAC,SAAS,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAE;AACpD,IAAA,gBAAgB,GAAG,MAAM,CAAC,eAAe,CAAC;AAC1C,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;IACtB,UAAU,GAAI,MAAM,CAAe,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AACtE,IAAA,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;IAE3B,IAAI,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC;AAC/C,IAAA,oBAAoB;AACtB,IAAA,mBAAmB;AACnB,IAAA,yBAAyB;AACzB,IAAA,oBAAoB;AACpB,IAAA,kBAAkB;IAClB,OAAO,GAAG,MAAM,CAAC,gBAAgB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AACpD,IAAA,gBAAgB;AAChB,IAAA,mBAAmB;;AAGnB,IAAA,qBAAqB;;AAGpB,IAAA,SAAS;;AAGT,IAAA,eAAe;;AAGf,IAAA,WAAW;;AAGX,IAAA,cAAc;AAEvB;;;AAGG;IACH,OAAO,GAAY,KAAK;AAExB;;;AAGG;AACM,IAAA,YAAY,GAAkB,IAAI,OAAO,EAAQ;AAE1D;;;AAGG;IACH,WAAW,GAAW,WAAW;AAEjC;;;AAGG;IACH,UAAU,GAAG,KAAK;AAElB;;;AAGG;AACH,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,KAAmB,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,KAAK,CAAC;;;AAI7C,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK;AACpB,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;;;IAGlB,SAAS,GAAG,KAAK;AAE3B;;;AAGG;AACH,IAAA,IACI,EAAE,GAAA;QACJ,OAAO,IAAI,CAAC,GAAG;;IAEjB,IAAI,EAAE,CAAC,KAAa,EAAA;QAClB,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,IAAI,CAAC,IAAI;;AAErB,IAAA,GAAG;AAEb;;;AAGG;AACM,IAAA,WAAW;AAEpB;;;AAGG;AACM,IAAA,IAAI;AAEb;;;AAGG;AACH,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,YAAY,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,KAAK;;IAE9F,IAAI,QAAQ,CAAC,KAAmB,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,KAAK,CAAC;;AAErC,IAAA,SAAS;;AAGnB,IAAA,IACI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,KAAK;;IAEnB,IAAI,IAAI,CAAC,KAAa,EAAA;AACpB,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK;AAC3B,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,MAAM;QAC5B,IAAI,CAAC,aAAa,EAAE;;;;AAKpB,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,sBAAsB,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAChE,IAAI,CAAC,WAAW,CAAC,aAAkC,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK;;AAGxE,QAAA,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;YAC3B,IAAI,CAAC,2BAA2B,EAAE;;;IAG5B,KAAK,GAAG,MAAM;;AAGxB,IAAA,IACI,iBAAiB,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO;;IAExC,IAAI,iBAAiB,CAAC,KAAwB,EAAA;AAC5C,QAAA,IAAI,CAAC,kBAAkB,CAAC,OAAO,GAAG,KAAK;;AAGzC;;;AAGG;AACwB,IAAA,mBAAmB;AAE9C;;;AAGG;AACH,IAAA,IACI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC;AACV,cAAE,IAAI,CAAC,yBAAyB,CAAC,KAAK;AACtC,cAAE,IAAI,CAAC,mBAAmB,CAAC,KAAK;;IAEpC,IAAI,KAAK,CAAC,KAAU,EAAA;AAClB,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE;AACxB,YAAA,IAAI,IAAI,CAAC,yBAAyB,EAAE;gBAClC,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC;;iBAC1C;AACL,gBAAA,IAAI,CAAC,mBAAmB,CAAC,KAAK,GAAG,KAAK;;AAGxC,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;;;;AAK5B,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,KAAmB,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,KAAK,CAAC;;IAEvC,SAAS,GAAG,KAAK;;AAIzB,IAAA,mBAAmB;;AAGnB,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,kBAAkB,CAAC,UAAU;;IAE3C,IAAI,UAAU,CAAC,KAAc,EAAA;AAC3B,QAAA,IAAI,CAAC,kBAAkB,CAAC,UAAU,GAAG,KAAK;;AAGlC,IAAA,qBAAqB,GAAG;QAChC,MAAM;QACN,UAAU;QACV,gBAAgB;QAChB,OAAO;QACP,MAAM;QACN,MAAM;AACP,KAAA,CAAC,MAAM,CAAC,CAAC,IAAI,sBAAsB,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAI9C,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AACnD,QAAA,MAAM,eAAe,GAAG,MAAM,CAAC,kBAAkB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AACpE,QAAA,MAAM,wBAAwB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAC1D,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,wBAAwB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;AAE/E,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;QAC9C,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE;QAE/C,IAAI,QAAQ,EAAE;AACZ,YAAA,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AAC5B,gBAAA,IAAI,CAAC,yBAAyB,GAAG,QAAQ;;iBACpC;AACL,gBAAA,IAAI,CAAC,mBAAmB,GAAG,QAAQ;;;aAEhC;;;AAGL,YAAA,IAAI,CAAC,mBAAmB,GAAG,OAAO;;AAGpC,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,KAAK;;AAGtC,QAAA,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;;;;AAKjB,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE;AACtB,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,gBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC;AACzF,aAAC,CAAC;;QAGJ,IAAI,CAAC,kBAAkB,GAAG,IAAI,kBAAkB,CAC9C,wBAAwB,EACxB,IAAI,CAAC,SAAS,EACd,eAAe,EACf,UAAU,EACV,IAAI,CAAC,YAAY,CAClB;QACD,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS;AAC1C,QAAA,IAAI,CAAC,eAAe,GAAG,QAAQ,KAAK,QAAQ;AAC5C,QAAA,IAAI,CAAC,WAAW,GAAG,QAAQ,KAAK,UAAU;QAC1C,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU;QACvC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,OAAO,EAAE,mBAAmB,IAAI,KAAK;AAErE,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AACxB,YAAA,IAAI,CAAC,WAAW,GAAI,OAA6B,CAAC;AAChD,kBAAE;kBACA,mBAAmB;;AAGzB,QAAA,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAClC,MAAM,CAAC,MAAK;;AAEV,gBAAA,IAAI,CAAC,yBAA0B,CAAC,KAAK,EAAE;AACvC,gBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;AAC1B,aAAC,CAAC;;;IAIN,eAAe,GAAA;AACb,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;AAC5B,YAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,SAAS,CAAC,KAAK,IAAG;AAC9E,gBAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,YAAY;AACpC,gBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;AAC1B,aAAC,CAAC;;;IAIN,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;;IAG1B,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;AAE5B,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC5B,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;;AAGtE,QAAA,IAAI,CAAC,gBAAgB,IAAI;AACzB,QAAA,IAAI,CAAC,mBAAmB,IAAI;;IAG9B,SAAS,GAAA;AACP,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;;;;YAIlB,IAAI,CAAC,gBAAgB,EAAE;;;;;AAMvB,YAAA,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,EAAE;gBACjF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ;AACvC,gBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;;;;;;QAO5B,IAAI,CAAC,sBAAsB,EAAE;;;QAI7B,IAAI,CAAC,sBAAsB,EAAE;;;AAI/B,IAAA,KAAK,CAAC,OAAsB,EAAA;QAC1B,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC;;;IAI/C,gBAAgB,GAAA;AACd,QAAA,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,EAAE;;;AAI5C,IAAA,aAAa,CAAC,SAAkB,EAAA;AAC9B,QAAA,IAAI,SAAS,KAAK,IAAI,CAAC,OAAO,EAAE;YAC9B;;AAGF,QAAA,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,SAAS,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,mBAAmB,EAAE;AACnF,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAiC;;;AAIlE,YAAA,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE;;AAE7B,gBAAA,OAAO,CAAC,IAAI,GAAG,MAAM;AACrB,gBAAA,OAAO,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;AAC/B,gBAAA,OAAO,CAAC,IAAI,GAAG,QAAQ;;iBAClB;AACL,gBAAA,OAAO,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;;;AAInC,QAAA,IAAI,CAAC,OAAO,GAAG,SAAS;AACxB,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;;IAG1B,QAAQ,GAAA;;;;;;;;;;IAWE,sBAAsB,GAAA;QAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK;AAErD,QAAA,IAAI,IAAI,CAAC,oBAAoB,KAAK,QAAQ,EAAE;AAC1C,YAAA,IAAI,CAAC,oBAAoB,GAAG,QAAQ;AACpC,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;;;;IAKpB,sBAAsB,GAAA;AAC5B,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,EAAE;AAC1C,QAAA,IAAI,WAAW,KAAK,IAAI,CAAC,oBAAoB,EAAE;AAC7C,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;AAC9C,YAAA,IAAI,CAAC,oBAAoB,GAAG,WAAW;YACvC;kBACI,OAAO,CAAC,YAAY,CAAC,aAAa,EAAE,WAAW;AACjD,kBAAE,OAAO,CAAC,eAAe,CAAC,aAAa,CAAC;;;;IAKpC,eAAe,GAAA;AACvB,QAAA,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI;;;IAIvB,aAAa,GAAA;QACrB,IACE,uBAAuB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aAC/C,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAC/C;AACA,YAAA,MAAM,+BAA+B,CAAC,IAAI,CAAC,KAAK,CAAC;;;;IAK3C,aAAa,GAAA;AACrB,QAAA,OAAO,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;;;IAIlD,WAAW,GAAA;;QAEnB,IAAI,QAAQ,GAAI,IAAI,CAAC,WAAW,CAAC,aAAkC,CAAC,QAAQ;AAC5E,QAAA,OAAO,QAAQ,IAAI,QAAQ,CAAC,QAAQ;;AAGtC;;;AAGG;AACH,IAAA,IAAI,KAAK,GAAA;AACP,QAAA,QACE,CAAC,IAAI,CAAC,aAAa,EAAE;AACrB,YAAA,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK;YACrC,CAAC,IAAI,CAAC,WAAW,EAAE;AACnB,YAAA,CAAC,IAAI,CAAC,UAAU;;AAIpB;;;AAGG;AACH,IAAA,IAAI,gBAAgB,GAAA;AAClB,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE;;;;AAIxB,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,aAAkC;YACzE,MAAM,WAAW,GAAkC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;;;YAI3E,QACE,IAAI,CAAC,OAAO;AACZ,gBAAA,aAAa,CAAC,QAAQ;gBACtB,CAAC,IAAI,CAAC,KAAK;AACX,gBAAA,CAAC,EAAE,aAAa,CAAC,aAAa,GAAG,CAAC,CAAC,IAAI,WAAW,IAAI,WAAW,CAAC,KAAK,CAAC;;aAErE;AACL,YAAA,OAAO,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAI,CAAC,KAAK;;;AAI1D;;;AAGG;AACH,IAAA,iBAAiB,CAAC,GAAa,EAAA;AAC7B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;QAC9C,MAAM,mBAAmB,GAAG,OAAO,CAAC,YAAY,CAAC,kBAAkB,CAAC;AACpE,QAAA,IAAI,QAAkB;;;;;QAMtB,IAAI,mBAAmB,EAAE;AACvB,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,IAAI,GAAG;AACjD,YAAA,QAAQ,GAAG,GAAG,CAAC,MAAM,CACnB,mBAAmB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CACzE;;aACI;YACL,QAAQ,GAAG,GAAG;;AAGhB,QAAA,IAAI,CAAC,qBAAqB,GAAG,GAAG;AAEhC,QAAA,IAAI,QAAQ,CAAC,MAAM,EAAE;AACnB,YAAA,OAAO,CAAC,YAAY,CAAC,kBAAkB,EAAE,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;aACvD;AACL,YAAA,OAAO,CAAC,eAAe,CAAC,kBAAkB,CAAC;;;AAI/C;;;AAGG;IACH,gBAAgB,GAAA;;;;AAId,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,KAAK,EAAE;;;;IAKhB,eAAe,GAAA;AACb,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAkC;AACnE,QAAA,OAAO,IAAI,CAAC,eAAe,KAAK,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC;;AAG/D,IAAA,iBAAiB,GAAG,CAAC,KAAY,KAAU;AACjD,QAAA,MAAM,EAAE,GAAG,KAAK,CAAC,MAA0B;;;;;;;AAQ3C,QAAA,IAAI,CAAC,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,cAAc,KAAK,CAAC,IAAI,EAAE,CAAC,YAAY,KAAK,CAAC,EAAE;;;;;AAKjE,YAAA,EAAE,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1B,YAAA,EAAE,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;;AAE9B,KAAC;IAEO,yBAAyB,GAAG,MAAW;;;;AAI/C,KAAC;AAED;;;;;;;;AAQG;IACK,2BAA2B,GAAA;AACjC,QAAA,IAAI,CAAC,mBAAmB,IAAI;QAE5B,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;YAC9E,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAC9C,IAAI,CAAC,WAAW,CAAC,aAAa,EAC9B,OAAO,EACP,IAAI,CAAC,yBAAyB,CAC/B;;;;IAKK,qBAAqB,GAAA;AAC7B,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AACxB,YAAA,OAAO,IAAI;;AAGb,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,mBAAmB,CAAC,EAAE;AAChE,YAAA,OAAO,MAAM;;AAGf,QAAA,OAAO,IAAI;;uGApjBF,QAAQ,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAR,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,QAAQ,EA8LA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,2HAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,aAAA,EAAA,IAAA,EAAA,MAAA,EAAA,QAAA,EAAA,UAAA,EAAA,IAAA,EAAA,MAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,mBAAA,EAAA,CAAA,kBAAA,EAAA,qBAAA,CAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,UAAA,EAAA,mBAAA,EAAA,CAAA,qBAAA,EAAA,qBAAA,EAAA,gBAAgB,CAhMxB,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,sBAAA,EAAA,OAAA,EAAA,YAAA,EAAA,EAAA,UAAA,EAAA,EAAA,wBAAA,EAAA,WAAA,EAAA,2CAAA,EAAA,+BAAA,EAAA,wCAAA,EAAA,gBAAA,EAAA,0CAAA,EAAA,qBAAA,EAAA,6BAAA,EAAA,gBAAA,EAAA,oCAAA,EAAA,mBAAA,EAAA,IAAA,EAAA,IAAA,EAAA,UAAA,EAAA,kCAAA,EAAA,UAAA,EAAA,UAAA,EAAA,WAAA,EAAA,cAAA,EAAA,eAAA,EAAA,yBAAA,EAAA,oBAAA,EAAA,mDAAA,EAAA,mBAAA,EAAA,yCAAA,EAAA,oBAAA,EAAA,UAAA,EAAA,SAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,uBAAA,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,mBAAmB,EAAE,WAAW,EAAE,QAAQ,EAAC,CAAC,EAAA,QAAA,EAAA,CAAA,UAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAEvD,QAAQ,EAAA,UAAA,EAAA,CAAA;kBApCpB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,CAAA;AAC8C,yDAAA,CAAA;AACxD,oBAAA,QAAQ,EAAE,UAAU;AACpB,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,uBAAuB;;;;AAIhC,wBAAA,0BAA0B,EAAE,WAAW;AACvC,wBAAA,6CAA6C,EAAE,+BAA+B;AAC9E,wBAAA,0CAA0C,EAAE,gBAAgB;AAC5D,wBAAA,4CAA4C,EAAE,qBAAqB;AACnE,wBAAA,+BAA+B,EAAE,gBAAgB;AACjD,wBAAA,sCAAsC,EAAE,mBAAmB;;;AAG3D,wBAAA,MAAM,EAAE,IAAI;AACZ,wBAAA,YAAY,EAAE,kCAAkC;AAChD,wBAAA,YAAY,EAAE,UAAU;AACxB,wBAAA,aAAa,EAAE,cAAc;AAC7B,wBAAA,iBAAiB,EAAE,yBAAyB;AAC5C,wBAAA,sBAAsB,EAAE,iDAAiD;;;AAGzE,wBAAA,qBAAqB,EAAE,yCAAyC;AAChE,wBAAA,sBAAsB,EAAE,UAAU;;;AAGlC,wBAAA,WAAW,EAAE,IAAI;AACjB,wBAAA,SAAS,EAAE,qBAAqB;AAChC,wBAAA,QAAQ,EAAE,sBAAsB;AAChC,wBAAA,SAAS,EAAE,YAAY;AACxB,qBAAA;oBACD,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,mBAAmB,EAAE,WAAW,EAAU,QAAA,EAAC,CAAC;AACnE,iBAAA;wDAmEK,QAAQ,EAAA,CAAA;sBADX;gBAqBG,EAAE,EAAA,CAAA;sBADL;gBAaQ,WAAW,EAAA,CAAA;sBAAnB;gBAMQ,IAAI,EAAA,CAAA;sBAAZ;gBAOG,QAAQ,EAAA,CAAA;sBADX;gBAWG,IAAI,EAAA,CAAA;sBADP;gBAwBG,iBAAiB,EAAA,CAAA;sBADpB;gBAY0B,mBAAmB,EAAA,CAAA;sBAA7C,KAAK;uBAAC,kBAAkB;gBAOrB,KAAK,EAAA,CAAA;sBADR;gBAoBG,QAAQ,EAAA,CAAA;sBADX;gBAWD,mBAAmB,EAAA,CAAA;sBADlB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;;;MC1QzB,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,EAHf,OAAA,EAAA,CAAA,eAAe,EAAE,kBAAkB,EAAE,QAAQ,CAC7C,EAAA,OAAA,EAAA,CAAA,QAAQ,EAAE,kBAAkB,EAAE,eAAe,EAAE,eAAe,CAAA,EAAA,CAAA;wGAE7D,cAAc,EAAA,OAAA,EAAA,CAHf,eAAe,EAAE,kBAAkB,EACzB,kBAAkB,EAAE,eAAe,EAAE,eAAe,CAAA,EAAA,CAAA;;2FAE7D,cAAc,EAAA,UAAA,EAAA,CAAA;kBAJ1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,kBAAkB,EAAE,QAAQ,CAAC;oBACxD,OAAO,EAAE,CAAC,QAAQ,EAAE,kBAAkB,EAAE,eAAe,EAAE,eAAe,CAAC;AAC1E,iBAAA;;;;;"}