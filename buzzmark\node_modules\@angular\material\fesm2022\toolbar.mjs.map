{"version": 3, "file": "toolbar.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/toolbar/toolbar.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/toolbar/toolbar.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/toolbar/toolbar-module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Platform} from '@angular/cdk/platform';\nimport {DOCUMENT} from '@angular/common';\nimport {\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  Component,\n  ContentChildren,\n  Directive,\n  ElementRef,\n  Input,\n  QueryList,\n  ViewEncapsulation,\n  inject,\n} from '@angular/core';\n\n@Directive({\n  selector: 'mat-toolbar-row',\n  exportAs: 'matToolbarRow',\n  host: {'class': 'mat-toolbar-row'},\n})\nexport class MatToolbarRow {}\n\n@Component({\n  selector: 'mat-toolbar',\n  exportAs: 'matToolbar',\n  templateUrl: 'toolbar.html',\n  styleUrl: 'toolbar.css',\n  host: {\n    'class': 'mat-toolbar',\n    '[class]': 'color ? \"mat-\" + color : \"\"',\n    '[class.mat-toolbar-multiple-rows]': '_toolbarRows.length > 0',\n    '[class.mat-toolbar-single-row]': '_toolbarRows.length === 0',\n  },\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n})\nexport class MatToolbar implements AfterViewInit {\n  protected _elementRef = inject(ElementRef);\n  private _platform = inject(Platform);\n  private _document = inject(DOCUMENT);\n\n  // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n  /**\n   * Theme color of the toolbar. This API is supported in M2 themes only, it has\n   * no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/toolbar/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  @Input() color?: string | null;\n\n  /** Reference to all toolbar row elements that have been projected. */\n  @ContentChildren(MatToolbarRow, {descendants: true}) _toolbarRows: QueryList<MatToolbarRow>;\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  ngAfterViewInit() {\n    if (this._platform.isBrowser) {\n      this._checkToolbarMixedModes();\n      this._toolbarRows.changes.subscribe(() => this._checkToolbarMixedModes());\n    }\n  }\n\n  /**\n   * Throws an exception when developers are attempting to combine the different toolbar row modes.\n   */\n  private _checkToolbarMixedModes() {\n    if (this._toolbarRows.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      // Check if there are any other DOM nodes that can display content but aren't inside of\n      // a <mat-toolbar-row> element.\n      const isCombinedUsage = Array.from<HTMLElement>(this._elementRef.nativeElement.childNodes)\n        .filter(node => !(node.classList && node.classList.contains('mat-toolbar-row')))\n        .filter(node => node.nodeType !== (this._document ? this._document.COMMENT_NODE : 8))\n        .some(node => !!(node.textContent && node.textContent.trim()));\n\n      if (isCombinedUsage) {\n        throwToolbarMixedModesError();\n      }\n    }\n  }\n}\n\n/**\n * Throws an exception when attempting to combine the different toolbar row modes.\n * @docs-private\n */\nexport function throwToolbarMixedModesError() {\n  throw Error(\n    'MatToolbar: Attempting to combine different toolbar modes. ' +\n      'Either specify multiple `<mat-toolbar-row>` elements explicitly or just place content ' +\n      'inside of a `<mat-toolbar>` for a single row.',\n  );\n}\n", "<ng-content></ng-content>\n<ng-content select=\"mat-toolbar-row\"></ng-content>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '../core';\nimport {<PERSON><PERSON>oolbar, MatToolbarRow} from './toolbar';\n\n@NgModule({\n  imports: [MatCommonModule, MatToolbar, MatToolbarRow],\n  exports: [MatToolbar, MatToolbarRow, MatCommonModule],\n})\nexport class MatToolbarModule {}\n"], "names": [], "mappings": ";;;;;;;;MA4Ba,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,iBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,eAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBALzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iBAAiB;AAC3B,oBAAA,QAAQ,EAAE,eAAe;AACzB,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,iBAAiB,EAAC;AACnC,iBAAA;;MAiBY,UAAU,CAAA;AACX,IAAA,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC;AAClC,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;;AAGpC;;;;;;AAMG;AACM,IAAA,KAAK;;AAGuC,IAAA,YAAY;AAGjE,IAAA,WAAA,GAAA;IAEA,eAAe,GAAA;AACb,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC5B,IAAI,CAAC,uBAAuB,EAAE;AAC9B,YAAA,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;;;AAI7E;;AAEG;IACK,uBAAuB,GAAA;AAC7B,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;;;AAG/E,YAAA,MAAM,eAAe,GAAG,KAAK,CAAC,IAAI,CAAc,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,UAAU;iBACtF,MAAM,CAAC,IAAI,IAAI,EAAE,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;iBAC9E,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,MAAM,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG,CAAC,CAAC;iBACnF,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;YAEhE,IAAI,eAAe,EAAE;AACnB,gBAAA,2BAA2B,EAAE;;;;uGAzCxB,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAV,UAAU,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,iCAAA,EAAA,iCAAA,EAAA,yBAAA,EAAA,8BAAA,EAAA,2BAAA,EAAA,EAAA,cAAA,EAAA,aAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,cAAA,EAAA,SAAA,EAgBJ,aAAa,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EC5DhC,mFAEA,EAAA,MAAA,EAAA,CAAA,ogEAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FD0Ca,UAAU,EAAA,UAAA,EAAA,CAAA;kBAdtB,SAAS;+BACE,aAAa,EAAA,QAAA,EACb,YAAY,EAGhB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,aAAa;AACtB,wBAAA,SAAS,EAAE,6BAA6B;AACxC,wBAAA,mCAAmC,EAAE,yBAAyB;AAC9D,wBAAA,gCAAgC,EAAE,2BAA2B;AAC9D,qBAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,QAAA,EAAA,mFAAA,EAAA,MAAA,EAAA,CAAA,ogEAAA,CAAA,EAAA;wDAe5B,KAAK,EAAA,CAAA;sBAAb;gBAGoD,YAAY,EAAA,CAAA;sBAAhE,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,aAAa,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;;AA+BrD;;;AAGG;SACa,2BAA2B,GAAA;IACzC,MAAM,KAAK,CACT,6DAA6D;QAC3D,wFAAwF;AACxF,QAAA,+CAA+C,CAClD;AACH;;MErFa,gBAAgB,CAAA;uGAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAhB,gBAAgB,EAAA,OAAA,EAAA,CAHjB,eAAe,EAAE,UAAU,EAAE,aAAa,CAAA,EAAA,OAAA,EAAA,CAC1C,UAAU,EAAE,aAAa,EAAE,eAAe,CAAA,EAAA,CAAA;wGAEzC,gBAAgB,EAAA,OAAA,EAAA,CAHjB,eAAe,EACY,eAAe,CAAA,EAAA,CAAA;;2FAEzC,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAJ5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,UAAU,EAAE,aAAa,CAAC;AACrD,oBAAA,OAAO,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,eAAe,CAAC;AACtD,iBAAA;;;;;"}