{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/slider/testing/slider-harness-filters.ts", "../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/slider/testing/slider-thumb-harness.ts", "../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/slider/testing/slider-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {BaseHarnessFilters} from '@angular/cdk/testing';\n\n/** Possible positions of a slider thumb. */\nexport enum ThumbPosition {\n  START,\n  END,\n}\n\n/** A set of criteria that can be used to filter a list of `MatSliderHarness` instances. */\nexport interface SliderHarnessFilters extends BaseHarnessFilters {\n  /** Filters out only range/non-range sliders. */\n  isRange?: boolean;\n\n  /** Only find instances which match the given disabled state. */\n  disabled?: boolean;\n}\n\n/** A set of criteria that can be used to filter a list of `MatSliderThumbHarness` instances. */\nexport interface SliderThumbHarnessFilters extends BaseHarnessFilters {\n  /** Filters out slider thumbs with a particular position. */\n  position?: ThumbPosition;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {coerceNumberProperty} from '@angular/cdk/coercion';\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n  parallel,\n} from '@angular/cdk/testing';\nimport {SliderThumbHarnessFilters, ThumbPosition} from './slider-harness-filters';\n\n/** Harness for interacting with a thumb inside of a Material slider in tests. */\nexport class MatSliderThumbHarness extends ComponentHarness {\n  static hostSelector =\n    'input[matSliderThumb], input[matSliderStartThumb], input[matSliderEndThumb]';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a slider thumb with specific attributes.\n   * @param options Options for filtering which thumb instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatSliderThumbHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: SliderThumbHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options).addOption(\n      'position',\n      options.position,\n      async (harness, value) => {\n        return (await harness.getPosition()) === value;\n      },\n    );\n  }\n\n  /** Gets the position of the thumb inside the slider. */\n  async getPosition(): Promise<ThumbPosition> {\n    // Meant to mimic MDC's logic where `matSliderThumb` is treated as END.\n    const isStart = (await (await this.host()).getAttribute('matSliderStartThumb')) != null;\n    return isStart ? ThumbPosition.START : ThumbPosition.END;\n  }\n\n  /** Gets the value of the thumb. */\n  async getValue(): Promise<number> {\n    return await (await this.host()).getProperty<number>('valueAsNumber');\n  }\n\n  /** Sets the value of the thumb. */\n  async setValue(newValue: number): Promise<void> {\n    const input = await this.host();\n\n    // Since this is a range input, we can't simulate the user interacting with it so we set the\n    // value directly and dispatch a couple of fake events to ensure that everything fires.\n    await input.setInputValue(newValue + '');\n    await input.dispatchEvent('input');\n    await input.dispatchEvent('change');\n  }\n\n  /** Gets the current percentage value of the slider. */\n  async getPercentage(): Promise<number> {\n    const [value, min, max] = await parallel(() => [\n      this.getValue(),\n      this.getMinValue(),\n      this.getMaxValue(),\n    ]);\n\n    return (value - min) / (max - min);\n  }\n\n  /** Gets the maximum value of the thumb. */\n  async getMaxValue(): Promise<number> {\n    return coerceNumberProperty(await (await this.host()).getProperty<number>('max'));\n  }\n\n  /** Gets the minimum value of the thumb. */\n  async getMinValue(): Promise<number> {\n    return coerceNumberProperty(await (await this.host()).getProperty<number>('min'));\n  }\n\n  /** Gets the text representation of the slider's value. */\n  async getDisplayValue(): Promise<string> {\n    return (await (await this.host()).getAttribute('aria-valuetext')) || '';\n  }\n\n  /** Whether the thumb is disabled. */\n  async isDisabled(): Promise<boolean> {\n    return (await this.host()).getProperty<boolean>('disabled');\n  }\n\n  /** Gets the name of the thumb. */\n  async getName(): Promise<string> {\n    return await (await this.host()).getProperty<string>('name');\n  }\n\n  /** Gets the id of the thumb. */\n  async getId(): Promise<string> {\n    return await (await this.host()).getProperty<string>('id');\n  }\n\n  /**\n   * Focuses the thumb and returns a promise that indicates when the\n   * action is complete.\n   */\n  async focus(): Promise<void> {\n    return (await this.host()).focus();\n  }\n\n  /**\n   * Blurs the thumb and returns a promise that indicates when the\n   * action is complete.\n   */\n  async blur(): Promise<void> {\n    return (await this.host()).blur();\n  }\n\n  /** Whether the thumb is focused. */\n  async isFocused(): Promise<boolean> {\n    return (await this.host()).isFocused();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {coerceNumberProperty} from '@angular/cdk/coercion';\nimport {SliderHarnessFilters, ThumbPosition} from './slider-harness-filters';\nimport {MatSliderThumbHarness} from './slider-thumb-harness';\n\n/** Harness for interacting with a MDC mat-slider in tests. */\nexport class MatSliderHarness extends ComponentHarness {\n  static hostSelector = '.mat-mdc-slider';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a slider with specific attributes.\n   * @param options Options for filtering which input instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatSliderHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: SliderHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options)\n      .addOption('isRange', options.isRange, async (harness, value) => {\n        return (await harness.isRange()) === value;\n      })\n      .addOption('disabled', options.disabled, async (harness, disabled) => {\n        return (await harness.isDisabled()) === disabled;\n      });\n  }\n\n  /** Gets the start thumb of the slider (only applicable for range sliders). */\n  async getStartThumb(): Promise<MatSliderThumbHarness> {\n    if (!(await this.isRange())) {\n      throw Error(\n        '`getStartThumb` is only applicable for range sliders. ' +\n          'Did you mean to use `getEndThumb`?',\n      );\n    }\n    return this.locatorFor(MatSliderThumbHarness.with({position: ThumbPosition.START}))();\n  }\n\n  /** Gets the thumb (for single point sliders), or the end thumb (for range sliders). */\n  async getEndThumb(): Promise<MatSliderThumbHarness> {\n    return this.locatorFor(MatSliderThumbHarness.with({position: ThumbPosition.END}))();\n  }\n\n  /** Gets whether the slider is a range slider. */\n  async isRange(): Promise<boolean> {\n    return await (await this.host()).hasClass('mdc-slider--range');\n  }\n\n  /** Gets whether the slider is disabled. */\n  async isDisabled(): Promise<boolean> {\n    return await (await this.host()).hasClass('mdc-slider--disabled');\n  }\n\n  /** Gets the value step increments of the slider. */\n  async getStep(): Promise<number> {\n    // The same step value is forwarded to both thumbs.\n    const startHost = await (await this.getEndThumb()).host();\n    return coerceNumberProperty(await startHost.getProperty<string>('step'));\n  }\n\n  /** Gets the maximum value of the slider. */\n  async getMaxValue(): Promise<number> {\n    return (await this.getEndThumb()).getMaxValue();\n  }\n\n  /** Gets the minimum value of the slider. */\n  async getMinValue(): Promise<number> {\n    const startThumb = (await this.isRange())\n      ? await this.getStartThumb()\n      : await this.getEndThumb();\n    return startThumb.getMinValue();\n  }\n}\n"], "names": [], "mappings": ";;;AASA;IACY;AAAZ,CAAA,UAAY,aAAa,EAAA;AACvB,IAAA,aAAA,CAAA,aAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK;AACL,IAAA,aAAA,CAAA,aAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAG;AACL,CAAC,EAHW,aAAa,KAAb,aAAa,GAGxB,EAAA,CAAA,CAAA;;ACID;AACM,MAAO,qBAAsB,SAAQ,gBAAgB,CAAA;AACzD,IAAA,OAAO,YAAY,GACjB,6EAA6E;AAE/E;;;;AAIG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAqC,EAAE,EAAA;QAEvC,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,SAAS,CAClD,UAAU,EACV,OAAO,CAAC,QAAQ,EAChB,OAAO,OAAO,EAAE,KAAK,KAAI;YACvB,OAAO,CAAC,MAAM,OAAO,CAAC,WAAW,EAAE,MAAM,KAAK;AAChD,SAAC,CACF;;;AAIH,IAAA,MAAM,WAAW,GAAA;;AAEf,QAAA,MAAM,OAAO,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,qBAAqB,CAAC,KAAK,IAAI;AACvF,QAAA,OAAO,OAAO,GAAG,aAAa,CAAC,KAAK,GAAG,aAAa,CAAC,GAAG;;;AAI1D,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,eAAe,CAAC;;;IAIvE,MAAM,QAAQ,CAAC,QAAgB,EAAA;AAC7B,QAAA,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;;;QAI/B,MAAM,KAAK,CAAC,aAAa,CAAC,QAAQ,GAAG,EAAE,CAAC;AACxC,QAAA,MAAM,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC;AAClC,QAAA,MAAM,KAAK,CAAC,aAAa,CAAC,QAAQ,CAAC;;;AAIrC,IAAA,MAAM,aAAa,GAAA;AACjB,QAAA,MAAM,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,MAAM,QAAQ,CAAC,MAAM;YAC7C,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,EAAE;AACnB,SAAA,CAAC;QAEF,OAAO,CAAC,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC;;;AAIpC,IAAA,MAAM,WAAW,GAAA;AACf,QAAA,OAAO,oBAAoB,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,KAAK,CAAC,CAAC;;;AAInF,IAAA,MAAM,WAAW,GAAA;AACf,QAAA,OAAO,oBAAoB,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,KAAK,CAAC,CAAC;;;AAInF,IAAA,MAAM,eAAe,GAAA;AACnB,QAAA,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,gBAAgB,CAAC,KAAK,EAAE;;;AAIzE,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAU,UAAU,CAAC;;;AAI7D,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,MAAM,CAAC;;;AAI9D,IAAA,MAAM,KAAK,GAAA;AACT,QAAA,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,IAAI,CAAC;;AAG5D;;;AAGG;AACH,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE;;AAGpC;;;AAGG;AACH,IAAA,MAAM,IAAI,GAAA;QACR,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE;;;AAInC,IAAA,MAAM,SAAS,GAAA;QACb,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE;;;;ACzG1C;AACM,MAAO,gBAAiB,SAAQ,gBAAgB,CAAA;AACpD,IAAA,OAAO,YAAY,GAAG,iBAAiB;AAEvC;;;;AAIG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAgC,EAAE,EAAA;AAElC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO;AACtC,aAAA,SAAS,CAAC,SAAS,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,OAAO,EAAE,KAAK,KAAI;YAC9D,OAAO,CAAC,MAAM,OAAO,CAAC,OAAO,EAAE,MAAM,KAAK;AAC5C,SAAC;AACA,aAAA,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,OAAO,EAAE,QAAQ,KAAI;YACnE,OAAO,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,QAAQ;AAClD,SAAC,CAAC;;;AAIN,IAAA,MAAM,aAAa,GAAA;QACjB,IAAI,EAAE,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE;YAC3B,MAAM,KAAK,CACT,wDAAwD;AACtD,gBAAA,oCAAoC,CACvC;;AAEH,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAC,QAAQ,EAAE,aAAa,CAAC,KAAK,EAAC,CAAC,CAAC,EAAE;;;AAIvF,IAAA,MAAM,WAAW,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAC,QAAQ,EAAE,aAAa,CAAC,GAAG,EAAC,CAAC,CAAC,EAAE;;;AAIrF,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,mBAAmB,CAAC;;;AAIhE,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,sBAAsB,CAAC;;;AAInE,IAAA,MAAM,OAAO,GAAA;;AAEX,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,EAAE;QACzD,OAAO,oBAAoB,CAAC,MAAM,SAAS,CAAC,WAAW,CAAS,MAAM,CAAC,CAAC;;;AAI1E,IAAA,MAAM,WAAW,GAAA;QACf,OAAO,CAAC,MAAM,IAAI,CAAC,WAAW,EAAE,EAAE,WAAW,EAAE;;;AAIjD,IAAA,MAAM,WAAW,GAAA;QACf,MAAM,UAAU,GAAG,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE;AACtC,cAAE,MAAM,IAAI,CAAC,aAAa;AAC1B,cAAE,MAAM,IAAI,CAAC,WAAW,EAAE;AAC5B,QAAA,OAAO,UAAU,CAAC,WAAW,EAAE;;;;;;"}